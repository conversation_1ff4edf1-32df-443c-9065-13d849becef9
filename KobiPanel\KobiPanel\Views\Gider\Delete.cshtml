﻿@model KobiPanel.Models.Giderler

@{
    ViewBag.Title = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2><PERSON><PERSON> <PERSON><PERSON><PERSON></h2>

<h3><PERSON><PERSON><PERSON> istediğine emin misin?</h3>
<div>
    <h4><PERSON><PERSON><PERSON></h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.Konu)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Konu)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Tutar)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Tutar)
        </dd>

    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Delete" class="btn btn-default" /> |
            @Html.ActionLink("<PERSON><PERSON>", "Index")
        </div>
    }
</div>
