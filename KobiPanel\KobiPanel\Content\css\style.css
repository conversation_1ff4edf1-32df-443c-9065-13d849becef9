﻿.intro-2

{
    background: url("/Content/img/bg.jpg")no-repeat center center;
    background-size: cover;
}

.top-nav-collapse {
    background-color: #3f51b5 !important;
}

.navbar:not(.top-nav-collapse) {
    background: transparent !important;
}

@media (max-width: 768px) {
    .navbar:not(.top-nav-collapse) {
        background: #3f51b5 !important;
    }
}

.card {
    background-color: rgba(229, 228, 255, 0.2);
}

.md-form label {
    color: #ffffff;
}

h6 {
    line-height: 1.7;
}

html,
body,
header,
.view {
    height: 100%;
}

@media (min-width: 560px) and (max-width: 740px) {
    html,
    body,
    header,
    .view {
        height: 650px;
    }
}

@media (min-width: 800px) and (max-width: 850px) {
    html,
    body,
    header,
    .view {
        height: 650px;
    }
}

.card {
    margin-top: 30px;
    /*margin-bottom: -45px;*/
}

.md-form input[type=text]:focus:not([readonly]),
.md-form input[type=password]:focus:not([readonly]) {
    border-bottom: 1px solid #8EDEF8;
    box-shadow: 0 1px 0 0 #8EDEF8;
}

    .md-form input[type=text]:focus:not([readonly]) + label,
    .md-form input[type=password]:focus:not([readonly]) + label {
        color: #8EDEF8;
    }

.md-form .form-control {
    color: #fff;
}

.navbar.navbar-dark form .md-form input:focus:not([readonly]) {
    border-color: #8EDEF8;
}

@media (min-width: 800px) and (max-width: 850px) {
    .navbar:not(.top-nav-collapse) {
        background: #3f51b5 !important;
    }
}

