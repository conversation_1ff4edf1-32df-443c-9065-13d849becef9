﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace KobiPanel.Models
{
    [Table("Urun")]
    public class Urunler
    {
        [Key,DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        [DisplayName("Ürün Adı")]
        public string UrunAdi { get; set; }


        [DisplayName("Adet")]
        public int Adet { get; set; }

        [DisplayName("Alış Fiyatı")]
        public decimal AlisFiyat { get; set; }

        [DisplayName("KDV")]
        public double Kdv { get; set; }

        [DisplayName("Satış Fiyatı")]
        public decimal SatisFiyat { get; set; }
        [DisplayName("Eklenme Tarihi")]
        public Nullable<System.DateTime> EklenmeTarihi { get; set; }

        public Nullable<System.DateTime> DuzenlenmeTarihi { get; set; }




        //[System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        //public virtual ICollection<Satis> Satis { get; set; }
    }
}
