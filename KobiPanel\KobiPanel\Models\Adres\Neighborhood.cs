//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("Neighborhood")]
    public class Neighborhood
    {
        
        public Neighborhood()
        {
            this.Musteriler = new HashSet<Musteriler>();
        }
        [Key]
        public int NeighborhoodID { get; set; }
        public int DistrictID { get; set; }
        public string NeighborhoodName { get; }
        public string ZipCode { get; }
    
        public virtual District District { get; set; }
        
        public virtual ICollection<Musteriler> Musteriler { get; set; }
    }
}
