﻿@model KobiPanel.Models.Satislar

@{
    ViewBag.Title = "Delete";
}

<h2>Delete</h2>

<h3>Bu satışı silmek istediğinize emin misiniz?</h3>
<div>
    <h4>Satislar</h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.Tutar)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Tutar)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ServisMi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ServisMi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SatisTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SatisTarihi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.TeslimEdildiMi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.TeslimEdildiMi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Aciklama)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Aciklama)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Musteriler.ad)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Musteriler.ad)
        </dd>

    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Delete" class="btn btn-default" /> |
            @Html.ActionLink("Listeye Geri Dön", "Index")
        </div>
    }
</div>
