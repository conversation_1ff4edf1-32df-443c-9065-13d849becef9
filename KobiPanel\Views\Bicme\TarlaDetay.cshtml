﻿@model KobiPanel.Models.Bicme.BicilenTarlalar

@{
    ViewBag.PageHeader = "Tarla Detayı";
    ViewBag.Title = "Tarla Detayı";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<div>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.Musteri.adsoyad)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Musteri.adsoyad)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Donum)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Donum)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.BicmeFiyati)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.BicmeFiyati)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ToplamTutar)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ToplamTutar)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.TahsilatTutari)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.TahsilatTutari)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.KalanTutar)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.KalanTutar)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.BicimTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.BicimTarihi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.aciklama)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.aciklama)
        </dd>

    </dl>
</div>
<p>
    @Html.ActionLink("Edit", "Edit", new { id = Model.TarlaID }) |
    @Html.ActionLink("Back to List", "Index")
</p>
