﻿@model KobiPanel.Models.Satislar

@{
    ViewBag.Title = "Details";
}

<h2>Details</h2>

<div>
    <h4>Satislar</h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.Tutar)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Tutar)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ServisMi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ServisMi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SatisTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SatisTarihi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.TeslimEdildiMi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.TeslimEdildiMi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Aciklama)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Aciklama)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Musteriler.adsoyad)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Musteriler.adsoyad)
        </dd>

    </dl>
</div>
<p>
    @Html.ActionLink("Edit", "Edit", new { id = Model.SatisID }) |
    @Html.ActionLink("Listeye Geri Dön", "Index")
</p>
