namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Data.Entity.Spatial;

    [Table("Araclar")]
    public class Araclar
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        [Required]
        [StringLength(9)]
        public string Plaka { get; set; }

        public DateTime VizeTarihi { get; set; }

        public DateTime SigortaTarihi { get; set; }
    }
}
