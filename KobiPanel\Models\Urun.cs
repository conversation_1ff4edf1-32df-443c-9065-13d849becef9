﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace KobiPanel.Models
{    
    [Table("Urun")]
    public class Urun
    {
        [Key,DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int UrunID { get; set; }

        [DisplayName("Ürün Adı"), Required]
        public string Urun<PERSON>di { get; set; }

        [DisplayName("Ürün Fiyatı"),Required]
        public decimal UrunFiyati { get; set; }

        [DisplayName("Mevcut Stok"), Required]
        public int MevcutStok { get; set; }

        [DisplayName("Nakliye Ücreti"), Required]
        public int NakliyeUcreti { get; set; }

        [DisplayName("Eklenme Tarihi"), Required]
        public DateTime EklenmeTarihi { get; set; }

        [DisplayName("D<PERSON>zenlenme Tarihi"), Required]
        public DateTime DuzenlenmeTarihi { get; set; }


    }
}