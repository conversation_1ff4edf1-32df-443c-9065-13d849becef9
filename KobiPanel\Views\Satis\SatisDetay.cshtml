﻿@model KobiPanel.Models.Satislar

@{
    ViewBag.Title = "Satış Detayları";
}

<div>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.Musteriler.adsoyad)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Musteriler.adsoyad)
        </dd>
        <dt>
            @Html.DisplayNameFor(model => model.Tutar)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Tutar)
        </dd>

        <dt>
            @Html.DisplayNameFor(model=>model.NakliyeBedeli)
        </dt>

        <dd>
            @Html.DisplayFor(model=>model.NakliyeBedeli)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ServisMi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ServisMi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SatisTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SatisTarihi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.TeslimEdildiMi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.TeslimEdildiMi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Aciklama)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Aciklama)
        </dd>


    </dl>
</div>
<p>
    @Html.ActionLink("Düzenle", "SatisDuzenle", new { id = Model.SatisID }) |
    @Html.ActionLink("Listeye Geri Dön", "SatisAnasayfa",null, new {@class = "btn btn-primary" })
</p>
