﻿@model KobiPanel.Models.Bicme.BicilenTarlalar

@{
    ViewBag.Pageheader = "Tarla <PERSON>";
    ViewBag.Title = "Create";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()

    <div class="form-horizontal">
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div>
            <label class="mdb-main-label" style="color:red">Tarla Sahibini Düzenlemeniz Önerilmez</label>
            @Html.DropDownListFor(m => m.MusteriID, (SelectList)ViewBag.SListMusteriler, "Lütfen Bir Müşteri Seçiniz", new { @class = "mdb-select md-form", searchable = "Müşteri Ara", aria_invalid = "false", data_live_search = "true", lang = "tr" })
            <button type="button" onclick="window.location='@Url.Action("MusteriEkle","Musteri")'" class="btn-save btn btn-primary btn-sm">Yeni Müşteri Ekle</button>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="md-form">
                    @Html.EditorFor(model => model.Donum, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.LabelFor(model => model.Donum, htmlAttributes: new { @class = "active" })
                    @Html.ValidationMessageFor(model => model.Donum, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="md-form">
                    @Html.EditorFor(model => model.BicmeFiyati, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.LabelFor(model => model.BicmeFiyati, htmlAttributes: new { @class = "active" })
                    @Html.ValidationMessageFor(model => model.BicmeFiyati, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="md-form">
                    @Html.EditorFor(model => model.TahsilatTutari, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.LabelFor(model => model.TahsilatTutari, htmlAttributes: new { @class = "active" })
                    @Html.ValidationMessageFor(model => model.TahsilatTutari, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="md-form">
                    @Html.EditorFor(model => model.BicimTarihi, new { htmlAttributes = new { @class = "form-control", type = "date" } })
                    @Html.LabelFor(model => model.BicimTarihi, htmlAttributes: new { @class = "active" })
                    @Html.ValidationMessageFor(model => model.BicimTarihi, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Kaydet" class="btn btn-outline-success waves-effect" />
            </div>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Tarla Listesine Dön", "TarlaAnasayfa")
</div>
