﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using System.Web.Script.Serialization;
using KobiPanel.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace KobiPanel.Controllers
{
    public class MusteriController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();

        
        public ActionResult Index()
        {
            var musteriler = db.Musteriler.Include(m => m.City).Include(m => m.District).Include(m => m.Neighborhood).Include(m => m.Town);
            return View(musteriler.ToList());
        }
        
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Musteriler musteriler = db.Musteriler.Find(id);
            if (musteriler == null)
            {
                return HttpNotFound();
            }
            return View(musteriler);
        }

        // GET: Musteri/Create
        public ActionResult Create()
        {
            
            ViewBag.CityID = new SelectList(db.City.ToList(), "CityID", "CityName");
            List<string> d = new List<string>();
            d.Add("Seçim Yapınız");
            ViewBag.DistricID = new SelectList(d);
            ViewBag.NeighborhoodID = new SelectList(d);
            ViewBag.TownID = new SelectList(d);
            return View();
        }

        
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind(Include = "ID,adsoyad,tel,yas,CityID,TownID,DistricID,NeighborhoodID,KucukBasHayvanSayisi,BuyukBasHayvanSayisi")] Musteriler musteriler)
        {

            if (ModelState.IsValid)
            {
                db.Musteriler.Add(musteriler);
                db.SaveChanges();
                return RedirectToAction("Index");
            }
            List<string> d = new List<string>();
            d.Add("Seçim Yapınız");

            if (musteriler.CityID > 0)
            {
                ViewBag.CityID = new SelectList(db.City, "CityID", "CityName", musteriler.CityID);
            }
            else
            {
                ViewBag.CityID = new SelectList(db.City, "CityID", "CityName");
            }
            if (musteriler.DistricID > 0)
            {
                ViewBag.DistricID = new SelectList(db.District, "DistrictID", "DistrictName", musteriler.DistricID);
            }
            else
            {
                ViewBag.DistricID = new SelectList(d);

            }
            if (musteriler.NeighborhoodID > 0)
            {
                ViewBag.NeighborhoodID = new SelectList(db.Neighborhood, "NeighborhoodID", "NeighborhoodName", musteriler.NeighborhoodID);

            }
            else
            {
                ViewBag.NeighborhoodID = new SelectList(d);
            }
            if (musteriler.TownID > 0)
            {

                ViewBag.TownID = new SelectList(db.Town, "TownID", "TownName", musteriler.TownID);
            }
            else
            {
                ViewBag.TownID = new SelectList(d);
            }



            return View(musteriler);
        }
        
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Musteriler musteriler = db.Musteriler.Find(id);
            if (musteriler == null)
            {
                return HttpNotFound();
            }
            ViewBag.CityID = new SelectList(db.City, "CityID", "CityName", musteriler.CityID);
            ViewBag.DistricID = new SelectList(db.District, "DistrictID", "DistrictName", musteriler.DistricID);
            ViewBag.NeighborhoodID = new SelectList(db.Neighborhood, "NeighborhoodID", "NeighborhoodName", musteriler.NeighborhoodID);
            ViewBag.TownID = new SelectList(db.Town, "TownID", "TownName", musteriler.TownID);
            return View(musteriler);
        }

        
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind(Include = "ID,ad,soyad,tel,yas,CityID,TownID,DistricID,NeighborhoodID,KucukBasHayvanSayisi,BuyukBasHayvanSayisi")] Musteriler musteriler)
        {
            if (ModelState.IsValid)
            {
                db.Entry(musteriler).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("Index");
            }
            ViewBag.CityID = new SelectList(db.City, "CityID", "CityName", musteriler.CityID);
            ViewBag.DistricID = new SelectList(db.District, "DistrictID", "DistrictName", musteriler.DistricID);
            ViewBag.NeighborhoodID = new SelectList(db.Neighborhood, "NeighborhoodID", "NeighborhoodName", musteriler.NeighborhoodID);
            ViewBag.TownID = new SelectList(db.Town, "TownID", "TownName", musteriler.TownID);
            return View(musteriler);
        }

        
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Musteriler musteriler = db.Musteriler.Find(id);
            if (musteriler == null)
            {
                return HttpNotFound();
            }
            return View(musteriler);
        }

        
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            Musteriler musteriler = db.Musteriler.Find(id);
            db.Musteriler.Remove(musteriler);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }



        public JsonResult GetTowns(int Id)
        {
            var List = db.Town.Where(m => m.CityID == Id).Select(x => new { x.TownID, x.TownName }).ToList();

            return Json(List, JsonRequestBehavior.AllowGet);
        }


        public JsonResult GetDistrict(int Id)
        {
            var List = db.District.Where(m => m.TownID == Id).Select(x => new { x.DistrictID, x.DistrictName }).ToList();

            return Json(List, JsonRequestBehavior.AllowGet);
        }
        public JsonResult GetNeighborhood(int Id)
        {
            var List = db.Neighborhood.Where(m => m.DistrictID == Id).Select(x => new { x.NeighborhoodID, x.NeighborhoodName }).ToList();

            return Json(List, JsonRequestBehavior.AllowGet);
        }

        #region SemtListGetir Tam çalışmıyor.

        //[HttpPost]
        //[AllowAnonymous]
        //public JsonResult semtList(int id)
        //{
        //    List<District> Semtler = db.District.Where(m => m.TownID == id).ToList();

        //    foreach (var item in Semtler)
        //    {
        //        List<SelectListItem> listItems = (from semt in db.Neighborhood.Where(m => m.DistrictID == m.DistrictID).ToList()
        //                                          select new SelectListItem()
        //                                          {
        //                                              Text = semt.NeighborhoodName,
        //                                              Value = semt.NeighborhoodID.ToString()
        //                                          }
        //                                          ).ToList();
        //        Mahalleler.AddRange(listItems);
        //    }

        //    ViewBag.MahalleListesi = Mahalleler;
        //    return Json(ViewBag.MahalleListesi, JsonRequestBehavior.AllowGet);

        //}

        #endregion
    }
}
