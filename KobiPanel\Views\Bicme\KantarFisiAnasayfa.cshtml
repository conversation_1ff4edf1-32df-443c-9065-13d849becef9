﻿@using GridMvc.Html
@model IEnumerable<KobiPanel.Models.Bicme.KantarFisi>

@{
    ViewBag.PageHeader = "Kantar Fişi Listesi";
    ViewBag.Title = "Kantar Fişi Listesi";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<link href="~/Content/Gridmvc.css" rel="stylesheet" />

<link href="~/Content/gridmvc.datepicker.min.css" rel="stylesheet" />
<link href="~/Content/PagedList.css" rel="stylesheet" />

<div>
    @Html.Grid(Model).Columns(col =>
{
    col.Add(x => x.FisID).Titled("Tartım Numarası").Sortable(true);
    col.Add(x => x.BicilenTarlalar.Musteri.adsoyad).RenderValueAs(x => x.BicilenTarlalar.Musteri.adsoyad + "(" + x.BicilenTarlalar.Musteri.ID + ")").Titled("Tarla Sahibi").Filterable(true).Sortable(true);
    col.Add(x => x.TarlaID).Titled("Tarla Numarası").Sortable(true).Filterable(true);
    col.Add(x => x.Plaka).Titled("Araç Plakası").Sortable(true).Filterable(true);
    col.Add(x => x.SoforAdi).Titled("Şoför Adı").Sortable(true).Filterable(true);
    col.Add(x => x.birinciTartim).Titled("1. Tartım").Sortable(true).Filterable(true);
    col.Add(x => x.ikinciTartim).Titled("2.Tartım").Sortable(true).Filterable(true);
    col.Add(x => x.NetKg).Titled("Net Kg").Sortable(true).Filterable(true);

    col.Add(x => x.aciklama).Titled("Açıklama").Sortable(true);


}).WithPaging(12).SetLanguage("tr")
</div>