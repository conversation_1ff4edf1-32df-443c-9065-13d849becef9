﻿@model IEnumerable<KobiPanel.Models.Musteriler>

@{
    /**/

    ViewBag.Title = "Index";
}

<h2 class="text-lg-center">Müşteri Listesi</h2>

<p>
    @Html.ActionLink("Ye<PERSON> Müşteri Ekle", "Create", null, new { @class = "btn btn-primary" })
</p>

<!--//TODO : Tablo geliştirilecek. Türkçeleştirilecek-->
<table id="example" class="table table-striped table-bordered dt-responsive" style="width:100%">
    <thead>
        <tr>
            <th>
                Ad Soyad
            </th>
            <th>
                Telefon
            </th>
            <th>
                İlçe
            </th>
            <th>
                Belde
            </th>
            <th>
                Mahalle
            </th>
            <th>
                İşlem
            </th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.adsoyad)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.tel)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Town.TownName)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.District.DistrictName)
                </td>

                <td>
                    @Html.DisplayFor(modelItem => item.Neighborhood.NeighborhoodName)
                </td>
                <td>
                    <i class="far fa-edit" style="color:#000000">
                        @Html.ActionLink("Düzenle", "Edit", new { id = item.ID }, new { @class = "text-dark" })
                    </i>
                    @Html.ActionLink("Detay", "Details", new { id = item.ID }, new { @class = "text-dark" })
                    <i class="fal fa-trash-alt" style="color:red">
                        @Html.ActionLink("Sil", "Delete", new { id = item.ID }, new { @class = "text-danger" })
                    </i>
                </td>
            </tr>
        }

    </tbody>
</table>


<script>
    $(document).ready(function () {
        $('#example').DataTable();
    });
</script>




@*<div class="table-responsive">
        <table id="MusteriTablosu" class="table table-hover mb-0">
            <tr>
                <th class="th-lg">
                    Ad Soyad
                </th>
                <th class="th-lg">
                    Telefon
                </th>
                <th class="th-lg">
                    İlçe
                </th>
                <th class="th-lg">
                    Belde
                </th>
                <th class="th-lg">
                    Mahalle
                </th>
                <th class="th-lg">
                    İşlem
                </th>
            </tr>



            @foreach (var item in Model)
            {
                <tr>
                    <td>
                        @Html.DisplayFor(modelItem => item.adsoyad)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.tel)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Town.TownName)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.District.DistrictName)
                    </td>

                    <td>
                        @Html.DisplayFor(modelItem => item.Neighborhood.NeighborhoodName)
                    </td>
                    <td>
                        <i class="far fa-edit" style="color:#000000">
                            @Html.ActionLink("Düzenle", "Edit", new { id = item.ID }, new { @class = "text-dark" })
                        </i>
                        @Html.ActionLink("Detay", "Details", new { id = item.ID }, new { @class = "text-dark" })
                        <i class="fal fa-trash-alt" style="color:red">
                            @Html.ActionLink("Sil", "Delete", new { id = item.ID }, new { @class = "text-danger" })
                        </i>
                    </td>
                </tr>

            }

        </table>
    </div>*@

@*<input type="text" onkeyup="TableSearch(this)" placeholder="Arama için yazınız" />
    <script>
        function TableSearch(input) {
            // Declare variables
            var table = document.getElementById('MusteriTablosu');
            var input, filter, table, tr, td, i, txtValue;
            filter = input.value.toUpperCase();
            tr = table.getElementsByTagName("tr");

            // Loop through all table rows, and hide those who don't match the search query
            for (i = 0; i < tr.length; i++) {
                td = tr[i].getElementsByTagName("td")[0];
                if (td) {
                    txtValue = td.textContent || td.innerText;
                    if (txtValue.toUpperCase().indexOf(filter) > -1) {
                        tr[i].style.display = "";
                    } else {
                        tr[i].style.display = "none";
                    }
                }
            }
        }
    </script>*@


<link href="~/Content/datatable/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="~/Content/datatable/responsive.bootstrap4.min.css" rel="stylesheet" />
<script src="~/Content/datatable/dataTables.bootstrap4.min.js"></script>
<script src="~/Content/datatable/dataTables.responsive.min.js"></script>
<script src="~/Content/datatable/jquery.dataTables.min.js"></script>
<script src="~/Content/datatable/responsive.bootstrap4.min.js"></script>