﻿@model KobiPanel.Models.Satislar

@{
    ViewBag.Title = "Satış Silme";
    ViewBag.PageHeader = "Satış Silme";
}

<h3>Bu satışı silmek istediğinize emin misiniz?</h3>
<div>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.Musteriler.adsoyad)
        </dt>
        <dd>
            @Html.DisplayFor(model => model.Musteriler.adsoyad)
        </dd>
        <dt>
            @Html.DisplayNameFor(model => model.Tutar)
        </dt>
        <dd>
            @Html.DisplayFor(model => model.Tutar)
        </dd>
        <dt>
            @Html.DisplayNameFor(model => model.ServisMi)
        </dt>
        <dd>
            @(Model.ServisMi ? "Adrese Teslim" : "Yerinde Teslim")
        </dd>
        <dt>
            @Html.DisplayNameFor(model => model.SatisTarihi)
        </dt>
        <dd>
            @Html.DisplayFor(model => model.SatisTarihi)
        </dd>
        <dt>
            @Html.DisplayNameFor(model => model.TeslimEdildiMi)
        </dt>

        @if (Model.TeslimEdildiMi == true)
        {
            <dd style="color:green">
                Teslim Edildi
            </dd>
        }
        else
        {
            <dd style="color:red">
                Teslim EDİLMEDİ
            </dd>
        }


        <dt>
            @Html.DisplayNameFor(model => model.Aciklama)
        </dt>
        <dd>
            @Html.DisplayFor(model => model.Aciklama)
        </dd>
    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Sil" class="btn btn-outline-red waves-effect" /> |
            @Html.ActionLink("Listeye Geri Dön", "SatisAnasayfa",null, new {@class = "btn btn-primary" })
        </div>
    }
</div>
