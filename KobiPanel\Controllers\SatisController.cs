using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using KobiPanel.Models;
using KobiPanel.Models.ViewModels;
using PagedList;

namespace KobiPanel.Controllers
{
    public class SatisController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();

        public ActionResult SatisAnasayfaGrid()
        {
            var model = db.Satislar.ToList();



            return View(model);
        }
        public ActionResult SatisAnasayfa(ListeTuru? tip, string sortOrder, string currentFilter, string searchString, int? page)
        {

            List<SatislarListesiDTO> model = new List<SatislarListesiDTO>();
            if (string.IsNullOrEmpty(searchString))
            {
                switch (tip)
                {
                    case ListeTuru.TeslimEdilmeyen:
                        model = db.Satislar.
                       Select(m => new SatislarListesiDTO()
                       {
                           Musteri = m.<PERSON>,
                           SatisID = m.SatisID,
                           SatisTarihi = m.SatisTarihi,
                           ServisMi = m.ServisMi,
                           TeslimEdildiMi = m.TeslimEdildiMi,
                           Tutar = m.Tutar
                       }).
                           Where(m => m.TeslimEdildiMi == false).
                           OrderBy(m => m.SatisTarihi).ToList();
                        break;
                    case ListeTuru.TeslimEdilen:
                        model = db.Satislar.
                        Select(m => new SatislarListesiDTO()
                        {
                            Musteri = m.Musteriler,
                            SatisID = m.SatisID,
                            SatisTarihi = m.SatisTarihi,
                            ServisMi = m.ServisMi,
                            TeslimEdildiMi = m.TeslimEdildiMi,
                            Tutar = m.Tutar
                        }).
                            Where(m => m.TeslimEdildiMi == true).
                            OrderBy(m => m.SatisTarihi).ToList();
                        break;
                    default:
                        model = db.Satislar.
                        Select(m => new SatislarListesiDTO()
                        {
                            Musteri = m.Musteriler,
                            SatisID = m.SatisID,
                            SatisTarihi = m.SatisTarihi,
                            ServisMi = m.ServisMi,
                            TeslimEdildiMi = m.TeslimEdildiMi,
                            Tutar = m.Tutar
                        }).
                            OrderBy(m => m.SatisTarihi).ToList();
                        break;
                }
            }

            ViewBag.CurrentSort = sortOrder;
            ViewBag.NameSortParm = String.IsNullOrEmpty(sortOrder) ? "name_desc" : "";
            ViewBag.DateSortParm = sortOrder == "Date" ? "date_desc" : "Date";

            if (searchString != null)
            {
                page = 1;
            }
            else
            {
                searchString = currentFilter;
            }

            ViewBag.CurrentFilter = searchString;

            if (!String.IsNullOrEmpty(searchString))
            {

                model = db.Satislar.
                  Select(m => new SatislarListesiDTO()
                  {
                      Musteri = m.Musteriler,
                      SatisID = m.SatisID,
                      SatisTarihi = m.SatisTarihi,
                      ServisMi = m.ServisMi,
                      TeslimEdildiMi = m.TeslimEdildiMi,
                      Tutar = m.Tutar
                  }).
                      Where(m => m.Musteri.adsoyad.Contains(searchString)).
                      OrderBy(m => m.Musteri.adsoyad).ToList();
            }

            switch (sortOrder)
            {
                case "name_desc":
                   model=  model.OrderByDescending(s => s.Musteri.adsoyad).ToList();
                    break;
                case "Date":
                    model= model.OrderBy(s => s.SatisTarihi).ToList();
                    break;
                case "date_desc":
                   model=  model.OrderByDescending(s => s.SatisTarihi).ToList();
                    break;
                default:  // Name ascending
                    model= model.OrderBy(s => s.Musteri.kayitTarihi).ToList();
                    break;
            }

            int pageSize = 15;
            int pageNumber = (page ?? 1);
            return View(model.ToPagedList(pageNumber, pageSize));
        }


        public ActionResult SatisDetay(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Satislar satislar = db.Satislar.Find(id);
            if (satislar == null)
            {
                return HttpNotFound();
            }
            return View(satislar);
        }


        public ActionResult SatisEkle()
        {
            try
            {
                List<Musteriler> musteri = db.Musteriler.Include(m => m.City).Include(m => m.Town).Include(m => m.Neighborhood).ToList();

                foreach (var item in musteri)
                {
                    try
                    {
                        string cityName = item.City?.CityName ?? "Bilinmeyen İl";
                        string townName = item.Town?.TownName ?? "Bilinmeyen İlçe";
                        string neighborhoodName = item.Neighborhood?.NeighborhoodName ?? "Bilinmeyen Mahalle";

                        string adres = $"  {cityName} - {townName} - {neighborhoodName}";
                        item.adsoyad = (item.adsoyad ?? "Bilinmeyen Müşteri") + " | " + adres;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Müşteri adres oluşturma hatası: {ex.Message}");
                        item.adsoyad = item.adsoyad ?? "Bilinmeyen Müşteri";
                    }
                }

                ViewBag.SListMusteriler = new SelectList(musteri, "ID", "adsoyad");

                List<Urun> urun = db.Urun.ToList();

                foreach (var item in urun)
                {
                    try
                    {
                        string urunAdi = item.UrunAdi ?? "Bilinmeyen Ürün";
                        string fiyat = item.UrunFiyati.ToString("0.##");
                        string nakliye = item.NakliyeUcreti.ToString("0.##");

                        item.UrunAdi = $"{urunAdi} | Fiyatı: {fiyat} - Nakliye Ücreti: {nakliye} TL";
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Ürün bilgisi oluşturma hatası: {ex.Message}");
                        item.UrunAdi = item.UrunAdi ?? "Bilinmeyen Ürün";
                    }
                }

                ViewBag.SListUrunler = new SelectList(urun, "UrunID", "UrunAdi");

                // Boş model gönder ki View'de null reference hatası olmasın
                return View(new Satislar());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SatisEkle GET hatası: {ex.Message}");
                ViewBag.Error = "Sayfa yüklenirken bir hata oluştu.";
                return View(new Satislar());
            }
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult SatisEkle(Satislar model)
        {
            model.Musteriler = db.Musteriler.Find(model.MusteriID);
            model.Urun = db.Urun.Find(model.UrunID);

            if (model.ServisMi)
            {
                model.NakliyeBedeli = model.Urun.NakliyeUcreti * model.UrunAdeti;

                model.Tutar = ((model.Urun.NakliyeUcreti + model.Urun.UrunFiyati) * model.UrunAdeti) - model.indirim;

            }
            else
            {
                model.Tutar = (model.UrunAdeti * model.Urun.UrunFiyati) - model.indirim;
            }

            model.SatisTarihi = model.SatisTarihi.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));
            model.TeslimTarihi = model.SatisTarihi.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));
            ModelState.Remove("Tutar");
            ModelState.Remove("TeslimTarihi");

                db.Satislar.Add(model);
                int sonuc = db.SaveChanges();

                if (sonuc > 0)
                {
                    TempData["Success"] = "Satış bilgileri kayıt edilmiştir.";

                    var id = model.SatisID;
                    return Redirect("~/Satis/SatisOzeti/" + id);
                }
                else
                {
                    ViewBag.Error = "Bir hata oluştu.";
                    return View(model);
                }
        }



        public ActionResult SatisDuzenle(int id)
        {
            var model = db.Satislar.Find(id);
            model.Musteriler = db.Musteriler.Find(model.MusteriID);

            List<Urun> urun = db.Urun.ToList();

            ViewBag.SListUrunler = new SelectList(urun, "UrunID", "UrunAdi",model.UrunID);

            return View(model);
        }


        [HttpPost]
        public ActionResult SatisDuzenle(Satislar model)
        {
            model.Musteriler = db.Musteriler.Find(model.MusteriID);
            model.Urun = db.Urun.Find(model.UrunID);
            if (model.ServisMi)
            {


                model.NakliyeBedeli = model.Urun.NakliyeUcreti * model.UrunAdeti;

                model.Tutar = (model.Urun.NakliyeUcreti + model.Urun.UrunFiyati) * model.UrunAdeti - model.indirim;

            }
            else
            {
                model.Tutar = model.UrunAdeti * model.Urun.UrunFiyati - model.indirim;
            }

            if (model.TeslimEdildiMi==true)
            {
                model.TeslimTarihi = DateTime.Now;
            }


            db.Entry(model).State = EntityState.Modified;
            int sonuc = db.SaveChanges();
            if (sonuc>0)
            {
                TempData["DuzenleSuccess"] = "Satış Düzenleme İşlemi Başarılı.";
            return RedirectToAction("SatisAnasayfa");
            }
            else
            {
                TempData["DuzenleFailed"] = "Satış Düzenleme İşlemi Başarısız.";
            }


            return View(model);
        }

        public ActionResult SatisOzeti(int? id)
        {
            var model = db.Satislar.Find(id);
            return View(model);
        }



        public ActionResult SatisSil(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Satislar satislar = db.Satislar.Find(id);
            if (satislar == null)
            {
                return HttpNotFound();
            }
            return View(satislar);
        }


        [HttpPost, ActionName("SatisSil")]
        [ValidateAntiForgeryToken]
        public ActionResult SatisSilOnayli(int id)
        {
            Satislar satislar = db.Satislar.Find(id);
            db.Satislar.Remove(satislar);

            int sonuc = db.SaveChanges();

            if (sonuc>0)
            {
                TempData["SilindiSuccess"] = "Satış kaydı başarıyla silindi.";
            }

            return RedirectToAction("SatisAnasayfa");
        }

        public ActionResult TeslimEdilmeyenSatis()
        {
            SatisViewModel model = new SatisViewModel();
            model.Satislar = db.Satislar.Where(m => m.TeslimEdildiMi == false);
            model.Musteriler = db.Musteriler.ToList();


            return View(model);
        }

        public ActionResult TeslimEt(int id)
        {
            if (id > 0)
            {
                var model = db.Satislar.Find(id);
                model.TeslimEdildiMi = true;
                model.TeslimTarihi = DateTime.Now;
                int sonuc = db.SaveChanges();
                if (sonuc > 0)
                {
                    TempData["TeslimSuccess"] = "Satış teslim edildi olarak başarıyla işaretlendi.";
                    return RedirectToAction("SatisAnasayfa");
                }
                return View("SatisAnasayfa");

            }

            return View();
        }

        public ActionResult SatisEkleDeneme()
        {


            List<Musteriler> musteri = db.Musteriler.ToList();



            ViewBag.SListMusteriler = new SelectList(musteri, "ID", "adsoyad");

            List<Urun> urun = db.Urun.ToList();

            ViewBag.SListUrunler = new SelectList(urun, "UrunID", "UrunAdi");
            return View();
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult SatisEkleDeneme(Satislar model)
        {
            model.Musteriler = db.Musteriler.Find(model.MusteriID);
            model.Urun = db.Urun.Find(model.UrunID);

            if (model.ServisMi)
            {
                model.NakliyeBedeli = model.Urun.NakliyeUcreti * model.UrunAdeti;

                model.Tutar = ((model.Urun.NakliyeUcreti + model.Urun.UrunFiyati) * model.UrunAdeti) - model.indirim;

            }
            else
            {
                model.Tutar = (model.UrunAdeti * model.Urun.UrunFiyati) - model.indirim;
            }

            model.SatisTarihi = model.SatisTarihi.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));
            model.TeslimTarihi = model.SatisTarihi.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));
            ModelState.Remove("Tutar");
            ModelState.Remove("TeslimTarihi");

            db.Satislar.Add(model);
            int sonuc = db.SaveChanges();

            if (sonuc > 0)
            {
                TempData["Success"] = "Satış bilgileri kayıt edilmiştir.";

                var id = model.SatisID;
                return Redirect("~/Satis/SatisOzeti/" + id);
            }
            else
            {
                ViewBag.Error = "Bir hata oluştu.";
                return View(model);
            }
        }




        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }

        public enum ListeTuru
        {
            TeslimEdilmeyen,
            TeslimEdilen
        }
    }



}
