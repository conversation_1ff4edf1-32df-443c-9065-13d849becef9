// Miscellenous
// Advanced tables
.table {
  fieldset {
    &.form-check {
      margin-bottom: 0;
    }
    label {
      &.form-check-label {
        height: $advanced-table-fieldset-label-height;
      }
    }
  }
}

// Product table
.product-table {
  img {
    max-height: $product-table-img-max-height;
    min-width: $product-table-img-min-width;
  }
  td {
    vertical-align: middle;
  }
}

.streak {
  display: block;
  position: relative;
  overflow: hidden;
  height: 250px;
  &.streak-md {
    height: 400px;
    @media (max-width: 736px) {
      height: 300px;
    }
  }
  &.streak-lg {
    height: 650px;
    @media (max-width: 450px) {
      height: 900px;
    }
  }
  &.streak-long {
    height: 200px;
    @media (max-width: 450px) {
      height: 620px;
    }
  }
  &.streak-long-2 {
    height: 400px;
    @media (max-width: 450px) {
      height: 700px;
    }
  }
  &.streak-photo {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    @media (min-width: 1366px) {
      background-attachment: fixed;
    }
  }
  &.no-flex {
    padding-top: 3.125rem;
    padding-bottom: 3.125rem;
    height: auto;
  }
}

.collapse-content {
  table,
  p {
    &.collapse {
      &:not(.show) {
        height: 2.65rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    &.collapsing {
      min-height: 2.65rem;
    }
  }
  a {
    &.collapsed {
      &:after {
        content: 'Read More';
      }
    }
    &:not(.collapsed) {
      &:after {
        content: 'Read Less';
      }
    }
  }
}
