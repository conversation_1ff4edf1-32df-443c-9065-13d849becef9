.timeline-main .stepper {
  &.timeline-images li {
    a {
      padding: 0px;
      left: 50%;

      .circle {
        margin-top: 0.9rem;
        width: 23px;
        height: 23px;
        line-height: 23px;
        font-size: 1em;
        text-align: center;
        position: absolute;
        top: 16px;
        margin-left: -12px;
        background-color: #ccc;
        z-index: 2;
      }
    }

    .step-content {
      width: 45%;
      float: left;
      border-radius: 2px;
      position: relative;

      &.hoverable {
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);

        &:hover {
          box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
        }
      }
    }

    &.timeline-inverted {
      align-items: flex-end;

      .step-content {
        float: right;
      }
    }
  }

  &.stepper-vertical.timeline-images li:not(:last-child):after,
  &.stepper-vertical.timeline-simple li:not(:last-child):after {
    content: " ";
    position: absolute;
    width: 2px;
    left: 50%;
    top: 54px;
    height: 100%;
    margin-left: -1.5px;
  }

  &.stepper-vertical.timeline-images li:not(:last-child):after {
    background-color: #26c6da;
  }

  &.stepper-vertical.timeline-simple li:not(:last-child):after {
    background-color: #9e9e9e;
  }


  &.timeline-simple li {
    a {
      padding: 0px;
      left: 50%;

      .circle {
        margin-top: 0.9rem;
        width: 23px;
        height: 23px;
        line-height: 23px;
        font-size: 1.4em;
        text-align: center;
        position: absolute;
        top: 16px;
        margin-left: -12px;
        background-color: #ccc;
        z-index: 2;
      }
    }

    .step-content {
      width: 45%;
      float: left;
      border-radius: 2px;
      position: relative;

      &:before {
        position: absolute;
        top: 26px;
        right: -15px;
        display: inline-block;
        border-top: 15px solid transparent;
        border-left: 15px solid #e0e0e0;
        border-right: 0 solid #e0e0e0;
        border-bottom: 15px solid transparent;
        content: " ";
      }

      &:after {
        position: absolute;
        top: 27px;
        right: -14px;
        display: inline-block;
        border-top: 14px solid transparent;
        border-left: 14px solid #fff;
        border-right: 0 solid #fff;
        border-bottom: 14px solid transparent;
        content: " ";
      }

      &.hoverable {
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);

        &:hover {
          box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
        }
      }
    }

    &.timeline-inverted {
      align-items: flex-end;

      .step-content {
        float: right;

        &:before {
          border-left-width: 0;
          border-right-width: 15px;
          left: -15px;
          right: auto;
        }

        &:after {
          border-left-width: 0;
          border-right-width: 14px;
          left: -14px;
          right: auto;
        }
      }
    }
  }

  &.timeline li {
    a {
      padding: 0px 24px;
      left: 50%;

      .circle {
        width: 50px;
        height: 50px;
        line-height: 50px;
        font-size: 1.4em;
        text-align: center;
        position: absolute;
        top: 16px;
        margin-left: -50px;
        background-color: #ccc;
        z-index: 2;
      }
    }

    .step-content {
      width: 45%;
      float: left;
      border-radius: 2px;
      position: relative;

      &:before {
        position: absolute;
        top: 26px;
        right: -15px;
        display: inline-block;
        border-top: 15px solid transparent;
        border-left: 15px solid #e0e0e0;
        border-right: 0 solid #e0e0e0;
        border-bottom: 15px solid transparent;
        content: " ";
      }

      &:after {
        position: absolute;
        top: 27px;
        right: -14px;
        display: inline-block;
        border-top: 14px solid transparent;
        border-left: 14px solid #fff;
        border-right: 0 solid #fff;
        border-bottom: 14px solid transparent;
        content: " ";
      }
    }

    &.timeline-inverted {
      align-items: flex-end;

      .step-content {
        float: right;

        &:before {
          border-left-width: 0;
          border-right-width: 15px;
          left: -15px;
          right: auto;
        }

        &:after {
          border-left-width: 0;
          border-right-width: 14px;
          left: -14px;
          right: auto;
        }
      }
    }
  }

  &.stepper-vertical li:not(:last-child):after {
    content: " ";
    position: absolute;
    width: 3px;
    background-color: #e0e0e0;
    left: 50%;
    top: 65px;
    margin-left: -1.5px;
  }

  &.colorful-timeline li {
    a {
      padding: 0px 24px;
      left: 50%;

      .circle {
        width: 50px;
        height: 50px;
        line-height: 50px;
        font-size: 1.4em;
        text-align: center;
        position: absolute;
        top: 16px;
        margin-left: -50px;
        background-color: #ccc;
        z-index: 2;
      }
    }

    .step-content {
      width: 45%;
      float: left;
      border-radius: 2px;
      position: relative;

      &:before {
        position: absolute;
        top: 1rem;
        right: -15px;
        display: inline-block;
        border-top: 15px solid transparent;
        border-left: 15px solid #D32F2F;
        border-right: 0 solid #D32F2F;
        border-bottom: 15px solid transparent;
        content: " ";
      }

      &:after {
        position: absolute;
        top: 1rem;
        right: -14px;
        display: inline-block;
        border-top: 14px solid transparent;
        border-left: 14px solid #D32F2F;
        border-right: 0 solid #D32F2F;
        border-bottom: 14px solid transparent;
        content: " ";
      }

      .timeline-header {
        border-top-left-radius: 2px;
        border-top-right-radius: 2px;
      }
    }

    &.timeline-inverted {
      align-items: flex-end;

      .step-content {
        float: right;

        &:before {
          border-left-width: 0;
          border-right-width: 15px;
          left: -15px;
          right: auto;
        }

        &:after {
          border-left-width: 0;
          border-right-width: 14px;
          left: -14px;
          right: auto;
        }
      }
    }
  }

  &.stepper-vertical.colorful-timeline li:not(:last-child):after {
    content: " ";
    position: absolute;
    width: 3px;
    background-color: #D32F2F;
    left: 50%;
    top: 65px;
    margin-left: -1.5px;
  }

  &.timeline-animated {
    .more-padding {
      padding-right: 100px;
      padding-left: 100px;
    }

    li {
      transition: all 0.7s ease-in-out;

      a {
        padding: 0px 24px;
        left: 50%;

        .circle {
          width: 50px;
          height: 50px;
          line-height: 50px;
          font-size: 1.4em;
          text-align: center;
          position: absolute;
          top: 16px;
          margin-left: -50px;
          background-color: #ccc;
          z-index: 2;
        }
      }

      .step-content {
        width: 45%;
        float: left;
        border-radius: 2px;
        position: relative;

        &:before {
          position: absolute;
          top: 26px;
          right: -15px;
          display: inline-block;
          border-top: 15px solid transparent;
          border-left: 15px solid #e0e0e0;
          border-right: 0 solid #e0e0e0;
          border-bottom: 15px solid transparent;
          content: " ";
        }

        &:after {
          position: absolute;
          top: 27px;
          right: -14px;
          display: inline-block;
          border-top: 14px solid transparent;
          border-left: 14px solid #fff;
          border-right: 0 solid #fff;
          border-bottom: 14px solid transparent;
          content: " ";
        }
      }

      &.timeline-inverted {
        align-items: flex-end;

        .step-content {
          float: right;

          &:before {
            border-left-width: 0;
            border-right-width: 15px;
            left: -15px;
            right: auto;
          }

          &:after {
            border-left-width: 0;
            border-right-width: 14px;
            left: -14px;
            right: auto;
          }
        }
      }
    }
  }

  &.stepper-vertical li:not(:last-child):after {
    content: " ";
    position: absolute;
    width: 3px;
    background-color: #e0e0e0;
    left: 50%;
    top: 65px;
    margin-left: -1.5px;
  }
}

@media (max-width: 1025px) {
  .timeline-main .stepper {

    &.timeline-images li,
    &.timeline li,
    &.colorful-timeline li,
    &.timeline-animated li,
    &.timeline-simple li {
      align-items: flex-end;
    }
  }
}

@media (max-width: 450px) {
  .timeline-main .stepper {

    &.timeline-images li,
    &.timeline-simple li,
    &.timeline li,
    &.colorful-timeline li,
    &.timeline-animated li {
      a {
        left: 6%;
      }

      .step-content {
        width: 80%;
        left: 3rem;
        margin-right: 3rem;
        margin-bottom: 2rem;
        float: right;
      }
    }

    &.timeline-simple li,
    &.timeline li,
    &.colorful-timeline li,
    &.timeline-animated li {
      .step-content {
        &:before {
          border-left-width: 0;
          border-right-width: 15px;
          left: -15px;
          right: auto;
        }

        &:after {
          border-left-width: 0;
          border-right-width: 14px;
          left: -14px;
          right: auto;
        }
      }
    }

    &.stepper-vertical.timeline-simple li:not(:last-child):after,
    &.stepper-vertical.timeline-images li:not(:last-child):after,
    &.colorful-timeline li a,
    &.stepper-vertical.colorful-timeline li:not(:last-child):after,
    &.timeline-animated li a,
    &.stepper-vertical li:not(:last-child):after {
      left: 6%;
    }
  }
}

@media (min-width: 451px) and (max-width: 1025px) {

  .timeline-main .stepper {

    &.timeline-simple li,
    &.timeline-animated li,
    &.timeline li,
    &.colorful-timeline li {
      .step-content {
        &:before {
          border-left-width: 0;
          border-right-width: 15px;
          left: -15px;
          right: auto;
        }

        &:after {
          border-left-width: 0;
          border-right-width: 14px;
          left: -14px;
          right: auto;
        }
      }
    }

    &.timeline-images li,
    &.timeline li,
    &.timeline-simple li,
    &.timeline-animated li,
    &.colorful-timeline li {
      a {
        left: 6%;
      }

      .step-content {
        width: 85%;
        left: 3rem;
        margin-right: 3rem;
        margin-bottom: 2rem;
        float: right;
      }
    }

    &.stepper-vertical.timeline-images li:not(:last-child):after,
    &.stepper-vertical.timeline-simple li:not(:last-child):after,
    &.stepper-vertical li:not(:last-child):after,
    &.stepper-vertical.colorful-timeline li:not(:last-child):after {
      left: 6%;
    }
  }
}
