﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace KobiPanel.Models.ViewModels
{
    public class YeniSatisEkle
    {
        public Satislar Satis { get; set; }

        public Musteriler Musteri { get; set; }
    }

    public class SatisViewModel
    {

        public IEnumerable<Satislar> Satislar { get; set; }
        public List<Urun> urun { get; set; }
        public List<Musteriler> Musteriler { get; set; }
    }


    public class SatislarListesiDTO
    {

        public int SatisID { get; set; }
        [DisplayName("Teslim Şekli")]
        public bool ServisMi { get; set; }
        [DisplayName("Teslim Durumu")]
        public bool? TeslimEdildiMi { get; set; }
        [DisplayName("Satış Tarihi")]
        public DateTime SatisTarihi { get; set; }
        public decimal Tutar { get; set; }
        public Musteriler Musteri { get; set; }
    }
}
