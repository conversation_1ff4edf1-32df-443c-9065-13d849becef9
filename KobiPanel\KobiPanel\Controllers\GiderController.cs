﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using KobiPanel.Models;

namespace KobiPanel.Controllers
{
    public class GiderController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();

        // GET: Gider
        public ActionResult Index()
        {
            return View(db.Giderler.ToList());
        }


        // GET: Gider/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: Gider/Create
        // To protect from overposting attacks, please enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind(Include = "id,Konu,Tutar")] Giderler giderler)
        {
            giderler.Tarih = DateTime.Now;
            if (ModelState.IsValid)
            {
                giderler.Tarih = DateTime.Now;
                db.Giderler.Add(giderler);
                db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(giderler);
        }

        // GET: Gider/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Giderler giderler = db.Giderler.Find(id);
            if (giderler == null)
            {
                return HttpNotFound();
            }
            return View(giderler);
        }

        // POST: Gider/Edit/5
        // To protect from overposting attacks, please enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind(Include = "id,Konu,Tutar")] Giderler giderler)
        {
            giderler.Tarih = DateTime.Now;
            if (ModelState.IsValid)
            {
                db.Entry(giderler).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("Index");
            }
            return View(giderler);
        }

        // GET: Gider/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Giderler giderler = db.Giderler.Find(id);
            if (giderler == null)
            {
                return HttpNotFound();
            }
            return View(giderler);
        }

        // POST: Gider/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            Giderler giderler = db.Giderler.Find(id);
            db.Giderler.Remove(giderler);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
