﻿@model KobiPanel.Models.Musteriler

@{
    ViewBag.Title = "Kaydet";
    ViewBag.PageHeader = "<PERSON>üş<PERSON><PERSON> Ekle";
}


@if (TempData["musterieklebasarili"] != null)
{
    <div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>@TempData["musterieklebasarili"]</strong>
    </div>
}

@if (TempData["musterieklebasarisiz"] != null)
{
    <div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>@TempData["musterieklebasarisiz"]</strong>
    </div>
}




@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <div class="row">
        <div class="col-md-6">
            <div class="row">
                <div class="col-md-12">
                    <div class="md-form form-lg">
                        @Html.EditorFor(model => model.adsoyad, new { htmlAttributes = new { @class = "form-control ilkharfbuyut form-control-lg md-bg"} })
                        @Html.LabelFor(model => model.adsoyad, htmlAttributes: new { @class = "" })
                        @Html.ValidationMessageFor(model => model.adsoyad, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="md-form input-group form-lg">
                        <div class="input-group-prepend">
                            <span class="input-group-text md-addon" id="material-addon1">0</span>
                        </div>
                        @Html.LabelFor(model => model.tel, htmlAttributes: new { @class = "" })
                        @Html.EditorFor(model => model.tel, new { htmlAttributes = new { @class = "form-control", placeholder = "5. . . . . . . . . . . ." } })
                        @Html.ValidationMessageFor(model => model.tel, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="md-form">
                        @Html.EditorFor(model => model.KucukBasHayvanSayisi, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.LabelFor(model => model.KucukBasHayvanSayisi, htmlAttributes: new { @class = "" })
                        @Html.ValidationMessageFor(model => model.KucukBasHayvanSayisi, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="md-form">
                        @Html.EditorFor(model => model.BuyukBasHayvanSayisi, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.LabelFor(model => model.BuyukBasHayvanSayisi, htmlAttributes: new { @class = "" })
                        @Html.ValidationMessageFor(model => model.BuyukBasHayvanSayisi, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="col-md-12">
                <div class="md-form">
                    @Html.DropDownList("CityID", null, htmlAttributes: new { @class = "mdb-select md-form", onchange = "CityOnChange(this)", searchable = "İl Seçin..." })
                    @Html.LabelFor(model => model.CityID, htmlAttributes: new { @class = "active" })
                    @Html.ValidationMessageFor(model => model.CityID, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="col-md-12">
                <div class="md-form">
                    @Html.DropDownList("TownID", null, htmlAttributes: new { @class = "mdb-select md-form", onchange = "GetNeighborhood(this)", searchable = "İlçe Seçin..." })
                    @Html.LabelFor(model => model.TownID, htmlAttributes: new { @class = "active" })
                    @Html.ValidationMessageFor(model => model.TownID, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="col-md-12">
                <div class="md-form">
                    @Html.DropDownList("NeighborhoodID", null, htmlAttributes: new { @class = "mdb-select md-form", searchable = "Mahalle Seçin..." })
                    @Html.LabelFor(model => model.NeighborhoodID, htmlAttributes: new { @class = "active" })
                    @Html.ValidationMessageFor(model => model.NeighborhoodID, "", new { @class = "text-danger" })
                </div>
            </div>


        </div>


        <div class="col-md-12">
            <button type="submit" class="btn btn-light-green btn-lg btn-block btn-rounded waves-effect waves-light">Kaydet</button>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Listeye Geri Dön", "MusteriAnasayfa", null, new { @class = "btn btn-info" })
</div>
<script>

    function CityOnChange(data) {
        $.ajax({
            type: 'GET',
            url: '/Musteri/GetTowns/?Id=' + data.value,
            success: function (data) {
                $("#TownID").find("option").remove();
                  $("#NeighborhoodID").find("option").remove();
                $('.mdb-select').material_select('destroy'); 
                $('.mdb-select').material_select();
                $.each(data, function (index, element) {
                    $("#TownID").append(new Option(element.TownName, element.TownID))
                });
            }
        });
    }
    function GetNeighborhood(data) {
        $.ajax({
            type: 'GET',
            url: '/Musteri/GetNeighborhood/?Id=' + data.value,
            success: function (data) {
                $("#NeighborhoodID").find("option").remove();
                $('.mdb-select').material_select('destroy'); 
                $('.mdb-select').material_select();
                $.each(data, function (index, element) {
                    $("#NeighborhoodID").append("<option value=" + element.NeighborhoodID + ">" + element.NeighborhoodName + "</option>");
                    //console.log(element.NeighborhoodID + "=>" + element.NeighborhoodName);

                });
            }
        });
    }
</script>