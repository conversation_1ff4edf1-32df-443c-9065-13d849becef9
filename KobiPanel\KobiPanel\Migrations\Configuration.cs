namespace KobiPanel.Migrations
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Migrations;
    using System.IO;
    using System.Linq;

    internal sealed class Configuration : DbMigrationsConfiguration<KobiPanel.Models.KobiPanelDbContext>
    {
        public Configuration()
        {
            AutomaticMigrationsEnabled = true;
            AutomaticMigrationDataLossAllowed = true;
        }

        protected override void Seed(KobiPanel.Models.KobiPanelDbContext context)
        {
            if (context.City.Count()==0)

            {

                var baseDir = AppDomain.CurrentDomain.BaseDirectory.Replace("\\bin", string.Empty) + "\\Migrations";

                context.Database.ExecuteSqlCommand(File.ReadAllText(baseDir + "\\il_ilce.sql"));

            }
        }
    }
}
