﻿@model IEnumerable<KobiPanel.Models.Musteriler>
@{
    /**/

    ViewBag.Title = "Müşteri Listesi";
}
<p>
    @Html.ActionLink("Yeni Müşteri Ekle", "MusteriEkle", null, new { @class = "btn btn-primary" })
</p>
@if (TempData["silmeislemi"] != null)
{
    <div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>@TempData["silmeislemi"]</strong>
    </div>
}
@if (TempData["musteriduzenlebasarili"] != null)
{
    <div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>@TempData["musteriduzenlebasarili"]</strong>
    </div>
}





<!--//TODO : Tablo geliştirilecek. Türkçeleştirilecek-->
<table id="example" class="table table-striped table-bordered dt-responsive" style="width:100%">
    <thead>
        <tr>
            <th class="th-lg">
                Ad Soyad
            </th>
            <th class="th-lg">
                Telefon
            </th>
            <th>
                İlçe
            </th>
            <th>
                Mahalle
            </th>
            <th>
                İşlem
            </th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.adsoyad)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.tel)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Town.TownName)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.Neighborhood.NeighborhoodName)
                </td>
                <td>
                    <i class="fas fa-info-circle">
                        @Html.ActionLink("Detay", "MusteriDetay", new { id = item.ID }, new { @class = "text-dark" })
                    </i>
                    <i class="fas fa-edit">
                        @Html.ActionLink("Düzenle", "MusteriDuzenle", new { id = item.ID }, new { @class = "text-dark" })
                    </i>
                    <i class="far fa-trash-alt" style="color:red">
                        @Html.ActionLink("Sil", "MusteriSil", new { id = item.ID }, new { @class = "text-danger" })
                    </i>
                </td>
            </tr>
        }
    </tbody>
</table>

<script>
    $(document).ready(function () {
        $('#example').DataTable();
    });
</script>

<link href="~/Content/datatable/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="~/Content/datatable/responsive.bootstrap4.min.css" rel="stylesheet" />
<script src="~/Content/datatable/dataTables.bootstrap4.min.js"></script>
<script src="~/Content/datatable/dataTables.responsive.min.js"></script>
<script src="~/Content/datatable/jquery.dataTables.min.js"></script>
<script src="~/Content/datatable/responsive.bootstrap4.min.js"></script>