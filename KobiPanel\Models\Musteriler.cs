namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Data.Entity.Spatial;

    [Table("Musteriler")]
    public class Musteriler
    {     
        [Key,DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        [DisplayName("Adı Soyadı")]
        [Required(AllowEmptyStrings = false, ErrorMessage = "Ad Soyad alanı gereklidir.")]
        [DataType(DataType.Text)]
        public string adsoyad { get; set; }

        [DataType(DataType.PhoneNumber)]
        [Required(AllowEmptyStrings = false, ErrorMessage = "Telefon numarası giriniz.")]
        [DisplayName("Telefon Numarası")]
        public string tel { get; set; }

        [DataType(DataType.Date)]
        [DisplayName("Kayıt Tarihi")]
        public DateTime kayitTarihi { get; set; }

        [DataType(DataType.Date)]
        [DisplayName("Düzenlenme Tarihi")]
        public DateTime DuzenlemeTarihi { get; set; }

        [DisplayName("Yaşı")]
        public short? yas { get; set; }

        
        [DisplayName("İl")]
        public int? CityID { get; set; }

        
        [DisplayName("İlçe")]
        public int? TownID { get; set; }

        
        [DisplayName("Mahalle")]
        public int? NeighborhoodID { get; set; }


        [DisplayName("KüçükBaş Hayvan Sayısı")]
        public int? KucukBasHayvanSayisi { get; set; }

        [DisplayName("BüyükBaş Hayvan Sayısı")]
        public int? BuyukBasHayvanSayisi { get; set; }


        public virtual City City { get; set; }
        public virtual Neighborhood Neighborhood { get; set; }
        public virtual Town Town { get; set; }
    }
}
