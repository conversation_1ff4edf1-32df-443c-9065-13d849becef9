﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using KobiPanel.Models;

namespace KobiPanel.Controllers
{
    public class UrunController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();

        // GET: Urun
        public ActionResult UrunAnasayfa()
        {
            return View(db.Urun.ToList());
        }

        // GET: Urun/Create
        public ActionResult UrunEkle()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult UrunEkle(Urun urun)
        {
            urun.EklenmeTarihi = DateTime.Now;
            urun.DuzenlenmeTarihi = DateTime.Now;

            if (ModelState.IsValid)
            {



                db.Urun.Add(urun);
                db.SaveChanges();
                return RedirectToAction("UrunAnasayfa");
            }

            return View(urun);
        }

        // GET: Urun/Edit/5
        public ActionResult UrunDuzenle(int? id)
        {


            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Urun urun = db.Urun.Find(id);
            if (urun == null)
            {
                return HttpNotFound();
            }
            return View(urun);
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult UrunDuzenle(Urun urun)
        {
            urun.DuzenlenmeTarihi = DateTime.Now;

            if (ModelState.IsValid)
            {
                db.Entry(urun).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("UrunAnasayfa");
            }
            return View(urun);
        }

        // GET: Urun/Delete/5
        public ActionResult UrunSil(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Urun urun = db.Urun.Find(id);
            if (urun == null)
            {
                return HttpNotFound();
            }
            return View(urun);
        }

        // POST: Urun/Delete/5
        [HttpPost, ActionName("UrunSil")]
        [ValidateAntiForgeryToken]
        public ActionResult UrunSilOnayli(int id)
        {
            try
            {
                Urun urun = db.Urun.Find(id);
                if (urun == null)
                {
                    TempData["UrunSilHata"] = "Silinmek istenen ürün bulunamadı.";
                    return RedirectToAction("UrunAnasayfa");
                }

                string urunAdi = urun.UrunAdi; // Silmeden önce adı sakla
                db.Urun.Remove(urun);
                int sonuc = db.SaveChanges();

                if (sonuc > 0)
                {
                    TempData["UrunSilBasarili"] = urunAdi + " ürünü başarıyla silindi.";
                }
                else
                {
                    TempData["UrunSilHata"] = "Ürün silme işlemi başarısız oldu.";
                }
            }
            catch (Exception ex)
            {
                TempData["UrunSilHata"] = "Ürün silme işlemi sırasında bir hata oluştu: " + ex.Message;
            }

            return RedirectToAction("UrunAnasayfa");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
