﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace KobiPanel.Models.Bicme
{
    [Table("BicilenTarla")]
    public class BicilenTarlalar
    {
        [Key,DatabaseGenerated(DatabaseGeneratedOption.Identity),DisplayName("Tarla No")]
        public int TarlaID { get; set; }

        public int MusteriID { get; set; }

        [DisplayName("Tarla Dönümü")]
        public int Donum { get; set; }

        [DisplayName("Tarla Adı")]
        public string TarlaAdi { get; set; }

        [DisplayName("Biçme Fiyatı")]
        public int BicmeFiyati { get; set; }

        [DisplayName("Toplam Biçme Tutarı")]
        public int ToplamTutar { get; set; }

        [DisplayName("Tahsilat Tutarı")]
        public int TahsilatTutari { get; set; }

        [DisplayName("Kalan Tutar")]
        public int KalanTutar { get; set; }

        [DataType(DataType.Date),DisplayName("Biçim Tarihi")]
        public DateTime BicimTarihi { get; set; }

        [DisplayName("Açıklama")]
        public string aciklama { get; set; }
        
        [DisplayName("Tarladan Çıkan Mahsül(kg)")]
        public int ToplamHasatMiktari { get; set; }

        [DisplayName("Kilogram/1000m2")]
        public int DonumBasinaKg { get; set; }


        public List<KantarFisi> kantarfisi { get; set; }


        [ForeignKey("MusteriID")]
        public virtual Musteriler Musteri { get; set; }

    }
}