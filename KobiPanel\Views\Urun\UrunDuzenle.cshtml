﻿@model KobiPanel.Models.Urun

@{
    ViewBag.Title = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml";
}




@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    
<div class="form-horizontal">
    <hr />
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(model => model.UrunID)

    <div class="col-md-12">
        <div class="md-form">
            @Html.LabelFor(model => model.UrunAdi, htmlAttributes: new { @class = "control-label col-md-2" })
            @Html.EditorFor(model => model.UrunAdi, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.UrunAdi, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="col-md-12">
        <div class="md-form">
            @Html.LabelFor(model => model.UrunFiyati, htmlAttributes: new { @class = "control-label col-md-2" })
            @Html.EditorFor(model => model.UrunFiyati, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.UrunFiyati, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="col-md-12">
        <div class="md-form">
            @Html.EditorFor(model => model.NakliyeUcreti, new { htmlAttributes = new { @class = "form-control" } })
            @Html.LabelFor(model => model.NakliyeUcreti, htmlAttributes: new { @class = "" })
            @Html.ValidationMessageFor(model => model.NakliyeUcreti, "", new { @class = "text-danger" })
        </div>
    </div>

    @Html.HiddenFor(model=>model.EklenmeTarihi)


    <div class="col-md-12">
        <div class="md-form">
            @Html.LabelFor(model => model.MevcutStok, htmlAttributes: new { @class = "control-label col-md-2" })
            @Html.EditorFor(model => model.MevcutStok, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.MevcutStok, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        <div class="col-md-offset-2 col-md-10">
            <input type="submit" value="Save" class="btn btn-default" />
        </div>
    </div>
</div>
}

<div>
    @Html.ActionLink("Listeye Geri Dön", "UrunAnasayfa")
</div>
