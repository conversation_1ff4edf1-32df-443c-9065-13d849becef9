﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace KobiPanel.Filters
{
    public class ExcFilter : FilterAttribute, IExceptionFilter
    {
        public void OnException(ExceptionContext filterContext)
        {
            try
            {
                if (filterContext?.Exception != null)
                {
                    // Exception'ı logla
                    System.Diagnostics.Debug.WriteLine($"Global Exception: {filterContext.Exception.Message}");
                    System.Diagnostics.Debug.WriteLine($"Stack Trace: {filterContext.Exception.StackTrace}");

                    filterContext.ExceptionHandled = true;

                    // Null reference exception'lar için özel mesaj
                    string errorMessage = "Bir hata oluştu.";
                    if (filterContext.Exception is NullReferenceException)
                    {
                        errorMessage = "Veri erişim hatası oluştu. Lütfen tekrar deneyin.";
                    }
                    else if (filterContext.Exception is ArgumentNullException)
                    {
                        errorMessage = "Gerekli bilgiler eksik. Lütfen tüm alanları doldurun.";
                    }

                    if (filterContext.Controller?.TempData != null)
                    {
                        filterContext.Controller.TempData["error"] = errorMessage;
                        filterContext.Controller.TempData["errorDetails"] = filterContext.Exception.Message;
                    }

                    // Ajax request ise JSON döndür
                    if (filterContext.HttpContext.Request.IsAjaxRequest())
                    {
                        filterContext.Result = new JsonResult
                        {
                            Data = new { success = false, message = errorMessage },
                            JsonRequestBehavior = JsonRequestBehavior.AllowGet
                        };
                    }
                    else
                    {
                        filterContext.Result = new RedirectResult("~/Home/Login");
                    }
                }
            }
            catch (Exception ex)
            {
                // Exception handler'da bile hata olursa
                System.Diagnostics.Debug.WriteLine($"ExcFilter hatası: {ex.Message}");
                filterContext.Result = new RedirectResult("~/Home/Login");
            }
        }
    }
}