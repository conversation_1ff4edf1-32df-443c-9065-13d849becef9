﻿@using GridMvc.Columns
@model GridMvc.IGrid
@if (Model == null) { return; }
@if (Model.RenderOptions.RenderRowsOnly)
{
    @RenderGridBody();
}
else
{
    <div class="grid-mvc" data-lang="@Model.Language" data-gridname="@Model.RenderOptions.GridName" data-selectable="@Model.RenderOptions.Selectable.ToString().ToLower()" data-multiplefilters ="@Model.RenderOptions.AllowMultipleFilters.ToString().ToLower()">
        <div class="grid-wrap">
            <table class="table table-striped grid-table">
                @* Draw grid header *@
                <thead>
                    @RenderGridHeader()
                </thead>
                <tbody>
                    @RenderGridBody()
                </tbody>
            </table>
            @RenderGridPager()
        </div>
    </div>
}
@helper RenderGridBody()
{
    if (!Model.ItemsToDisplay.Any())
    {
    <tr class="ilkharfbuyut grid-empty-text">
        <td colspan="@Model.Columns.Count()">
            @Model.EmptyGridText
        </td>
    </tr>
    }
    else
    {
        foreach (object item in Model.ItemsToDisplay)
        {
    <tr class="ilkharfbuyut grid-row @Model.GetRowCssClasses(item)">
        @foreach (IGridColumn column in Model.Columns)
        {
            @column.CellRenderer.Render(column, column.GetCell(item))
        }
    </tr>
        }
    }
}
@helper RenderGridHeader()
{
    <tr class="ilkharfbuyut ">
        @foreach (IGridColumn column in Model.Columns)
        {
            @column.HeaderRenderer.Render(column)
        }
    </tr>
}
@helper RenderGridPager()
{
    if (Model.EnablePaging && Model.Pager != null)
    {
    <div class="ilkharfbuyut grid-footer">
        @Html.Partial(Model.Pager.TemplateName, Model.Pager)
    </div>
    }
}
