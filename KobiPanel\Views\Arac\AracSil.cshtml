﻿@model KobiPanel.Models.Vehicles.Araclar

@{
    ViewBag.Title = "Araç Silme İşlemi";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h3>Aşağıdaki aracı silmek istediğinize emin misiniz?</h3>
<div>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.Plaka)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Plaka)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.TescilNo)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.TescilNo)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.VizeBitisTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.VizeBitisTarihi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SigortaBitisTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SigortaBitisTarihi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.EgzozBitisTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.EgzozBitisTarihi)
        </dd>

    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Sil" class="btn btn-danger" /> |
            @Html.ActionLink("Back to List", "AracAnasayfa")
        </div>
    }
</div>
