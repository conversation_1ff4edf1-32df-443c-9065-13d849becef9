﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace KobiPanel.Helpers
{
    public class IPAutorizeAttribute:ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            try
            {
                if (filterContext?.HttpContext?.Session == null ||
                    filterContext.HttpContext.Session["User"] == null)
                {
                    filterContext.Result = new RedirectResult("~/Home/Login");
                    return;
                }

                // Ek güvenlik kontrolü - session timeout kontrolü
                if (filterContext.HttpContext.Session.IsNewSession)
                {
                    filterContext.Result = new RedirectResult("~/Home/Login");
                    return;
                }
            }
            catch (Exception ex)
            {
                // Hata durumunda güvenli tarafta kal - login'e yönlendir
                System.Diagnostics.Debug.WriteLine("IPAutorize hatası: " + ex.Message);
                filterContext.Result = new RedirectResult("~/Home/Login");
            }
        }
    }
}