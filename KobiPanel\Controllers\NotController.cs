﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using KobiPanel.Models;

namespace KobiPanel.Controllers
{
    public class NotController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();

        // GET: Not
        public ActionResult NotAnasayfa()
        {
            ViewBag.PageHeader = "Notlar";
            return View(db.Not.ToList());
        }
        
        // GET: Not/Create
        public ActionResult NotEkle()
        {
            ViewBag.PageHeader = "Yeni Not Oluştur";
            return View();
        }

        
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult NotEkle(Not not)
        {
            not.Tarih = DateTime.Now;
            if (ModelState.IsValid)
            {
                db.Not.Add(not);
                int sonuc = db.SaveChanges();

                if (sonuc>0)
                {
                    TempData["notolusturuldu"] = "Not Başarıyla E<PERSON>";
                }


                return RedirectToAction("NotAnasayfa");
            }

            return View(not);
        }

        // GET: Not/Edit/5
        public ActionResult NotDuzenle(int? id)
        {
            ViewBag.PageHeader = "Not Düzenleme";
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Not not = db.Not.Find(id);
            if (not == null)
            {
                return HttpNotFound();
            }
            return View(not);
        }

        
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult NotDuzenle(Not not)
        {
            not.Tarih = DateTime.Now;
            if (ModelState.IsValid)
            {
                db.Entry(not).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("NotAnasayfa");
            }
            return View(not);
        }

        // GET: Not/Delete/5
        public ActionResult NotSil(int? id)
        {
            ViewBag.PageHeader = "Not Silme İşlemi";

            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Not not = db.Not.Find(id);
            if (not == null)
            {
                return HttpNotFound();
            }
            return View(not);
        }

        // POST: Not/Delete/5
        [HttpPost, ActionName("NotSil")]
        [ValidateAntiForgeryToken]
        public ActionResult NotSilOnayli(int id)
        {
            Not not = db.Not.Find(id);
            db.Not.Remove(not);
            db.SaveChanges();
            return RedirectToAction("NotAnasayfa");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
