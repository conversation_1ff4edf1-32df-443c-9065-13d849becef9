﻿@model KobiPanel.Models.Urun

@{
    ViewBag.PageHeader = "<PERSON><PERSON><PERSON><PERSON>";
    ViewBag.Title = "<PERSON>r<PERSON><PERSON>";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


@using (Html.BeginForm()) 
{
    @Html.AntiForgeryToken()
    
<div class="form-horizontal">
    <hr />

    <div class="row">

        @Html.ValidationSummary(true, "", new { @class = "text-danger" })



        <div class="col-md-12">
            <div class="md-form">
                @Html.EditorFor(model => model.UrunAdi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.LabelFor(model => model.UrunAdi, htmlAttributes: new { @class = "" })
                @Html.ValidationMessageFor(model => model.UrunAdi, "", new { @class = "text-danger" })
            </div>
        </div>


        <div class="col-md-12">
            <div class="md-form">
                @Html.EditorFor(model => model.UrunFiyati, new { htmlAttributes = new { @class = "form-control" } })
                @Html.LabelFor(model => model.UrunFiyati, htmlAttributes: new { @class = "" })
                @Html.ValidationMessageFor(model => model.UrunFiyati, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="col-md-12">
            <div class="md-form">
                @Html.EditorFor(model => model.NakliyeUcreti, new { htmlAttributes = new { @class = "form-control" } })
                @Html.LabelFor(model => model.NakliyeUcreti, htmlAttributes: new { @class = "" })
                @Html.ValidationMessageFor(model => model.NakliyeUcreti, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="col-md-12">
            <div class="md-form">
                @Html.EditorFor(model => model.MevcutStok, new { htmlAttributes = new { @class = "form-control" } })
                @Html.LabelFor(model => model.MevcutStok, htmlAttributes: new { @class = "" })
                @Html.ValidationMessageFor(model => model.MevcutStok, "", new { @class = "text-danger" })
            </div>
        </div>


        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Ekle" class="btn btn-success" />
            </div>
        </div>
    </div>


</div>
}

<div>
    @Html.ActionLink("Listeye Dön", "UrunAnasayfa")
</div>
