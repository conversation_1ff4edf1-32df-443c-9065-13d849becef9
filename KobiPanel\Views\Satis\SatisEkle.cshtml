﻿@model KobiPanel.Models.Satislar

@{
    ViewBag.Title = "Ye<PERSON>ı<PERSON>";
    ViewBag.PageHeader = "Yeni Satı<PERSON> Ekle";
}

<link href="~/Content/css/bootstrap-select.min.css" rel="stylesheet" />
<script src="~/Scripts/bootstrap-select.min.js"></script>

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()

    <div class="form-horizontal">

        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })

        @if (ViewBag.Success != null)
        {
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                <strong>@ViewBag.Success</strong>
            </div>
        }

        @if (ViewBag.Error != null)
        {
            <div class="alert alert-warning alert-dismissible">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                <strong>@ViewBag.Error</strong>
            </div>
        }

        <div>
            <label class="mdb-main-label">Müşteri</label>
            @Html.DropDownListFor(m => m.MusteriID, (SelectList)ViewBag.SListMusteriler, "Lütfen Bir Müşteri Seçiniz", new { @class = "mdb-select md-form md-outline colorful-select dropdown-primary", searchable = "Müşteri Ara", aria_invalid = "false", data_live_search = "true", lang = "tr" })
            <button type="button" onclick="window.location='@Url.Action("MusteriEkle","Musteri")'" class="btn-save btn btn-primary btn-sm">Yeni Müşteri Ekle</button>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div>
                    @Html.DropDownListFor(m => m.UrunID, (SelectList)ViewBag.SListUrunler, "Ürün Seçiniz", new { @class = "mdb-select md-form md-outline colorful-select dropdown-primary", searchable = "Ürün Ara", aria_invalid = "false", data_live_search = "true", lang = "tr" })
                </div>
            </div>
            <div class="col-md-6">
                <div class="row">
                    <div class="col-md-12">
                        <div class="md-form">
                            @Html.EditorFor(model => model.UrunAdeti, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.LabelFor(model => model.UrunAdeti, htmlAttributes: new { @class = "" })
                            @Html.ValidationMessageFor(model => model.UrunAdeti, "", new { @class = "text-danger" })
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success alert-dismissible" role="alert">
                    Tutar otomatik olarak hesaplanacaktır.
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">

                <div class="md-form">
                    @Html.EditorFor(model => model.SatisTarihi, new { htmlAttributes = new { @class = "form-control", type = "date" } })
                    @Html.LabelFor(model => model.SatisTarihi, htmlAttributes: new { @class = "active" })
                    @Html.ValidationMessageFor(model => model.SatisTarihi, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3">
                <div class="form-group" style="padding-left: 1em;">
                    @Html.LabelFor(m => m.KrediKartiMi)
                    <div class="switch">
                        <label>
                            <i style="color:green"> Nakit</i>
                            <input type="checkbox" onchange="$('#@Html.IdFor(m=>m.KrediKartiMi)').val($(this).prop('checked'));">
                            <span class="lever"></span> <i style="color:#5788d8">Kredi Kartı</i> <br>
                            @Html.HiddenFor(m => m.KrediKartiMi)
                        </label>
                    </div>
                    @Html.ValidationMessageFor(model => model.KrediKartiMi, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group" style="padding-left: 1em;">
                    @Html.LabelFor(m => m.ServisMi)
                    <div class="switch">
                        <label>
                            Depoda Teslim
                            <input type="checkbox" onchange="$('#@Html.IdFor(m=>m.ServisMi)').val($(this).prop('checked'));">
                            <span class="lever"></span> Adrese Teslim <br>
                            @Html.HiddenFor(m => m.ServisMi)
                        </label>
                    </div>
                    @Html.ValidationMessageFor(model => model.ServisMi, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group" style="padding-left: 1em;">
                    @Html.LabelFor(m => m.TeslimEdildiMi)
                    <div class="switch">
                        <label>
                            Teslim<i style="color:red"> Edilmedi</i>
                            <input type="checkbox" onchange="$('#@Html.IdFor(m=>m.TeslimEdildiMi)').val($(this).prop('checked'));">
                            <span class="lever"></span> Teslim <i style="color:green"> Edildi</i> <br>
                            @Html.HiddenFor(m => m.TeslimEdildiMi)
                        </label>
                    </div>
                    @Html.ValidationMessageFor(model => model.TeslimEdildiMi, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="col-md-3">
                <div>
                    <a class="btn btn-primary" data-toggle="collapse" href="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                        Detay
                    </a>
                </div>
                <!-- Collapsible element -->
                <div class="collapse" id="collapseExample">
                    @Html.EditorFor(model=>model.indirim, new { htmlAttributes = new { @class = "form-control" } })
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="md-form">
                    @Html.TextAreaFor(model => model.Aciklama, new { @class = "md-textarea form-control"  })
                    @Html.LabelFor(model => model.Aciklama, htmlAttributes: new { @class = "" })
                    @Html.ValidationMessageFor(model => model.Aciklama, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        <div class="row">
            <div class="col-md-12">
                <button type="submit" class="btn btn-light-green btn-lg btn-block btn-rounded">Kaydet</button>
            </div>
        </div>
    </div>
}

<!--Müşteri Arama Script-->
<script>
    /* When the user clicks on the button,
    toggle between hiding and showing the dropdown content */
    function myFunction() {
        document.getElementById("myDropdown").classList.toggle("show");
    }

    function filterFunction() {
        var input, filter, ul, li, a, i;
        input = document.getElementById("myInput");
        filter = input.value.toUpperCase();
        div = document.getElementById("myDropdown");
        a = div.getElementsByTagName("a");
        for (i = 0; i < a.length; i++) {
            txtValue = a[i].textContent || a[i].innerText;
            if (txtValue.toUpperCase().indexOf(filter) > -1) {
                a[i].style.display = "";
            } else {
                a[i].style.display = "none";
            }
        }
    }
</script>