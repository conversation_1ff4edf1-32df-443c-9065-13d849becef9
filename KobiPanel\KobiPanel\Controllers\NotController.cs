﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using KobiPanel.Models;

namespace KobiPanel.Controllers
{
    public class NotController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();

        // GET: Not
        public ActionResult Index()
        {
            return View(db.Not.ToList());
        }

        // GET: Not/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Not not = db.Not.Find(id);
            if (not == null)
            {
                return HttpNotFound();
            }
            return View(not);
        }

        // GET: Not/Create
        public ActionResult Create()
        {
            return View();
        }

        // POST: Not/Create
        // To protect from overposting attacks, please enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create([Bind(Include = "id,konu,aciklama,Tarih")] Not not)
        {
            not.Tarih = DateTime.Now;
            if (ModelState.IsValid)
            {
                db.Not.Add(not);
                db.SaveChanges();
                return RedirectToAction("Index");
            }

            return View(not);
        }

        // GET: Not/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Not not = db.Not.Find(id);
            if (not == null)
            {
                return HttpNotFound();
            }
            return View(not);
        }

        // POST: Not/Edit/5
        // To protect from overposting attacks, please enable the specific properties you want to bind to, for 
        // more details see https://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind(Include = "id,konu,aciklama,Tarih")] Not not)
        {
            not.Tarih = DateTime.Now;
            if (ModelState.IsValid)
            {
                db.Entry(not).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("Index");
            }
            return View(not);
        }

        // GET: Not/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Not not = db.Not.Find(id);
            if (not == null)
            {
                return HttpNotFound();
            }
            return View(not);
        }

        // POST: Not/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            Not not = db.Not.Find(id);
            db.Not.Remove(not);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
