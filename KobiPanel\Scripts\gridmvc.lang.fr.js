﻿/***
* Grid.Mvc french language (fr-FR) http://gridmvc.codeplex.com/
*/
window.GridMvc = window.GridMvc || {};
window.GridMvc.lang = window.GridMvc.lang || {};
GridMvc.lang.fr = {
    filterTypeLabel: "Type: ",
    filterValueLabel: "Valeur :",
    applyFilterButtonText: "Appliquer",
    filterSelectTypes: {
        Equals: "Egale",
        StartsWith: "Commence par",
        Contains: "Contient",
        EndsWith: "Fini par",
        GreaterThan: "Supérieur à",
        LessThan: "Inférieur à",
        GreaterThanOrEquals: "Supérieur ou égale à",
        LessThanOrEquals: "Inférieur ou égale à",
    },
    code: 'fr',
    boolTrueLabel: "Oui",
    boolFalseLabel: "Non",
    clearFilterLabel: "Effacer le filtre"
};