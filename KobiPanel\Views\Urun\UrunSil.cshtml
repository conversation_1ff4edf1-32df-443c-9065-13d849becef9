﻿@model KobiPanel.Models.Urun

@{
    ViewBag.Title = "Ü<PERSON>ün Silme İşlemi";
    ViewBag.PageHeader = "<PERSON><PERSON>ün Silme İşlemi";

    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h3><PERSON><PERSON>ağ<PERSON><PERSON>i ürünü silmek istediğinize emin misiniz?</h3>
<div>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.UrunAdi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.UrunAdi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.UrunFiyati)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.UrunFiyati)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.MevcutStok)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.MevcutStok)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.EklenmeTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.EklenmeTarihi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.DuzenlenmeTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.DuzenlenmeTarihi)
        </dd>

    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Sil" class="btn btn-danger" /> |
            @Html.ActionLink("Listeye Dön", "Urun Anasayfa")
        </div>
    }
</div>
