﻿http://gridmvc.codeplex.com/

Note: Grid.Mvc installer has already made the following changes to your project:
- Views/Shared/_Grid.cshtml, Views/Shared/_GridPager.cshtml - views for grid
- Content/Gridmvc.css - default stylesheet for the grid
- Scripts/gridmvc.js, Scripts/gridmvc.min.js - Grid.Mvc scripts
- Reference GridMvc.dll

Follow these steps to start use Grid.mvc:

1. Define a model for display in the grid, like:

	public class Foo
	{
		public string Title { get; set; }
		public string Description { get;set; }
	}


2. Prepare your model in your controller, like:
	
	public ActionResult Index()
	{
		var items = fooRepository.GetAll();
		return View(items);
	}


3. Render GridMvc in the View: You can use Html helper extenstion:

	Reference Grid.Mvc.Html in your view:

		@using GridMvc.Html

	Render Grid.Mvc:

		@Html.Grid(Model).Columns(columns =>
						{
							columns.Add(foo => foo.Title).Titled("Custom column title").SetWidth(110);
							columns.Add(foo => foo.Description).Sortable(true);
						}).WithPaging(20)


4. Register a grid stylesheet and scripts in your _Layouts.cshtml file:
	
	<link href="@Url.Content("~/Content/Gridmvc.css")" rel="stylesheet" type="text/css" />
	<script src="@Url.Content("~/Scripts/gridmvc.min.js")" type="text/javascript"> </script>


Notes:
	
Default layout of Grid.Mvc uses Twitter Bootstrap (css framework). You also need to include stylesheets on your page. You can download it from - http://twitter.github.com/bootstrap/
like:

	<link href="@Url.Content("~/Content/Css/bootstrap.min.css")" rel="stylesheet" type="text/css" />


Also you need to ensure that jQuery has registred on the page before gridmvc.min.js script.

For more documetation, please see: http://gridmvc.codeplex.com/