﻿@model KobiPanel.Models.Not

@{
    ViewBag.Title = "Not Silme";
}


<h3>Bu notu silmek istediğinize emin misiniz?</h3>
<div>
    <hr />
    <dl class="dl-horizontal">

        <dt>
            @Html.DisplayNameFor(model => model.konu)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.konu)
        </dd>


        <dt>
            @Html.DisplayNameFor(model => model.aciklama)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.aciklama)
        </dd>

    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Delete" class="btn btn-default" /> |
            @Html.ActionLink("Listeye Geri Dön", "NotSil",null, new {@class = "btn btn-primary" })
        </div>
    }
</div>
