﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="KobiPanelDBModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <!--Errors Found During Generation:
warning 6002: The table/view 'KobiPanelDB.dbo.Araclar' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="Araclar">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Plaka" Type="varchar" MaxLength="20" />
        </EntityType>
        <EntityType Name="City">
          <Key>
            <PropertyRef Name="CityID" />
          </Key>
          <Property Name="CityID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CityName" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="PlateNo" Type="nvarchar" MaxLength="2" Nullable="false" />
          <Property Name="PhoneCode" Type="nvarchar" MaxLength="7" Nullable="false" />
        </EntityType>
        <EntityType Name="District">
          <Key>
            <PropertyRef Name="DistrictID" />
          </Key>
          <Property Name="DistrictID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="TownID" Type="int" Nullable="false" />
          <Property Name="DistrictName" Type="nvarchar" MaxLength="100" Nullable="false" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6013: The table/view 'KobiPanelDB.dbo.Giderler' does not have a primary key defined and no valid primary key could be inferred. This table/view has been excluded. To use the entity, you will need to review your schema, add the correct keys, and uncomment it.
        <EntityType Name="Giderler">
          <Property Name="id" Type="int" />
          <Property Name="Konu" Type="varchar" MaxLength="150" />
          <Property Name="Tutar" Type="smallmoney" />
        </EntityType>-->
        <EntityType Name="Musteriler">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="ad" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="soyad" Type="varchar" MaxLength="50" />
          <Property Name="tel" Type="varchar" MaxLength="50" />
          <Property Name="yas" Type="smallint" />
          <Property Name="CityID" Type="int" />
          <Property Name="TownID" Type="int" />
          <Property Name="DistricID" Type="int" />
          <Property Name="NeighborhoodID" Type="int" />
          <Property Name="KucukBasHayvanSayisi" Type="int" />
          <Property Name="BuyukBasHayvanSayisi" Type="int" />
        </EntityType>
        <EntityType Name="Neighborhood">
          <Key>
            <PropertyRef Name="NeighborhoodID" />
          </Key>
          <Property Name="NeighborhoodID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="DistrictID" Type="int" Nullable="false" />
          <Property Name="NeighborhoodName" Type="nvarchar" MaxLength="100" Nullable="false" />
          <Property Name="ZipCode" Type="nvarchar" MaxLength="20" Nullable="false" />
        </EntityType>
        <EntityType Name="Not">
          <Key>
            <PropertyRef Name="id" />
          </Key>
          <Property Name="id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="konu" Type="varchar" MaxLength="50" />
          <Property Name="aciklama" Type="varchar(max)" />
        </EntityType>
        <EntityType Name="Rehber">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="AdSoyad" Type="varchar" MaxLength="100" />
          <Property Name="il" Type="varchar" MaxLength="100" />
          <Property Name="ilce" Type="varchar" MaxLength="100" />
          <Property Name="koy" Type="varchar" MaxLength="100" />
          <Property Name="adres" Type="varchar" MaxLength="150" />
          <Property Name="KBHayvanSayisi" Type="int" />
          <Property Name="BBHayvanSayisi" Type="int" />
          <Property Name="Telefon" Type="varchar" MaxLength="50" />
        </EntityType>
        <EntityType Name="Satislar">
          <Key>
            <PropertyRef Name="SatisID" />
          </Key>
          <Property Name="SatisID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="MusteriID" Type="int" Nullable="false" />
          <Property Name="Tutar" Type="smallmoney" Nullable="false" />
          <Property Name="ServisMi" Type="bit" Nullable="false" />
          <Property Name="SatisTarihi" Type="smalldatetime" Nullable="false" />
          <Property Name="TeslimEdildiMi" Type="bit" />
          <Property Name="Aciklama" Type="varchar" MaxLength="100" />
        </EntityType>
        <EntityType Name="sysdiagrams">
          <Key>
            <PropertyRef Name="diagram_id" />
          </Key>
          <Property Name="name" Type="nvarchar" MaxLength="128" Nullable="false" />
          <Property Name="principal_id" Type="int" Nullable="false" />
          <Property Name="diagram_id" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="version" Type="int" />
          <Property Name="definition" Type="varbinary(max)" />
        </EntityType>
        <EntityType Name="Town">
          <Key>
            <PropertyRef Name="TownID" />
          </Key>
          <Property Name="TownID" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CityID" Type="int" Nullable="false" />
          <Property Name="TownName" Type="nvarchar" MaxLength="50" Nullable="false" />
        </EntityType>
        <Association Name="FK_District_Town">
          <End Role="Town" Type="Self.Town" Multiplicity="1" />
          <End Role="District" Type="Self.District" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Town">
              <PropertyRef Name="TownID" />
            </Principal>
            <Dependent Role="District">
              <PropertyRef Name="TownID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Musteriler_City">
          <End Role="City" Type="Self.City" Multiplicity="0..1" />
          <End Role="Musteriler" Type="Self.Musteriler" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="City">
              <PropertyRef Name="CityID" />
            </Principal>
            <Dependent Role="Musteriler">
              <PropertyRef Name="CityID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Musteriler_District">
          <End Role="District" Type="Self.District" Multiplicity="0..1" />
          <End Role="Musteriler" Type="Self.Musteriler" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="District">
              <PropertyRef Name="DistrictID" />
            </Principal>
            <Dependent Role="Musteriler">
              <PropertyRef Name="DistricID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Musteriler_Neighborhood">
          <End Role="Neighborhood" Type="Self.Neighborhood" Multiplicity="0..1" />
          <End Role="Musteriler" Type="Self.Musteriler" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Neighborhood">
              <PropertyRef Name="NeighborhoodID" />
            </Principal>
            <Dependent Role="Musteriler">
              <PropertyRef Name="NeighborhoodID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Musteriler_Town">
          <End Role="Town" Type="Self.Town" Multiplicity="0..1" />
          <End Role="Musteriler" Type="Self.Musteriler" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Town">
              <PropertyRef Name="TownID" />
            </Principal>
            <Dependent Role="Musteriler">
              <PropertyRef Name="TownID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Neighborhood_District">
          <End Role="District" Type="Self.District" Multiplicity="1" />
          <End Role="Neighborhood" Type="Self.Neighborhood" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="District">
              <PropertyRef Name="DistrictID" />
            </Principal>
            <Dependent Role="Neighborhood">
              <PropertyRef Name="DistrictID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Satislar_Musteriler1">
          <End Role="Musteriler" Type="Self.Musteriler" Multiplicity="1" />
          <End Role="Satislar" Type="Self.Satislar" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Musteriler">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="Satislar">
              <PropertyRef Name="MusteriID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Town_City">
          <End Role="City" Type="Self.City" Multiplicity="1" />
          <End Role="Town" Type="Self.Town" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="City">
              <PropertyRef Name="CityID" />
            </Principal>
            <Dependent Role="Town">
              <PropertyRef Name="CityID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="KobiPanelDBModelStoreContainer">
          <EntitySet Name="City" EntityType="Self.City" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="District" EntityType="Self.District" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Musteriler" EntityType="Self.Musteriler" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Neighborhood" EntityType="Self.Neighborhood" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Not" EntityType="Self.Not" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Rehber" EntityType="Self.Rehber" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Satislar" EntityType="Self.Satislar" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="sysdiagrams" EntityType="Self.sysdiagrams" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Town" EntityType="Self.Town" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Araclar" EntityType="Self.Araclar" store:Type="Tables" store:Schema="dbo">
            <DefiningQuery>SELECT 
    [Araclar].[ID] AS [ID], 
    [Araclar].[Plaka] AS [Plaka]
    FROM [dbo].[Araclar] AS [Araclar]</DefiningQuery>
          </EntitySet>
          <AssociationSet Name="FK_District_Town" Association="Self.FK_District_Town">
            <End Role="Town" EntitySet="Town" />
            <End Role="District" EntitySet="District" />
          </AssociationSet>
          <AssociationSet Name="FK_Musteriler_City" Association="Self.FK_Musteriler_City">
            <End Role="City" EntitySet="City" />
            <End Role="Musteriler" EntitySet="Musteriler" />
          </AssociationSet>
          <AssociationSet Name="FK_Musteriler_District" Association="Self.FK_Musteriler_District">
            <End Role="District" EntitySet="District" />
            <End Role="Musteriler" EntitySet="Musteriler" />
          </AssociationSet>
          <AssociationSet Name="FK_Musteriler_Neighborhood" Association="Self.FK_Musteriler_Neighborhood">
            <End Role="Neighborhood" EntitySet="Neighborhood" />
            <End Role="Musteriler" EntitySet="Musteriler" />
          </AssociationSet>
          <AssociationSet Name="FK_Musteriler_Town" Association="Self.FK_Musteriler_Town">
            <End Role="Town" EntitySet="Town" />
            <End Role="Musteriler" EntitySet="Musteriler" />
          </AssociationSet>
          <AssociationSet Name="FK_Neighborhood_District" Association="Self.FK_Neighborhood_District">
            <End Role="District" EntitySet="District" />
            <End Role="Neighborhood" EntitySet="Neighborhood" />
          </AssociationSet>
          <AssociationSet Name="FK_Satislar_Musteriler1" Association="Self.FK_Satislar_Musteriler1">
            <End Role="Musteriler" EntitySet="Musteriler" />
            <End Role="Satislar" EntitySet="Satislar" />
          </AssociationSet>
          <AssociationSet Name="FK_Town_City" Association="Self.FK_Town_City">
            <End Role="City" EntitySet="City" />
            <End Role="Town" EntitySet="Town" />
          </AssociationSet>
        </EntityContainer>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="KobiPanelDBModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="City">
          <Key>
            <PropertyRef Name="CityID" />
          </Key>
          <Property Name="CityID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CityName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="PlateNo" Type="String" MaxLength="2" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="PhoneCode" Type="String" MaxLength="7" FixedLength="false" Unicode="true" Nullable="false" />
          <NavigationProperty Name="Musteriler" Relationship="Self.FK_Musteriler_City" FromRole="City" ToRole="Musteriler" />
          <NavigationProperty Name="Town" Relationship="Self.FK_Town_City" FromRole="City" ToRole="Town" />
        </EntityType>
        <EntityType Name="District">
          <Key>
            <PropertyRef Name="DistrictID" />
          </Key>
          <Property Name="DistrictID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="TownID" Type="Int32" Nullable="false" />
          <Property Name="DistrictName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <NavigationProperty Name="Town" Relationship="Self.FK_District_Town" FromRole="District" ToRole="Town" />
          <NavigationProperty Name="Musteriler" Relationship="Self.FK_Musteriler_District" FromRole="District" ToRole="Musteriler" />
          <NavigationProperty Name="Neighborhood" Relationship="Self.FK_Neighborhood_District" FromRole="District" ToRole="Neighborhood" />
        </EntityType>
        <EntityType Name="Musteriler">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="ad" Type="String" MaxLength="50" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="soyad" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="tel" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="yas" Type="Int16" />
          <Property Name="CityID" Type="Int32" />
          <Property Name="TownID" Type="Int32" />
          <Property Name="DistricID" Type="Int32" />
          <Property Name="NeighborhoodID" Type="Int32" />
          <Property Name="KucukBasHayvanSayisi" Type="Int32" />
          <Property Name="BuyukBasHayvanSayisi" Type="Int32" />
          <NavigationProperty Name="City" Relationship="Self.FK_Musteriler_City" FromRole="Musteriler" ToRole="City" />
          <NavigationProperty Name="District" Relationship="Self.FK_Musteriler_District" FromRole="Musteriler" ToRole="District" />
          <NavigationProperty Name="Neighborhood" Relationship="Self.FK_Musteriler_Neighborhood" FromRole="Musteriler" ToRole="Neighborhood" />
          <NavigationProperty Name="Town" Relationship="Self.FK_Musteriler_Town" FromRole="Musteriler" ToRole="Town" />
          <NavigationProperty Name="Satislar" Relationship="Self.FK_Satislar_Musteriler" FromRole="Musteriler" ToRole="Satislar" />
        </EntityType>
        <EntityType Name="Neighborhood">
          <Key>
            <PropertyRef Name="NeighborhoodID" />
          </Key>
          <Property Name="NeighborhoodID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="DistrictID" Type="Int32" Nullable="false" />
          <Property Name="NeighborhoodName" Type="String" MaxLength="100" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ZipCode" Type="String" MaxLength="20" FixedLength="false" Unicode="true" Nullable="false" />
          <NavigationProperty Name="District" Relationship="Self.FK_Neighborhood_District" FromRole="Neighborhood" ToRole="District" />
          <NavigationProperty Name="Musteriler" Relationship="Self.FK_Musteriler_Neighborhood" FromRole="Neighborhood" ToRole="Musteriler" />
        </EntityType>
        <EntityType Name="Not">
          <Key>
            <PropertyRef Name="id" />
          </Key>
          <Property Name="id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="konu" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="aciklama" Type="String" MaxLength="Max" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="Rehber">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" />
          <Property Name="AdSoyad" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="il" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="ilce" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="koy" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="adres" Type="String" MaxLength="150" FixedLength="false" Unicode="false" />
          <Property Name="KBHayvanSayisi" Type="Int32" />
          <Property Name="BBHayvanSayisi" Type="Int32" />
          <Property Name="Telefon" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
        </EntityType>
        <EntityType Name="Satislar">
          <Key>
            <PropertyRef Name="SatisID" />
          </Key>
          <Property Name="SatisID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="MusteriID" Type="Int32" Nullable="false" />
          <Property Name="Tutar" Type="Decimal" Precision="10" Scale="4" Nullable="false" />
          <Property Name="ServisMi" Type="Boolean" Nullable="false" />
          <Property Name="SatisTarihi" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="TeslimEdildiMi" Type="Boolean" />
          <Property Name="Aciklama" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="Musteriler" Relationship="Self.FK_Satislar_Musteriler" FromRole="Satislar" ToRole="Musteriler" />
        </EntityType>
        <EntityType Name="sysdiagrams">
          <Key>
            <PropertyRef Name="diagram_id" />
          </Key>
          <Property Name="name" Type="String" MaxLength="128" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="principal_id" Type="Int32" Nullable="false" />
          <Property Name="diagram_id" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="version" Type="Int32" />
          <Property Name="definition" Type="Binary" MaxLength="Max" FixedLength="false" />
        </EntityType>
        <EntityType Name="Town">
          <Key>
            <PropertyRef Name="TownID" />
          </Key>
          <Property Name="TownID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CityID" Type="Int32" Nullable="false" />
          <Property Name="TownName" Type="String" MaxLength="50" FixedLength="false" Unicode="true" Nullable="false" />
          <NavigationProperty Name="City" Relationship="Self.FK_Town_City" FromRole="Town" ToRole="City" />
          <NavigationProperty Name="District" Relationship="Self.FK_District_Town" FromRole="Town" ToRole="District" />
          <NavigationProperty Name="Musteriler" Relationship="Self.FK_Musteriler_Town" FromRole="Town" ToRole="Musteriler" />
        </EntityType>
        <EntityType Name="Araclar">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Plaka" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
        </EntityType>
        <Association Name="FK_Musteriler_City">
          <End Role="City" Type="Self.City" Multiplicity="0..1" />
          <End Role="Musteriler" Type="Self.Musteriler" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="City">
              <PropertyRef Name="CityID" />
            </Principal>
            <Dependent Role="Musteriler">
              <PropertyRef Name="CityID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Town_City">
          <End Role="City" Type="Self.City" Multiplicity="1" />
          <End Role="Town" Type="Self.Town" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="City">
              <PropertyRef Name="CityID" />
            </Principal>
            <Dependent Role="Town">
              <PropertyRef Name="CityID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_District_Town">
          <End Role="Town" Type="Self.Town" Multiplicity="1" />
          <End Role="District" Type="Self.District" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Town">
              <PropertyRef Name="TownID" />
            </Principal>
            <Dependent Role="District">
              <PropertyRef Name="TownID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Musteriler_District">
          <End Role="District" Type="Self.District" Multiplicity="0..1" />
          <End Role="Musteriler" Type="Self.Musteriler" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="District">
              <PropertyRef Name="DistrictID" />
            </Principal>
            <Dependent Role="Musteriler">
              <PropertyRef Name="DistricID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Neighborhood_District">
          <End Role="District" Type="Self.District" Multiplicity="1" />
          <End Role="Neighborhood" Type="Self.Neighborhood" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="District">
              <PropertyRef Name="DistrictID" />
            </Principal>
            <Dependent Role="Neighborhood">
              <PropertyRef Name="DistrictID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Musteriler_Neighborhood">
          <End Role="Neighborhood" Type="Self.Neighborhood" Multiplicity="0..1" />
          <End Role="Musteriler" Type="Self.Musteriler" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Neighborhood">
              <PropertyRef Name="NeighborhoodID" />
            </Principal>
            <Dependent Role="Musteriler">
              <PropertyRef Name="NeighborhoodID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Musteriler_Town">
          <End Role="Town" Type="Self.Town" Multiplicity="0..1" />
          <End Role="Musteriler" Type="Self.Musteriler" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Town">
              <PropertyRef Name="TownID" />
            </Principal>
            <Dependent Role="Musteriler">
              <PropertyRef Name="TownID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_Satislar_Musteriler">
          <End Role="Musteriler" Type="Self.Musteriler" Multiplicity="1" />
          <End Role="Satislar" Type="Self.Satislar" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Musteriler">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="Satislar">
              <PropertyRef Name="MusteriID" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="KobiPanelDBEntities" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="City" EntityType="Self.City" />
          <EntitySet Name="District" EntityType="Self.District" />
          <EntitySet Name="Musteriler" EntityType="Self.Musteriler" />
          <EntitySet Name="Neighborhood" EntityType="Self.Neighborhood" />
          <EntitySet Name="Not" EntityType="Self.Not" />
          <EntitySet Name="Rehber" EntityType="Self.Rehber" />
          <EntitySet Name="Satislar" EntityType="Self.Satislar" />
          <EntitySet Name="sysdiagrams" EntityType="Self.sysdiagrams" />
          <EntitySet Name="Town" EntityType="Self.Town" />
          <EntitySet Name="Araclar" EntityType="Self.Araclar" />
          <AssociationSet Name="FK_Musteriler_City" Association="Self.FK_Musteriler_City">
            <End Role="City" EntitySet="City" />
            <End Role="Musteriler" EntitySet="Musteriler" />
          </AssociationSet>
          <AssociationSet Name="FK_Town_City" Association="Self.FK_Town_City">
            <End Role="City" EntitySet="City" />
            <End Role="Town" EntitySet="Town" />
          </AssociationSet>
          <AssociationSet Name="FK_District_Town" Association="Self.FK_District_Town">
            <End Role="Town" EntitySet="Town" />
            <End Role="District" EntitySet="District" />
          </AssociationSet>
          <AssociationSet Name="FK_Musteriler_District" Association="Self.FK_Musteriler_District">
            <End Role="District" EntitySet="District" />
            <End Role="Musteriler" EntitySet="Musteriler" />
          </AssociationSet>
          <AssociationSet Name="FK_Neighborhood_District" Association="Self.FK_Neighborhood_District">
            <End Role="District" EntitySet="District" />
            <End Role="Neighborhood" EntitySet="Neighborhood" />
          </AssociationSet>
          <AssociationSet Name="FK_Musteriler_Neighborhood" Association="Self.FK_Musteriler_Neighborhood">
            <End Role="Neighborhood" EntitySet="Neighborhood" />
            <End Role="Musteriler" EntitySet="Musteriler" />
          </AssociationSet>
          <AssociationSet Name="FK_Musteriler_Town" Association="Self.FK_Musteriler_Town">
            <End Role="Town" EntitySet="Town" />
            <End Role="Musteriler" EntitySet="Musteriler" />
          </AssociationSet>
          <AssociationSet Name="FK_Satislar_Musteriler" Association="Self.FK_Satislar_Musteriler">
            <End Role="Musteriler" EntitySet="Musteriler" />
            <End Role="Satislar" EntitySet="Satislar" />
          </AssociationSet>
        </EntityContainer>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="KobiPanelDBModelStoreContainer" CdmEntityContainer="KobiPanelDBEntities">
          <EntitySetMapping Name="City">
            <EntityTypeMapping TypeName="KobiPanelDBModel.City">
              <MappingFragment StoreEntitySet="City">
                <ScalarProperty Name="CityID" ColumnName="CityID" />
                <ScalarProperty Name="CityName" ColumnName="CityName" />
                <ScalarProperty Name="PlateNo" ColumnName="PlateNo" />
                <ScalarProperty Name="PhoneCode" ColumnName="PhoneCode" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="District">
            <EntityTypeMapping TypeName="KobiPanelDBModel.District">
              <MappingFragment StoreEntitySet="District">
                <ScalarProperty Name="DistrictID" ColumnName="DistrictID" />
                <ScalarProperty Name="TownID" ColumnName="TownID" />
                <ScalarProperty Name="DistrictName" ColumnName="DistrictName" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Musteriler">
            <EntityTypeMapping TypeName="KobiPanelDBModel.Musteriler">
              <MappingFragment StoreEntitySet="Musteriler">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="ad" ColumnName="ad" />
                <ScalarProperty Name="soyad" ColumnName="soyad" />
                <ScalarProperty Name="tel" ColumnName="tel" />
                <ScalarProperty Name="yas" ColumnName="yas" />
                <ScalarProperty Name="CityID" ColumnName="CityID" />
                <ScalarProperty Name="TownID" ColumnName="TownID" />
                <ScalarProperty Name="DistricID" ColumnName="DistricID" />
                <ScalarProperty Name="NeighborhoodID" ColumnName="NeighborhoodID" />
                <ScalarProperty Name="KucukBasHayvanSayisi" ColumnName="KucukBasHayvanSayisi" />
                <ScalarProperty Name="BuyukBasHayvanSayisi" ColumnName="BuyukBasHayvanSayisi" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Neighborhood">
            <EntityTypeMapping TypeName="KobiPanelDBModel.Neighborhood">
              <MappingFragment StoreEntitySet="Neighborhood">
                <ScalarProperty Name="NeighborhoodID" ColumnName="NeighborhoodID" />
                <ScalarProperty Name="DistrictID" ColumnName="DistrictID" />
                <ScalarProperty Name="NeighborhoodName" ColumnName="NeighborhoodName" />
                <ScalarProperty Name="ZipCode" ColumnName="ZipCode" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Not">
            <EntityTypeMapping TypeName="KobiPanelDBModel.Not">
              <MappingFragment StoreEntitySet="Not">
                <ScalarProperty Name="id" ColumnName="id" />
                <ScalarProperty Name="konu" ColumnName="konu" />
                <ScalarProperty Name="aciklama" ColumnName="aciklama" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Rehber">
            <EntityTypeMapping TypeName="KobiPanelDBModel.Rehber">
              <MappingFragment StoreEntitySet="Rehber">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="AdSoyad" ColumnName="AdSoyad" />
                <ScalarProperty Name="il" ColumnName="il" />
                <ScalarProperty Name="ilce" ColumnName="ilce" />
                <ScalarProperty Name="koy" ColumnName="koy" />
                <ScalarProperty Name="adres" ColumnName="adres" />
                <ScalarProperty Name="KBHayvanSayisi" ColumnName="KBHayvanSayisi" />
                <ScalarProperty Name="BBHayvanSayisi" ColumnName="BBHayvanSayisi" />
                <ScalarProperty Name="Telefon" ColumnName="Telefon" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Satislar">
            <EntityTypeMapping TypeName="KobiPanelDBModel.Satislar">
              <MappingFragment StoreEntitySet="Satislar">
                <ScalarProperty Name="SatisID" ColumnName="SatisID" />
                <ScalarProperty Name="MusteriID" ColumnName="MusteriID" />
                <ScalarProperty Name="Tutar" ColumnName="Tutar" />
                <ScalarProperty Name="ServisMi" ColumnName="ServisMi" />
                <ScalarProperty Name="SatisTarihi" ColumnName="SatisTarihi" />
                <ScalarProperty Name="TeslimEdildiMi" ColumnName="TeslimEdildiMi" />
                <ScalarProperty Name="Aciklama" ColumnName="Aciklama" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="sysdiagrams">
            <EntityTypeMapping TypeName="KobiPanelDBModel.sysdiagrams">
              <MappingFragment StoreEntitySet="sysdiagrams">
                <ScalarProperty Name="name" ColumnName="name" />
                <ScalarProperty Name="principal_id" ColumnName="principal_id" />
                <ScalarProperty Name="diagram_id" ColumnName="diagram_id" />
                <ScalarProperty Name="version" ColumnName="version" />
                <ScalarProperty Name="definition" ColumnName="definition" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Town">
            <EntityTypeMapping TypeName="KobiPanelDBModel.Town">
              <MappingFragment StoreEntitySet="Town">
                <ScalarProperty Name="TownID" ColumnName="TownID" />
                <ScalarProperty Name="CityID" ColumnName="CityID" />
                <ScalarProperty Name="TownName" ColumnName="TownName" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Araclar">
            <EntityTypeMapping TypeName="KobiPanelDBModel.Araclar">
              <MappingFragment StoreEntitySet="Araclar">
                <ScalarProperty Name="ID" ColumnName="ID" />
                <ScalarProperty Name="Plaka" ColumnName="Plaka" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="True" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>