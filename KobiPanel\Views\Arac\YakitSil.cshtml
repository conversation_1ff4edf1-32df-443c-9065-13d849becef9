﻿@model KobiPanel.Models.Vehicles.AlinanYakitlar

@{
    ViewBag.Title = "Yakıt Kaydı Silme İşlemi";
    ViewBag.PageHeader = "Yakıt Kaydı Silme İşlemi";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<h3>Bunu Silmek istediğinize emin misiniz?</h3>
<div>
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.Araclar.Plaka)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Araclar.Plaka)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.AracID)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.AracID)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.YakitTutari)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.YakitTutari)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.YakitinAlindigiYer)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.YakitinAlindigiYer)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.AlisTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.AlisTarihi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Litre)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Litre)
        </dd>

    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Delete" class="btn btn-default" /> |
            @Html.ActionLink("Back to List", "YakitAnasayfa")
        </div>
    }
</div>
