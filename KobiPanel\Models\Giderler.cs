namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Data.Entity.Spatial;

    [Table("Giderler")]
    public class Giderler
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int id { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessage = "Konu alanı gereklidir.")]
        [StringLength(30)]
        public string Konu { get; set; }

        [Required(ErrorMessage = "Tutar alanı gereklidir.")]
        public decimal? Tutar { get; set; }

        public DateTime Tarih { get; set; }
    }
}
