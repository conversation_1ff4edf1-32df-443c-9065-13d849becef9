//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("Musteriler")]
    public class Musteriler
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Musteriler()
        {
            this.Satislar = new HashSet<Satislar>();
        }

        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }
        public string ad { get; set; }
        public string soyad { get; set; }
        public string tel { get; set; }
        public Nullable<short> yas { get; set; }
        public Nullable<int> CityID { get; set; }
        public Nullable<int> TownID { get; set; }
        public Nullable<int> DistricID { get; set; }
        public Nullable<int> NeighborhoodID { get; set; }
        public Nullable<int> KucukBasHayvanSayisi { get; set; }
        public Nullable<int> BuyukBasHayvanSayisi { get; set; }
    
        public virtual City City { get; set; }
        public virtual District District { get; set; }
        public virtual Neighborhood Neighborhood { get; set; }
        public virtual Town Town { get; set; }
        
        public virtual ICollection<Satislar> Satislar { get; set; }
    }
}
