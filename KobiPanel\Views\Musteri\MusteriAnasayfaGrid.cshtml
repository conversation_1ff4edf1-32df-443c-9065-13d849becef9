﻿@using GridMvc.Html
@model IEnumerable<KobiPanel.Models.Musteriler>
@{

    ViewBag.Title = "Müşteri Listesi";
    Layout = "~/Views/Shared/_LayoutBos.cshtml";
}

<h2 style="text-align:center">Müşteri Listesi</h2>


<div>
    @Html.Grid(Model).Columns(col =>
{
    col.Add(x => x.adsoyad).Titled("Ad Soyad").Filterable(true).Sortable(true);

    col.Add(x => x.Town.TownName).Titled("İlçe").Sortable(true);
    col.Add(x => x.Neighborhood.NeighborhoodName).Titled("Mahalle").Sortable(true);
    col.Add(x => x.tel).RenderValueAs(x => x.tel).Titled("Telefon").Format("{0:(###) ### ## ##}").Filterable(true);
    col.Add(x => x.kayitTarihi).Titled("<PERSON>ı<PERSON> Tarihi");
    //col.Add(x => x.<PERSON>).Titled("Satış Tarihi").Sortable(true).Filterable(true);
    //col.Add(x => x.TeslimEdildiMi).Titled("Teslim Durumu").Sortable(true).Filterable(true);
    //col.Add(x => x.ServisMi).RenderValueAs(x => (x.ServisMi) ? "Adrese Teslim" : "Yerinde Teslim").Titled("Teslim Şekli").Sortable(true);


    }).WithPaging(12).SetLanguage("tr")
</div>


