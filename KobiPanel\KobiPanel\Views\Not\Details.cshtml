﻿@model KobiPanel.Models.Not

@{
    ViewBag.Title = "Details";
}

<h2>Details</h2>

<div>
    <h4>Not</h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.aciklama)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.aciklama)
        </dd>

    </dl>
</div>
<p>
    @Html.ActionLink("Edit", "Edit", new { id = Model.konu }) |
    @Html.ActionLink("Back to List", "Index")
</p>
