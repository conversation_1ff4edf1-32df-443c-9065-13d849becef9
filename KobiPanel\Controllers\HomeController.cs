﻿using KobiPanel.Helpers;
using KobiPanel.Models;
using KobiPanel.Models.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Security;
using System.Data.Entity;

namespace KobiPanel.Controllers
{


    public class HomeController : Controller
    {
        KobiPanelDbContext db = new KobiPanelDbContext();
        // GET: Home


        public ActionResult Index()
        {
            ViewBag.PageHeader = "Aydın Silaj Yönetim Paneli";

            int satissayisi = 0;

            satissayisi = db.Satislar.Count();

            ViewBag.MusteriSayisi = db.Musteriler.Count();
            ViewBag.SatisSayisi = satissayisi;
            ViewBag.UrunSayisi = db.Urun.Count();
            ViewBag.GiderSayisi = db.Giderler.Count();
            ViewBag.NotSayisi = db.Not.Count();

            var satislar = db.Satislar.ToList();

            decimal toplamtutar = 0;
            decimal SatilanKucukTutar = 0;
            int SatilanKucukAdet = 0;
            decimal SatilanBuyukTutar = 0;
            int SatilanBuyukAdet = 0;

            int SatilanSamanAdet = 0;

            decimal SatilanSamanTutar = 0;

            foreach (var item in satislar)
            {
                toplamtutar = toplamtutar + item.Tutar;
                if (item.UrunID == 5)
                {
                    SatilanKucukAdet = SatilanKucukAdet + item.UrunAdeti;
                    SatilanKucukTutar = SatilanKucukTutar + item.Tutar;
                }
                if (item.UrunID == 6)
                {
                    SatilanBuyukAdet = SatilanBuyukAdet + item.UrunAdeti;
                    SatilanBuyukTutar = SatilanBuyukTutar + item.Tutar;

                }
                if (item.UrunID == 7)
                {
                    SatilanSamanTutar = SatilanSamanTutar + item.Tutar;
                    SatilanSamanAdet = SatilanSamanAdet + item.UrunAdeti;
                }

            }
            ViewBag.SatilanSamanAdet = SatilanSamanAdet;
            ViewBag.SatilanSamanTutar = SatilanSamanTutar.ToString("0.##");


            ViewBag.SatilanKucukAdet = SatilanKucukAdet;
            ViewBag.SatilanKucukTutar = SatilanKucukTutar.ToString("0.##");
            ViewBag.SatilanBuyukAdet = SatilanBuyukAdet;
            ViewBag.SatilanBuyukTutar = SatilanBuyukTutar.ToString("0.##");

            ViewBag.ToplamTutar = toplamtutar;

            decimal gidertoplami = 0;

            foreach (var item in db.Giderler.ToList())
            {
                if (item.Tutar.HasValue)
                {
                    gidertoplami = gidertoplami + item.Tutar.Value;
                }
            }

            ViewBag.GiderToplami = gidertoplami.ToString("0.##");

            if (satissayisi > 0)
            {
                ViewBag.OrtSatisTutari = (toplamtutar / satissayisi).ToString("0.##");
            }
            else
            {
                ViewBag.OrtSatisTutari = "0";
            }


            return View();
        }


        [HttpGet]
        [AllowAnonymous]
        public ActionResult Login()
        {

            return View();
        }

        [HttpPost]
        [AllowAnonymous,ValidateAntiForgeryToken]
        public ActionResult Login(LoginViewModel model,string ReturnUrl)
        {
            if (model == null)
            {
                ModelState.AddModelError("", "Geçersiz giriş bilgileri");
                return View();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var u = db.Kullanicilar.FirstOrDefault(m => m.KullaniciAdi == model.KullaniciAdi && m.Sifre == model.Sifre);
                    if (u != null)
                    {
                        FormsAuthentication.SetAuthCookie(model.KullaniciAdi, true);
                        Session["User"] = u; // Session'a kullanıcı bilgisini ekle

                        return string.IsNullOrEmpty(ReturnUrl) ? RedirectToAction("MusteriAnasayfa", "Musteri") : (ActionResult)Redirect(ReturnUrl);
                    }
                    else
                    {
                        ModelState.AddModelError("", "Kullanıcı adı veya şifre yanlış");
                    }
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "Giriş işlemi sırasında bir hata oluştu: " + ex.Message);
                }
            }
            return View(model);
        }


        public ActionResult LogOff()
        {
            try
            {
                FormsAuthentication.SignOut();
                if (Session != null)
                {
                    Session.Clear();
                    Session.Abandon();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("LogOff hatası: " + ex.Message);
            }

            return RedirectToAction("Login");
        }

        public ActionResult GunOzeti()
        {
            var gunluksatislar = db.Satislar.Where(m => m.SatisTarihi.Day == DateTime.Now.Day).ToList();
            var gunlukgiderler = db.Giderler.Where(m => m.Tarih.Day == DateTime.Now.Day).ToList();

            decimal gunlukgelir =0;
            decimal gunlukgider =0;

            foreach (var item in gunluksatislar)
            {
                gunlukgelir = gunlukgelir + item.Tutar;
            }

            foreach (var item in gunlukgiderler)
            {
                if (item.Tutar.HasValue)
                {
                    gunlukgider = gunlukgider + item.Tutar.Value;
                }
            }



            ViewBag.GunlukSatis = gunluksatislar;
            ViewBag.GunlukGider = gunlukgiderler;

            return View();
        }


    }
}