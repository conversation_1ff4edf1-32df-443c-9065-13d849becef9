﻿using KobiPanel.Helpers;
using KobiPanel.Models;
using KobiPanel.Models.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Security;
using System.Data.Entity;

namespace KobiPanel.Controllers
{

    
    public class HomeController : Controller
    {
        KobiPanelDbContext db = new KobiPanelDbContext();
        // GET: Home


        public ActionResult Index()
        {
            ViewBag.PageHeader = "Aydın Silaj Yönetim Paneli";

            int satissayisi = 0;

            satissayisi = db.Satislar.Count();

            ViewBag.MusteriSayisi = db.Musteriler.Count();
            ViewBag.SatisSayisi = satissayisi;
            ViewBag.UrunSayisi = db.Urun.Count();
            ViewBag.GiderSayisi = db.Giderler.Count();
            ViewBag.NotSayisi = db.Not.Count();

            var satislar = db.Satislar.ToList();

            decimal toplamtutar = 0;
            decimal SatilanKucukTutar = 0;
            int SatilanKucukAdet = 0;
            decimal SatilanBuyukTutar = 0;
            int SatilanBuyukAdet = 0;
            
            int SatilanSamanAdet = 0;

            decimal SatilanSamanTutar = 0;

            foreach (var item in satislar)
            {
                toplamtutar = toplamtutar + item.Tutar;
                if (item.UrunID == 5)
                {
                    SatilanKucukAdet = SatilanKucukAdet + item.UrunAdeti;
                    SatilanKucukTutar = SatilanKucukTutar + item.Tutar;
                }
                if (item.UrunID == 6)
                {
                    SatilanBuyukAdet = SatilanBuyukAdet + item.UrunAdeti;
                    SatilanBuyukTutar = SatilanBuyukTutar + item.Tutar;

                }
                if (item.UrunID == 7)
                {
                    SatilanSamanTutar = SatilanSamanTutar + item.Tutar;
                    SatilanSamanAdet = SatilanSamanAdet + item.UrunAdeti;
                }
                
            }
            ViewBag.SatilanSamanAdet = SatilanSamanAdet;
            ViewBag.SatilanSamanTutar = SatilanSamanTutar.ToString("0.##");


            ViewBag.SatilanKucukAdet = SatilanKucukAdet;
            ViewBag.SatilanKucukTutar = SatilanKucukTutar.ToString("0.##");
            ViewBag.SatilanBuyukAdet = SatilanBuyukAdet;
            ViewBag.SatilanBuyukTutar = SatilanBuyukTutar.ToString("0.##");

            ViewBag.ToplamTutar = toplamtutar;

            decimal gidertoplami = 0;

            foreach (var item in db.Giderler.ToList())
            {
                gidertoplami = gidertoplami + item.Tutar;
            }

            ViewBag.GiderToplami = gidertoplami.ToString("0.##");

            if (satissayisi>0 )
            {
                ViewBag.OrtSatisTutari = (toplamtutar / satissayisi).ToString("0.##");
            }

            
            return View();
        }


        [HttpGet]
        [AllowAnonymous]
        public ActionResult Login()
        {

            return View();
        }

        [HttpPost]
        [AllowAnonymous,ValidateAntiForgeryToken]
        public ActionResult Login(LoginViewModel model,string ReturnUrl)
        {

            if (ModelState.IsValid)
            {
                var u = db.Kullanicilar.FirstOrDefault(m => m.KullaniciAdi == model.KullaniciAdi && m.Sifre == model.Sifre);
                if (u!=null)
                {
                    FormsAuthentication.SetAuthCookie(model.KullaniciAdi, true);

                    return ReturnUrl == null ? RedirectToAction("MusteriAnasayfa", "Musteri") : (ActionResult)Redirect(ReturnUrl);
                }
                else
                {
                   ModelState.AddModelError("","Kullanıcı adı veya şifre yanlış");
                }
               
            }
            return View(model);
        }


        public ActionResult LogOff()
        {
            FormsAuthentication.SignOut();
            return RedirectToAction("Login");
        }

        public ActionResult GunOzeti()
        {
            var gunluksatislar = db.Satislar.Where(m => m.SatisTarihi.Day == DateTime.Now.Day).ToList();
            var gunlukgiderler = db.Giderler.Where(m => m.Tarih.Day == DateTime.Now.Day).ToList();

            decimal gunlukgelir =0;
            decimal gunlukgider =0;

            foreach (var item in gunluksatislar)
            {
                gunlukgelir = gunlukgelir + item.Tutar;
            }

            foreach (var item in gunlukgiderler)
            {
                gunlukgider = gunlukgider + item.Tutar;
            }



            ViewBag.GunlukSatis = gunluksatislar;
            ViewBag.GunlukGider = gunlukgiderler;

            return View();
        }

        
    }
}