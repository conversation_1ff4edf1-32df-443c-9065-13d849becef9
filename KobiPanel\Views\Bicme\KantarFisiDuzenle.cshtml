﻿@model KobiPanel.Models.Bicme.KantarFisi

@{
    ViewBag.Title = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>Edit</h2>

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        <h4>KantarFisi</h4>
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        @Html.HiddenFor(model => model.FisID)

        <div class="form-group">
            @Html.LabelFor(model => model.TarlaID, "TarlaID", htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownList("TarlaID", null, htmlAttributes: new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.TarlaID, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.birinciTartim, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.birinciTartim, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.birinciTartim, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.ikinciTartim, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.ikinciTartim, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.ikinciTartim, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.NetKg, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.NetKg, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.NetKg, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Plaka, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Plaka, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Plaka, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.aciklama, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.aciklama, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.aciklama, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.TartimTarihi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.TartimTarihi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.TartimTarihi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Save" class="btn btn-default" />
            </div>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Back to List", "Index")
</div>
