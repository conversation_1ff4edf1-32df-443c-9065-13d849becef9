namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Data.Entity.Spatial;

    [Table("Town")]
    public class Town
    {

        public int TownID { get; set; }

        public int CityID { get; set; }
        [DisplayName("Şehir")]
        public string TownName { get; set; }

        public virtual City City { get; set; }

    }
}
