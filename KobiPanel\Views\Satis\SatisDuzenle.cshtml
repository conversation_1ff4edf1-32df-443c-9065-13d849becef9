﻿@model KobiPanel.Models.Satislar

@{
    ViewBag.Title = "SatisDuzenle";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>SatisDuzenle</h2>

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    
<div class="form-horizontal">
    <hr />
    <div>
        <label class="mdb-main-label">Müşteri</label>
        @Html.EditorFor(m=>m.Musteriler.adsoyad,new { htmlAttributes = new {@class="form-control disabled" } })
    </div>

    <div class="row">
        <div class="col-md-6">
            <div>
                @Html.DropDownListFor(m => m.UrunID, (SelectList)ViewBag.SListUrunler, "Ürün Seçiniz", new { @class = "mdb-select md-form", searchable = "Ürün Ara", aria_invalid = "false", data_live_search = "true", lang = "tr" })
            </div>
        </div>
        <div class="col-md-6">
            <div class="row">
                <div class="col-md-12">
                    <div class="md-form">
                        @Html.EditorFor(model => model.UrunAdeti, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.LabelFor(model => model.UrunAdeti, htmlAttributes: new { @class = "" })
                        @Html.ValidationMessageFor(model => model.UrunAdeti, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>
    </div>

    @Html.HiddenFor(model=>model.MusteriID)
    @Html.HiddenFor(model=>model.SatisTarihi)
    @Html.HiddenFor(model=>model.SatisID)

    <div class="row">
        <div class="col-md-12">
            <div class="alert alert-success alert-dismissible" role="alert">
                Tutar otomatik olarak hesaplanacaktır.
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">

            <div class="md-form">
                @Html.EditorFor(model => model.SatisTarihi, new { htmlAttributes = new { @class = "form-control", type = "date" } })
                @Html.LabelFor(model => model.SatisTarihi, htmlAttributes: new { @class = "active" })
                @Html.ValidationMessageFor(model => model.SatisTarihi, "", new { @class = "text-danger" })
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group" style="padding-left: 1em;">
                @Html.LabelFor(m => m.ServisMi)
                <div class="switch">
                    <label>
                        Yerinde Teslim
                        <input type="checkbox" onchange="$('#@Html.IdFor(m=>m.ServisMi)').val($(this).prop('checked'));"@(Model.ServisMi?"checked":"")>
                        <span class="lever"></span> Adrese Teslim <br>
                        @Html.HiddenFor(m => m.ServisMi)
                    </label>
                </div>
                @Html.ValidationMessageFor(model => model.ServisMi, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group" style="padding-left: 1em;">
                @Html.LabelFor(m => m.TeslimEdildiMi)
                <div class="switch">
                    <label>
                        Teslim<i style="color:red"> Edilmedi</i>
                        <input type="checkbox" onchange="$('#@Html.IdFor(m=>m.TeslimEdildiMi)').val($(this).prop('checked'));"@(Model.TeslimEdildiMi?"checked=true":"")>
                        <span class="lever"></span> Teslim <i style="color:green"> Edildi</i> <br>
                        @Html.HiddenFor(m => m.TeslimEdildiMi)
                    </label>
                </div>
                @Html.ValidationMessageFor(model => model.TeslimEdildiMi, "", new { @class = "text-danger" })
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="md-form">
                @Html.TextAreaFor(model => model.Aciklama, new { @class = "md-textarea form-control" })
                @Html.LabelFor(model => model.Aciklama, htmlAttributes: new { @class = "" })
                @Html.ValidationMessageFor(model => model.Aciklama, "", new { @class = "text-danger" })
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <button type="submit" class="btn btn-light-green btn-lg btn-block btn-rounded">Kaydet</button>
        </div>
    </div>
</div>
}

<div>
    @Html.ActionLink("Back to List", "Index")
</div>
