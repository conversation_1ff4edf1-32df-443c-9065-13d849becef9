﻿<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{9694393C-7E4B-4D02-AE7E-AC507ACEF093}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>KobiPanel</RootNamespace>
    <AssemblyName>KobiPanel</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <WebGreaseLibPath>..\packages\WebGrease.1.5.2\lib</WebGreaseLibPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <RunCodeAnalysis>true</RunCodeAnalysis>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime, Version=3.4.1.9004, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\packages\Antlr.3.4.1.9004\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Mvc.RazorTools, Version=1.0.5.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Mvc.RazorTools.Base.1.0.5\lib\net45\Mvc.RazorTools.dll</HintPath>
    </Reference>
    <Reference Include="Mvc.RazorTools.FontAwesome, Version=4.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Mvc.RazorTools.FontAwesome.4.3.0.0\lib\net45\Mvc.RazorTools.FontAwesome.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=4.5.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.5.0.4\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization, Version=1.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.0.6\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease, Version=1.5.2.14234, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WebGrease.1.5.2\lib\WebGrease.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Web.Infrastructure">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\css\addons-pro\cards-extended.css" />
    <Content Include="Content\css\addons-pro\cards-extended.min.css" />
    <Content Include="Content\css\addons-pro\chat.css" />
    <Content Include="Content\css\addons-pro\chat.min.css" />
    <Content Include="Content\css\addons-pro\stepper.css" />
    <Content Include="Content\css\addons-pro\stepper.min.css" />
    <Content Include="Content\css\addons-pro\timeline.css" />
    <Content Include="Content\css\addons-pro\timeline.min.css" />
    <Content Include="Content\css\addons\datatables.css" />
    <Content Include="Content\css\addons\datatables.min.css" />
    <Content Include="Content\css\all.css" />
    <Content Include="Content\css\bootstrap-material-datetimepicker.css" />
    <Content Include="Content\css\bootstrap-select.css" />
    <Content Include="Content\css\bootstrap-select.min.css" />
    <Content Include="Content\css\bootstrap-switch.css" />
    <Content Include="Content\css\bootstrap-switch.min.css" />
    <Content Include="Content\css\bootstrap-toggle.css" />
    <Content Include="Content\css\bootstrap-toggle.min.css" />
    <Content Include="Content\css\bootstrap.css" />
    <Content Include="Content\css\bootstrap.min.css" />
    <Content Include="Content\css\bootstrap2-toggle.css" />
    <Content Include="Content\css\bootstrap2-toggle.min.css" />
    <Content Include="Content\datatable\dataTables.bootstrap4.min.css" />
    <Content Include="Content\datatable\dataTables.bootstrap4.min.js" />
    <Content Include="Content\datatable\dataTables.responsive.min.js" />
    <Content Include="Content\datatable\jquery.dataTables.min.js" />
    <Content Include="Content\datatable\responsive.bootstrap4.min.css" />
    <Content Include="Content\datatable\responsive.bootstrap4.min.js" />
    <Content Include="Migrations\il_ilce.sql" />
    <Content Include="Scripts\bootstrap-switch.js" />
    <Content Include="Scripts\bootstrap-switch.min.js" />
    <Content Include="Scripts\bootstrap-select.js" />
    <Content Include="Scripts\bootstrap-select.min.js" />
    <Content Include="Scripts\i18n\defaults-en_US.js" />
    <Content Include="Scripts\i18n\defaults-en_US.min.js" />
    <Content Include="Content\font-awesome.css" />
    <Content Include="Content\font-awesome.min.css" />
    <Content Include="Content\fonts\Material-Design-Icons.svg" />
    <Content Include="Content\img\bg.jpg" />
    <Content Include="Content\img\bg2.jpg" />
    <Content Include="Content\css\mdb.css" />
    <Content Include="Content\css\mdb.lite.css" />
    <Content Include="Content\css\mdb.lite.min.css" />
    <Content Include="Content\css\mdb.min.css" />
    <Content Include="Content\css\modules\accordion-extended.css" />
    <Content Include="Content\css\modules\accordion-extended.min.css" />
    <Content Include="Content\css\modules\animations-extended.css" />
    <Content Include="Content\css\modules\animations-extended.min.css" />
    <Content Include="Content\css\modules\charts.css" />
    <Content Include="Content\css\modules\charts.min.css" />
    <Content Include="Content\css\modules\lightbox.css" />
    <Content Include="Content\css\modules\lightbox.min.css" />
    <Content Include="Content\css\modules\megamenu.css" />
    <Content Include="Content\css\modules\megamenu.min.css" />
    <Content Include="Content\css\modules\parallax.css" />
    <Content Include="Content\css\modules\parallax.min.css" />
    <Content Include="Content\css\fontawesome.css" />
    <Content Include="Content\css\style.css" />
    <Content Include="Content\css\style.min.css" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Content\webfonts\._fa-brands-400.svg" />
    <Content Include="Content\webfonts\._fa-light-300.svg" />
    <Content Include="Content\webfonts\._fa-regular-400.svg" />
    <Content Include="Content\webfonts\._fa-solid-900.svg" />
    <Content Include="Content\webfonts\fa-brands-400.svg" />
    <Content Include="Content\webfonts\fa-light-300.svg" />
    <Content Include="Content\webfonts\fa-regular-400.svg" />
    <Content Include="Content\webfonts\fa-solid-900.svg" />
    <Content Include="fonts\fontawesome-webfont.svg" />
    <Content Include="Global.asax" />
    <Content Include="Scripts\addons-pro\stepper.js" />
    <Content Include="Scripts\addons-pro\stepper.min.js" />
    <Content Include="Scripts\addons-pro\timeline.js" />
    <Content Include="Scripts\addons\datatables.js" />
    <Content Include="Scripts\addons\datatables.min.js" />
    <Content Include="Scripts\bootstrap-toggle.js" />
    <Content Include="Scripts\bootstrap-toggle.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Content\webfonts\._fa-brands-400.eot" />
    <Content Include="Content\webfonts\._fa-brands-400.ttf" />
    <Content Include="Content\webfonts\._fa-brands-400.woff" />
    <Content Include="Content\webfonts\._fa-brands-400.woff2" />
    <Content Include="Content\webfonts\._fa-light-300.eot" />
    <Content Include="Content\webfonts\._fa-light-300.ttf" />
    <Content Include="Content\webfonts\._fa-light-300.woff" />
    <Content Include="Content\webfonts\._fa-light-300.woff2" />
    <Content Include="Content\webfonts\._fa-regular-400.eot" />
    <Content Include="Content\webfonts\._fa-regular-400.ttf" />
    <Content Include="Content\webfonts\._fa-regular-400.woff" />
    <Content Include="Content\webfonts\._fa-regular-400.woff2" />
    <Content Include="Content\webfonts\._fa-solid-900.eot" />
    <Content Include="Content\webfonts\._fa-solid-900.ttf" />
    <Content Include="Content\webfonts\._fa-solid-900.woff" />
    <Content Include="Content\webfonts\._fa-solid-900.woff2" />
    <Content Include="Content\webfonts\fa-brands-400.eot" />
    <Content Include="Content\webfonts\fa-brands-400.ttf" />
    <Content Include="Content\webfonts\fa-brands-400.woff" />
    <Content Include="Content\webfonts\fa-brands-400.woff2" />
    <Content Include="Content\webfonts\fa-light-300.eot" />
    <Content Include="Content\webfonts\fa-light-300.ttf" />
    <Content Include="Content\webfonts\fa-light-300.woff" />
    <Content Include="Content\webfonts\fa-light-300.woff2" />
    <Content Include="Content\webfonts\fa-regular-400.eot" />
    <Content Include="Content\webfonts\fa-regular-400.ttf" />
    <Content Include="Content\webfonts\fa-regular-400.woff" />
    <Content Include="Content\webfonts\fa-regular-400.woff2" />
    <Content Include="Content\webfonts\fa-solid-900.eot" />
    <Content Include="Content\webfonts\fa-solid-900.ttf" />
    <Content Include="Content\webfonts\fa-solid-900.woff" />
    <Content Include="Content\webfonts\fa-solid-900.woff2" />
    <Content Include="Content\fonts\bootstrap-material-datetimepicker.js.indir" />
    <Content Include="Content\fonts\Material-Design-Icons.eot" />
    <Content Include="Content\fonts\Material-Design-Icons.ttf" />
    <Content Include="Content\fonts\Material-Design-Icons.woff" />
    <Content Include="Content\fonts\Material-Design-Icons.woff2" />
    <Content Include="fonts\FontAwesome.otf" />
    <Content Include="fonts\fontawesome-webfont.woff" />
    <Content Include="fonts\fontawesome-webfont.ttf" />
    <Content Include="fonts\fontawesome-webfont.eot" />
    <Content Include="Scripts\bootstrap-toggle.min.js.map" />
    <Content Include="Scripts\bootstrap2-toggle.min.js.map" />
    <Content Include="Content\css\bootstrap-select.css.map" />
    <Content Include="Scripts\i18n\defaults-en_US.js.map" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <Content Include="Scripts\bootstrap-select.js.map" />
    <Content Include="Scripts\bootstrap-select.min.js.map" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <None Include="Scripts\jquery-3.3.1.intellisense.js" />
    <Content Include="Scripts\bootstrap2-toggle.js" />
    <Content Include="Scripts\bootstrap2-toggle.min.js" />
    <Content Include="Scripts\jquery-3.3.1.js" />
    <Content Include="Scripts\jquery-3.3.1.min.js" />
    <Content Include="Scripts\jquery-3.3.1.slim.js" />
    <Content Include="Scripts\jquery-3.3.1.slim.min.js" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\jquery.mask.min.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Scripts\mdb.js" />
    <Content Include="Scripts\mdb.lite.js" />
    <Content Include="Scripts\mdb.lite.min.js" />
    <Content Include="Scripts\mdb.min.js" />
    <Content Include="Scripts\modernizr-2.6.2.js" />
    <Content Include="Scripts\modules\bootstrap.js" />
    <Content Include="Scripts\modules\bootstrap.min.js" />
    <Content Include="Scripts\modules\buttons.js" />
    <Content Include="Scripts\modules\cards.js" />
    <Content Include="Scripts\modules\character-counter.js" />
    <Content Include="Scripts\modules\chips.js" />
    <Content Include="Scripts\modules\collapsible.js" />
    <Content Include="Scripts\modules\default-file-input.js" />
    <Content Include="Scripts\modules\dropdown.js" />
    <Content Include="Scripts\modules\file-input.js" />
    <Content Include="Scripts\modules\forms-free.js" />
    <Content Include="Scripts\modules\jquery-3.3.1.min.js" />
    <Content Include="Scripts\modules\material-select.js" />
    <Content Include="Scripts\modules\mdb-autocomplete.js" />
    <Content Include="Scripts\modules\mdb.js" />
    <Content Include="Scripts\modules\mdb.lite.js" />
    <Content Include="Scripts\modules\mdb.lite.min.js" />
    <Content Include="Scripts\modules\mdb.min.js" />
    <Content Include="Scripts\modules\popper.min.js" />
    <Content Include="Scripts\modules\preloading.js" />
    <Content Include="Scripts\modules\range-input.js" />
    <Content Include="Scripts\modules\scrolling-navbar.js" />
    <Content Include="Scripts\modules\sidenav.js" />
    <Content Include="Scripts\modules\smooth-scroll.js" />
    <Content Include="Scripts\modules\sticky.js" />
    <Content Include="Scripts\popper.min.js" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Musteri\Create.cshtml" />
    <Content Include="Views\Musteri\Delete.cshtml" />
    <Content Include="Views\Musteri\Details.cshtml" />
    <Content Include="Views\Musteri\Edit.cshtml" />
    <Content Include="Views\Musteri\Index.cshtml" />
    <Content Include="Views\Not\Create.cshtml" />
    <Content Include="Views\Not\Delete.cshtml" />
    <Content Include="Views\Not\Edit.cshtml" />
    <Content Include="Views\Not\Index.cshtml" />
    <Content Include="Views\Shared\_Navbar.cshtml" />
    <Content Include="Views\Shared\_Footer.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\Razor Tools\FontAwesome.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="Controllers\GiderController.cs" />
    <Compile Include="Controllers\MusteriController.cs" />
    <Compile Include="Controllers\NotController.cs" />
    <Compile Include="Controllers\SatisController.cs" />
    <Compile Include="Controllers\UrunController.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Migrations\Configuration.cs" />
    <Compile Include="Models\Araclar.cs" />
    <Compile Include="Models\City.cs" />
    <Compile Include="Models\District.cs" />
    <Compile Include="Models\Giderler.cs" />
    <Compile Include="Models\KobiPanelDbContext.cs" />
    <Compile Include="Models\Musteriler.cs" />
    <Compile Include="Models\MyEntityBase.cs" />
    <Compile Include="Models\Neighborhood.cs" />
    <Compile Include="Models\Not.cs" />
    <Compile Include="Models\Satislar.cs" />
    <Compile Include="Models\Town.cs" />
    <Compile Include="Models\Urunler.cs" />
    <Compile Include="ViewModels\SatisViewModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\web.config" />
    <Content Include="font\roboto\Roboto-Bold.eot" />
    <Content Include="font\roboto\Roboto-Bold.ttf" />
    <Content Include="font\roboto\Roboto-Bold.woff" />
    <Content Include="font\roboto\Roboto-Bold.woff2" />
    <Content Include="font\roboto\Roboto-Light.eot" />
    <Content Include="font\roboto\Roboto-Light.ttf" />
    <Content Include="font\roboto\Roboto-Light.woff" />
    <Content Include="font\roboto\Roboto-Light.woff2" />
    <Content Include="font\roboto\Roboto-Medium.eot" />
    <Content Include="font\roboto\Roboto-Medium.ttf" />
    <Content Include="font\roboto\Roboto-Medium.woff" />
    <Content Include="font\roboto\Roboto-Medium.woff2" />
    <Content Include="font\roboto\Roboto-Regular.eot" />
    <Content Include="font\roboto\Roboto-Regular.ttf" />
    <Content Include="font\roboto\Roboto-Regular.woff" />
    <Content Include="font\roboto\Roboto-Regular.woff2" />
    <Content Include="font\roboto\Roboto-Thin.eot" />
    <Content Include="font\roboto\Roboto-Thin.ttf" />
    <Content Include="font\roboto\Roboto-Thin.woff" />
    <Content Include="font\roboto\Roboto-Thin.woff2" />
    <Content Include="Content\css\scss\addons-pro\_cards-extended.scss" />
    <Content Include="Content\css\scss\addons-pro\_chat.scss" />
    <Content Include="Content\css\scss\addons-pro\_steppers.scss" />
    <Content Include="Content\css\scss\addons-pro\_timeline.scss" />
    <Content Include="Content\css\scss\addons\_datatables.scss" />
    <Content Include="Content\css\scss\core\bootstrap\_functions.scss" />
    <Content Include="Content\css\scss\core\bootstrap\_variables.scss" />
    <Content Include="Content\css\scss\core\_colors.scss" />
    <Content Include="Content\css\scss\core\_global.scss" />
    <Content Include="Content\css\scss\core\_helpers.scss" />
    <Content Include="Content\css\scss\core\_masks.scss" />
    <Content Include="Content\css\scss\core\_mixins.scss" />
    <Content Include="Content\css\scss\core\_typography.scss" />
    <Content Include="Content\css\scss\core\_variables.scss" />
    <Content Include="Content\css\scss\core\_waves.scss" />
    <Content Include="Content\css\scss\free\modules\animations-extended\animations-extended.scss" />
    <Content Include="Content\css\scss\free\modules\animations-extended\_module.scss" />
    <Content Include="Content\css\scss\free\_animations-basic.scss" />
    <Content Include="Content\css\scss\free\_badges.scss" />
    <Content Include="Content\css\scss\free\_buttons.scss" />
    <Content Include="Content\css\scss\free\_cards.scss" />
    <Content Include="Content\css\scss\free\_carousels.scss" />
    <Content Include="Content\css\scss\free\_depreciated.scss" />
    <Content Include="Content\css\scss\free\_dropdowns.scss" />
    <Content Include="Content\css\scss\free\_footers.scss" />
    <Content Include="Content\css\scss\free\_forms.scss" />
    <Content Include="Content\css\scss\free\_input-group.scss" />
    <Content Include="Content\css\scss\free\_list-group.scss" />
    <Content Include="Content\css\scss\free\_loader.scss" />
    <Content Include="Content\css\scss\free\_modals.scss" />
    <Content Include="Content\css\scss\free\_msc.scss" />
    <Content Include="Content\css\scss\free\_navbars.scss" />
    <Content Include="Content\css\scss\free\_pagination.scss" />
    <Content Include="Content\css\scss\free\_steppers.scss" />
    <Content Include="Content\css\scss\free\_switch.scss" />
    <Content Include="Content\css\scss\free\_tables.scss" />
    <Content Include="Content\css\scss\mdb.lite.scss" />
    <Content Include="Content\css\scss\mdb.scss" />
    <Content Include="Content\css\scss\pro\modules\accordion-extended\accordion-extended.scss" />
    <Content Include="Content\css\scss\pro\modules\accordion-extended\_module.scss" />
    <Content Include="Content\css\scss\pro\modules\cards-extended\cards-extended.scss" />
    <Content Include="Content\css\scss\pro\modules\cards-extended\_module.scss" />
    <Content Include="Content\css\scss\pro\modules\charts\charts.scss" />
    <Content Include="Content\css\scss\pro\modules\charts\_module.scss" />
    <Content Include="Content\css\scss\pro\modules\lightbox\lightbox.scss" />
    <Content Include="Content\css\scss\pro\modules\lightbox\_module.scss" />
    <Content Include="Content\css\scss\pro\modules\megamenu\megamenu.scss" />
    <Content Include="Content\css\scss\pro\modules\megamenu\_module.scss" />
    <Content Include="Content\css\scss\pro\modules\parallax\parallax.scss" />
    <Content Include="Content\css\scss\pro\modules\parallax\_module.scss" />
    <Content Include="Content\css\scss\pro\picker\_default-date.scss" />
    <Content Include="Content\css\scss\pro\picker\_default-time.scss" />
    <Content Include="Content\css\scss\pro\picker\_default.scss" />
    <Content Include="Content\css\scss\pro\sections\_contacts.scss" />
    <Content Include="Content\css\scss\pro\sections\_magazine.scss" />
    <Content Include="Content\css\scss\pro\sections\_pricing.scss" />
    <Content Include="Content\css\scss\pro\sections\_social.scss" />
    <Content Include="Content\css\scss\pro\sections\_team.scss" />
    <Content Include="Content\css\scss\pro\sections\_templates.scss" />
    <Content Include="Content\css\scss\pro\sections\_testimonials.scss" />
    <Content Include="Content\css\scss\pro\_accordion-basic.scss" />
    <Content Include="Content\css\scss\pro\_animations.scss" />
    <Content Include="Content\css\scss\pro\_autocomplete.scss" />
    <Content Include="Content\css\scss\pro\_blog.scss" />
    <Content Include="Content\css\scss\pro\_buttons.scss" />
    <Content Include="Content\css\scss\pro\_cards-basic.scss" />
    <Content Include="Content\css\scss\pro\_carousels.scss" />
    <Content Include="Content\css\scss\pro\_checkbox.scss" />
    <Content Include="Content\css\scss\pro\_chips.scss" />
    <Content Include="Content\css\scss\pro\_depreciated.scss" />
    <Content Include="Content\css\scss\pro\_dropdowns.scss" />
    <Content Include="Content\css\scss\pro\_ecommerce.scss" />
    <Content Include="Content\css\scss\pro\_file-input.scss" />
    <Content Include="Content\css\scss\pro\_forms.scss" />
    <Content Include="Content\css\scss\pro\_input-group.scss" />
    <Content Include="Content\css\scss\pro\_material-select.scss" />
    <Content Include="Content\css\scss\pro\_msc.scss" />
    <Content Include="Content\css\scss\pro\_navbars.scss" />
    <Content Include="Content\css\scss\pro\_progress.scss" />
    <Content Include="Content\css\scss\pro\_radio.scss" />
    <Content Include="Content\css\scss\pro\_range.scss" />
    <Content Include="Content\css\scss\pro\_scrollbar.scss" />
    <Content Include="Content\css\scss\pro\_scrollspy.scss" />
    <Content Include="Content\css\scss\pro\_sidenav.scss" />
    <Content Include="Content\css\scss\pro\_skins.scss" />
    <Content Include="Content\css\scss\pro\_social-buttons.scss" />
    <Content Include="Content\css\scss\pro\_steppers.scss" />
    <Content Include="Content\css\scss\pro\_switch.scss" />
    <Content Include="Content\css\scss\pro\_tabs.scss" />
    <Content Include="Content\css\scss\pro\_timelines.scss" />
    <Content Include="Content\css\scss\pro\_toasts.scss" />
    <Content Include="Content\css\scss\pro\_tooltips.scss" />
    <Content Include="Content\css\scss\pro\_variables.scss" />
    <Content Include="Content\css\scss\_custom-skin.scss" />
    <Content Include="Content\css\scss\_custom-styles.scss" />
    <Content Include="Content\css\scss\_custom-variables.scss" />
    <None Include="packages.config" />
    <Content Include="Scripts\jquery-3.3.1.slim.min.map" />
    <Content Include="Scripts\jquery-3.3.1.min.map" />
    <Content Include="Views\Gider\Create.cshtml" />
    <Content Include="Views\Gider\Delete.cshtml" />
    <Content Include="Views\Gider\Edit.cshtml" />
    <Content Include="Views\Gider\Index.cshtml" />
    <Content Include="Views\Satis\Create.cshtml" />
    <Content Include="Views\Satis\Delete.cshtml" />
    <Content Include="Views\Satis\Details.cshtml" />
    <Content Include="Views\Satis\Edit.cshtml" />
    <Content Include="Views\Satis\Index.cshtml" />
    <Content Include="Views\Urun\Create.cshtml" />
    <Content Include="Views\Urun\Delete.cshtml" />
    <Content Include="Views\Urun\Details.cshtml" />
    <Content Include="Views\Urun\Edit.cshtml" />
    <Content Include="Views\Urun\Index.cshtml" />
    <Content Include="Views\Shared\_LeftSideNavbar.cshtml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>58259</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:58259/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>