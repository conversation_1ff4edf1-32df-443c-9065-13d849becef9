﻿@model IEnumerable<KobiPanel.Models.Not>

@{
    ViewBag.Title = "Not Listesi";
}

@if (TempData["notolusturuldu"] != null)
{
    <div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>@TempData["Success"]</strong>
    </div>
}

<p>
    @Html.ActionLink("Yeni Not Oluştur", "NotEkle", null, new { @class = "btn btn-primary" })
</p>
<table id="example" class="table table-striped table-bordered dt-responsive" style="width:100%">
    <tr>
        <th class="th-lg">
            <PERSON>nu
        </th>
        <th class="th-lg">
            Açıklama
        </th>
        <th class="th-sm">
            Oluşturulma Tarihi
        </th>
        <th class="th">İşlem</th>
    </tr>

    @foreach (var item in Model)
    {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.konu)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.aciklama)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Tarih)
            </td>
            <td>
                <i class="fas fa-edit" style="color:blue">
                    @Html.ActionLink("Düzenle", "NotDuzenle", new { id = item.id }, new { @class = "text-primary" })
                </i>
                <i class="fas fa-trash" style="color:red">
                    @Html.ActionLink("Sil", "NotSil", new { id = item.id }, new { @class = "text-danger" })

            </td>
        </tr>
    }

</table>
