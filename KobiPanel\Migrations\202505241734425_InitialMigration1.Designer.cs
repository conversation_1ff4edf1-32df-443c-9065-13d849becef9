// <auto-generated />
namespace KobiPanel.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.2.0-61023")]
    public sealed partial class InitialMigration1 : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(InitialMigration1));
        
        string IMigrationMetadata.Id
        {
            get { return "202505241734425_InitialMigration1"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
