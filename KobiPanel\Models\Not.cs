namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Data.Entity.Spatial;

    [Table("Not")]
    public class Not
    {
        [Key,DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int id { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessage = "Konu alanı gereklidir.")]
        [DisplayName("Konu")]
        public string konu { get; set; }


        [DisplayName("Açıklama")]
        [Required(AllowEmptyStrings = false, ErrorMessage = "Açıklama alanı gereklidir.")]
        public string aciklama { get; set; }

        [DataType(DataType.DateTime)]
        public DateTime Tarih { get; set; }
    }
}
