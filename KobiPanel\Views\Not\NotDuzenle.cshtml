﻿@model KobiPanel.Models.Not

@{
    ViewBag.Title = "Not Düzenle";
}



@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        @Html.HiddenFor(model => model.konu)

        <div class="form-group">
            @Html.LabelFor(model => model.aciklama, htmlAttributes: new { @class = "control-label col-md-6" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.aciklama, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.aciklama,"", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Kaydet" class="btn btn-success" />
            </div>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Listeye Geri Dön", "NotAnasayfa",null, new {@class = "btn btn-primary" })
</div>
