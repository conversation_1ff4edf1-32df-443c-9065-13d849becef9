﻿@model KobiPanel.Models.Vehicles.Araclar

@{
    ViewBag.Title = "Araç Ekleme İşlemi";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


@using (Html.BeginForm()) 
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="row">
            <div class="col-md-12">
                <div class="md-form">
                    @Html.EditorFor(model => model.Plaka, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.LabelFor(model => model.Plaka, htmlAttributes: new { @class = "" })
                    @Html.ValidationMessageFor(model => model.Plaka, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="md-form">
                    @Html.EditorFor(model => model.TescilNo, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.LabelFor(model => model.TescilNo, htmlAttributes: new { @class = "" })
                    @Html.ValidationMessageFor(model => model.TescilNo, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="md-form">
                    @Html.EditorFor(model => model.VizeBitisTarihi, new { htmlAttributes = new { @class = "form-control", type = "date" } })
                    @Html.LabelFor(model => model.VizeBitisTarihi, htmlAttributes: new { @class = "" })
                    @Html.ValidationMessageFor(model => model.VizeBitisTarihi, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="md-form">
                    @Html.EditorFor(model => model.SigortaBitisTarihi, new { htmlAttributes = new { @class = "form-control", type = "date" } })
                    @Html.LabelFor(model => model.SigortaBitisTarihi, htmlAttributes: new { @class = "" })
                    @Html.ValidationMessageFor(model => model.SigortaBitisTarihi, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="md-form">
                    @Html.EditorFor(model => model.EgzozBitisTarihi, new { htmlAttributes = new { @class = "form-control", type = "date" } })
                    @Html.LabelFor(model => model.EgzozBitisTarihi, htmlAttributes: new { @class = "" })
                    @Html.ValidationMessageFor(model => model.EgzozBitisTarihi, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>


        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Ekle" class="btn btn-outline-green waves-effect" />
            </div>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Back to List", "AracAnasayfa")
</div>
