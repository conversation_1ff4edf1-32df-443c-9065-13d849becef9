﻿@model KobiPanel.Models.Urunler

@{
    ViewBag.Title = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>Details</h2>

<div>
    <h4><PERSON><PERSON><PERSON></h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.UrunAdi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.UrunAdi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Adet)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Adet)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.AlisFiyat)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.AlisFiyat)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Kdv)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Kdv)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SatisFiyat)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SatisFiyat)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.EklenmeTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.EklenmeTarihi)
        </dd>

    </dl>
</div>
<p>
    @Html.ActionLink("Düzenle", "Edit", new { id = Model.ID }) |
    @Html.ActionLink("Listeye Geri Dön", "Index")
</p>
