﻿@using GridMvc.Html
@model IEnumerable<KobiPanel.Models.Satislar>
@{

    ViewBag.Title = "SatisAnasayfaGrid";
    Layout = "~/Views/Shared/_LayoutBos.cshtml";
}



<div>
    @Html.Grid(Model).Columns(col =>
{
    col.Add(x => x.Musteriler.adsoyad).RenderValueAs(x=>x.Musteriler.adsoyad +"(" + x.MusteriID + ")").Titled("Ad Soyad").Filterable(true).Sortable(true);
    col.Add(x => x.Urun.UrunAdi).Titled("Ürün Adı").Filterable(true);
    col.Add(x => x.Urun<PERSON>deti).Titled("Adet");
    col.Add(x => x.Satis<PERSON>arihi).Titled("Satış Tarihi").Sortable(true).Filterable(true);
    col.Add(x => x.Teslim<PERSON>dildiMi).Titled("Teslim Durumu").Sortable(true).Filterable(true);
    col.Add(x => x.ServisMi).RenderValueAs(x=>(x.Servis<PERSON>i)?"Adrese Teslim":"Yerinde Teslim").Titled("Teslim Şekli").Sortable(true);
    col.Add(x => x.Tutar).Sortable(true);
    

}).WithPaging(12).SetLanguage("tr")
</div>

