//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("District")]
    public class District
    {
        
        public District()
        {
            this.Musteriler = new HashSet<Musteriler>();
            this.Neighborhood = new HashSet<Neighborhood>();
        }
        [Key]
        public int DistrictID { get; set; }
        public int TownID { get; set; }
        public string DistrictName { get; set; }
    
        public virtual Town Town { get; set; }
        
        public virtual ICollection<Musteriler> Musteriler { get; set; }
        
        public virtual ICollection<Neighborhood> Neighborhood { get; set; }
    }
}
