﻿@model KobiPanel.Models.Satislar

@{
    ViewBag.PageHeader = "Satış Özeti";
    ViewBag.Title = "Satış Özeti";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="row">
    <div class="col-md-6">
        <div class="col-md-3" id="yazdirilacakicerik">
            <b class="align-content-center">YÜKLEME FİŞİ</b>  <br /> <br />
            @if (Model.KrediKartiMi == true)
            {
                <h3>POS MAKİNASI GÖTÜRÜLECEK</h3>
            }
            <b>MS NO : @Model.MusteriID - @Model.SatisID</b> <br />
            <b>MÜŞTERİ BİLGİLERİ</b>
            <b>
                @Model.Musteriler.adsoyad
            </b> <br />
            @*String Formatlama*@
            <b>
                0 @String.Format("{0:(###) ### ## ##}", Convert.ToInt64(Model.Musteriler.tel))
            </b> <br />
            <b>
                @Model.Musteriler.City.CityName - @Model.Musteriler.Town.TownName - @Model.Musteriler.Neighborhood.NeighborhoodName
            </b> <br />
            <b>
                ÜRÜN BİLGİLERİ <br /> @Model.Urun.UrunAdi - @Model.UrunAdeti Adet <br />
            </b>
            <b>
                Tarih :  @Model.SatisTarihi <br />
            </b>
            <b>
                @(Model.ServisMi == true ? "Adrese Teslim" : "Depoda Teslim") <br />
            </b>
            <b>
                Tutar :  @Model.Tutar TL <br />
            </b>
            @if (Model.Aciklama != null)
            {
                <b>
                    Açıklama : @Model.Aciklama
                </b>
            }
        </div>

        <button class="btn btn-outline-green btn-lg waves-effect" onclick='printDiv();'>YÜKLEME FİŞİ YAZDIR</button>
        <a href="~/Satis/SatisAnasayfa" class="btn btn-outline-danger btn-lg waves-effect">SATIŞ LİSTESİNE DÖN</a>

    </div>
    <div class="col-md-6">
        <hr />
        <dl class="dl-horizontal">
            <dt>
                @Html.DisplayNameFor(model => model.Musteriler.adsoyad)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.Musteriler.adsoyad)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.Urun.UrunAdi)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.Urun.UrunAdi)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.UrunAdeti)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.UrunAdeti)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.Tutar)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.Tutar)
            </dd>

            @if (Model.indirim > 0)
            {

                <dt>
                    @Html.DisplayNameFor(model => model.indirim)
                </dt>

                <dd>
                    @Html.DisplayFor(model => model.indirim)
                </dd>
            }



            <dt>
                @Html.DisplayNameFor(model => model.ServisMi)
            </dt>

            <dd>
                @(Model.ServisMi == true ? "Adrese Teslim" : "Yerinde Teslim")
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.SatisTarihi)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.SatisTarihi)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.NakliyeBedeli)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.NakliyeBedeli) TL
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.TeslimEdildiMi)
            </dt>

            @if (Model.TeslimEdildiMi == true)
            {
                <dd style="color:green">
                    @(Model.TeslimEdildiMi == true ? "Teslim Edildi" : "Teslim Edilmedi")
                </dd>
            }
            else
            {
                <dd style="color:red">
                    @(Model.TeslimEdildiMi == true ? "Teslim Edildi" : "Teslim Edilmedi")
                </dd>
            }

            <dt>
                @Html.DisplayNameFor(model => model.Aciklama)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.Aciklama)
            </dd>

        </dl>
    </div>
</div>


<script>
    function printDiv() {

        var divToPrint = document.getElementById('yazdirilacakicerik');

        var newWin = window.open('', 'Yazdırma Ekranı', 'height=600,width=500');

        newWin.document.open();

        newWin.document.write('<html><body onload="window.print()">' + divToPrint.innerHTML + '</body></html>');

        newWin.document.close();

        setTimeout(function () { newWin.close(); }, 10);

    }
</script>
