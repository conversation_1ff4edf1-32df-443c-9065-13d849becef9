/* ========================================
   DARK/LIGHT THEME TOGGLE SYSTEM
   ======================================== */

(function() {
    'use strict';

    // Theme configuration
    const THEMES = {
        LIGHT: 'light',
        DARK: 'dark'
    };

    const STORAGE_KEY = 'kobi-panel-theme';
    const THEME_ATTRIBUTE = 'data-theme';

    // DOM elements
    let themeToggleBtn = null;
    let themeIcon = null;

    /**
     * Initialize theme system
     */
    function initTheme() {
        // Get DOM elements
        themeToggleBtn = document.getElementById('theme-toggle');
        themeIcon = document.getElementById('theme-icon');

        if (!themeToggleBtn || !themeIcon) {
            console.warn('Theme toggle elements not found');
            return;
        }

        // Set initial theme
        const savedTheme = getSavedTheme();
        const systemTheme = getSystemTheme();
        const initialTheme = savedTheme || systemTheme;

        setTheme(initialTheme);

        // Add event listeners
        themeToggleBtn.addEventListener('click', toggleTheme);
        
        // Listen for system theme changes
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', handleSystemThemeChange);
        }

        console.log('Theme system initialized with theme:', initialTheme);
    }

    /**
     * Toggle between light and dark themes
     */
    function toggleTheme() {
        const currentTheme = getCurrentTheme();
        const newTheme = currentTheme === THEMES.DARK ? THEMES.LIGHT : THEMES.DARK;
        
        setTheme(newTheme);
        saveTheme(newTheme);

        // Add animation class
        if (themeIcon) {
            themeIcon.classList.add('theme-transition');
            setTimeout(() => {
                themeIcon.classList.remove('theme-transition');
            }, 300);
        }

        console.log('Theme toggled to:', newTheme);
    }

    /**
     * Set theme
     * @param {string} theme - Theme to set (light/dark)
     */
    function setTheme(theme) {
        if (!theme || !Object.values(THEMES).includes(theme)) {
            theme = THEMES.LIGHT;
        }

        // Set theme attribute on document
        document.documentElement.setAttribute(THEME_ATTRIBUTE, theme);

        // Update icon
        updateThemeIcon(theme);

        // Update button tooltip
        updateButtonTooltip(theme);

        // Trigger custom event
        document.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: theme }
        }));
    }

    /**
     * Update theme icon based on current theme
     * @param {string} theme - Current theme
     */
    function updateThemeIcon(theme) {
        if (!themeIcon) return;

        // Remove existing classes
        themeIcon.classList.remove('fa-moon', 'fa-sun');

        // Add appropriate icon
        if (theme === THEMES.DARK) {
            themeIcon.classList.add('fa-sun');
        } else {
            themeIcon.classList.add('fa-moon');
        }
    }

    /**
     * Update button tooltip
     * @param {string} theme - Current theme
     */
    function updateButtonTooltip(theme) {
        if (!themeToggleBtn) return;

        const tooltipText = theme === THEMES.DARK 
            ? 'Aydınlık moda geç' 
            : 'Karanlık moda geç';

        themeToggleBtn.setAttribute('title', tooltipText);
        themeToggleBtn.setAttribute('data-original-title', tooltipText);

        // Update Bootstrap tooltip if exists
        if (window.$ && $(themeToggleBtn).tooltip) {
            $(themeToggleBtn).tooltip('dispose').tooltip({
                title: tooltipText
            });
        }
    }

    /**
     * Get current theme
     * @returns {string} Current theme
     */
    function getCurrentTheme() {
        return document.documentElement.getAttribute(THEME_ATTRIBUTE) || THEMES.LIGHT;
    }

    /**
     * Save theme to localStorage
     * @param {string} theme - Theme to save
     */
    function saveTheme(theme) {
        try {
            localStorage.setItem(STORAGE_KEY, theme);
        } catch (error) {
            console.warn('Could not save theme to localStorage:', error);
        }
    }

    /**
     * Get saved theme from localStorage
     * @returns {string|null} Saved theme or null
     */
    function getSavedTheme() {
        try {
            return localStorage.getItem(STORAGE_KEY);
        } catch (error) {
            console.warn('Could not read theme from localStorage:', error);
            return null;
        }
    }

    /**
     * Get system theme preference
     * @returns {string} System theme preference
     */
    function getSystemTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return THEMES.DARK;
        }
        return THEMES.LIGHT;
    }

    /**
     * Handle system theme change
     * @param {MediaQueryListEvent} e - Media query event
     */
    function handleSystemThemeChange(e) {
        // Only apply system theme if user hasn't manually set a preference
        const savedTheme = getSavedTheme();
        if (!savedTheme) {
            const systemTheme = e.matches ? THEMES.DARK : THEMES.LIGHT;
            setTheme(systemTheme);
        }
    }

    /**
     * Get theme for external use
     * @returns {string} Current theme
     */
    function getTheme() {
        return getCurrentTheme();
    }

    /**
     * Set theme for external use
     * @param {string} theme - Theme to set
     */
    function setThemeExternal(theme) {
        setTheme(theme);
        saveTheme(theme);
    }

    /**
     * Reset theme to system preference
     */
    function resetTheme() {
        try {
            localStorage.removeItem(STORAGE_KEY);
        } catch (error) {
            console.warn('Could not remove theme from localStorage:', error);
        }
        
        const systemTheme = getSystemTheme();
        setTheme(systemTheme);
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTheme);
    } else {
        initTheme();
    }

    // Expose public API
    window.ThemeToggle = {
        getTheme: getTheme,
        setTheme: setThemeExternal,
        toggleTheme: toggleTheme,
        resetTheme: resetTheme,
        THEMES: THEMES
    };

    // Debug info
    console.log('Theme Toggle System loaded');

})();

/* ========================================
   ADDITIONAL THEME UTILITIES
   ======================================== */

/**
 * Apply theme-specific styles to charts and graphs
 */
function applyThemeToCharts() {
    const theme = window.ThemeToggle ? window.ThemeToggle.getTheme() : 'light';
    
    // Chart.js theme application (if Chart.js is used)
    if (window.Chart) {
        const isDark = theme === 'dark';
        Chart.defaults.global.defaultFontColor = isDark ? '#e0e0e0' : '#212529';
        Chart.defaults.global.defaultFontFamily = "'Roboto', sans-serif";
    }

    // Custom chart theme application can be added here
    console.log('Charts updated for theme:', theme);
}

/**
 * Apply theme to third-party components
 */
function applyThemeToComponents() {
    const theme = window.ThemeToggle ? window.ThemeToggle.getTheme() : 'light';
    
    // DataTables theme (if DataTables is used)
    if (window.$ && $.fn.DataTable) {
        $('.dataTable').each(function() {
            const table = $(this).DataTable();
            if (table) {
                table.draw();
            }
        });
    }

    // Material Design Bootstrap components
    if (window.$ && $.fn.materialSelect) {
        $('.mdb-select').materialSelect('destroy');
        setTimeout(() => {
            $('.mdb-select').materialSelect();
        }, 100);
    }

    console.log('Components updated for theme:', theme);
}

// Listen for theme changes and update components
document.addEventListener('themeChanged', function(e) {
    setTimeout(() => {
        applyThemeToCharts();
        applyThemeToComponents();
    }, 100);
});

// Apply theme to components on page load
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        applyThemeToCharts();
        applyThemeToComponents();
    }, 500);
});
