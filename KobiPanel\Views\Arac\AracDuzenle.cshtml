﻿@model KobiPanel.Models.Vehicles.Araclar

@{
    ViewBag.Title = "Araç Düzenleme";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        <h4>Ara<PERSON>lar</h4>
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        @Html.HiddenFor(model => model.ID)

        <div class="form-group">
            @Html.LabelFor(model => model.Plaka, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Plaka, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Plaka, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.TescilNo, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.TescilNo, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.TescilNo, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.VizeBitisTarihi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.VizeBitisTarihi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.VizeBitisTarihi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.SigortaBitisTarihi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.SigortaBitisTarihi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.SigortaBitisTarihi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.EgzozBitisTarihi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.EgzozBitisTarihi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.EgzozBitisTarihi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Save" class="btn btn-default" />
            </div>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Listeye Dön", "AracAnasayfa")
</div>
