﻿@model IEnumerable<KobiPanel.Models.Giderler>

@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>Giderler</h2>

<p>
    @Html.ActionLink("<PERSON><PERSON>", "Create",null,new {@class="btn btn-primary" })
</p>
<table class="table">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.Konu)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Tutar)₺
        </th>
        <th></th>
    </tr>

@foreach (var item in Model) {
    <tr>
        <td>
            @Html.DisplayFor(modelItem => item.Konu)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.Tutar)₺
        </td>
        <td>
            @Html.ActionLink("Düzenle", "Edit", new { id=item.id }) |
            @Html.ActionLink("Detaylar", "Details", new { id=item.id }) |
            @Html.ActionLink("Sil", "Delete", new { id=item.id })
        </td>
    </tr>
}

</table>
