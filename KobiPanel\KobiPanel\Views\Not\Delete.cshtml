﻿@model KobiPanel.Models.Not

@{
    ViewBag.Title = "Delete";
}

<h2>Delete</h2>

<h3>Bu notu silmek istediğinize emin misiniz?</h3>
<div>
    <h4>Not</h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.aciklama)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.aciklama)
        </dd>

    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Delete" class="btn btn-default" /> |
            @Html.ActionLink("Listeye Geri Dön", "Index")
        </div>
    }
</div>
