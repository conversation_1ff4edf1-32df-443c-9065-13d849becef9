﻿@model KobiPanel.Models.Bicme.BicilenTarlalar

@{
    ViewBag.Title = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>Delete</h2>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>BicilenTarlalar</h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.Musteri.adsoyad)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Musteri.adsoyad)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Donum)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Donum)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.BicmeFiyati)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.BicmeFiyati)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ToplamTutar)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ToplamTutar)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.TahsilatTutari)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.TahsilatTutari)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.KalanTutar)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.KalanTutar)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.BicimTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.BicimTarihi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.aciklama)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.aciklama)
        </dd>

    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Delete" class="btn btn-default" /> |
            @Html.ActionLink("Back to List", "Index")
        </div>
    }
</div>
