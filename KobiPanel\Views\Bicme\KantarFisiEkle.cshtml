﻿@model KobiPanel.Models.Bicme.KantarFisi

@{
	ViewBag.Pageheader = "Kantar Fişi <PERSON>";
	ViewBag.Title = "Kantar Fiş<PERSON>";
	Layout = "~/Views/Shared/_Layout.cshtml";
}


@using (Html.BeginForm()) 
{
	@Html.AntiForgeryToken()
	
	<div class="form-horizontal">
		<h4>KantarFisi</h4>
		<hr />
		@Html.ValidationSummary(true, "", new { @class = "text-danger" })

		
		<div>
			<label class="mdb-main-label">Tarla Seçiniz</label>
			@Html.DropDownList("TarlaID", new SelectList(ViewBag.TarlaID, "Value", "Text"), "Lütfen Tarla Seçiniz", new { @class = "mdb-select md-form", searchable = "Tarla Ara", aria_invalid = "false", data_live_search = "true", lang = "tr" })
		</div>
		
			
		



		<div class="form-group">
			@Html.LabelFor(model => model.birinciTartim, htmlAttributes: new { @class = "control-label col-md-2" })
			<div class="col-md-10">
				@Html.EditorFor(model => model.birinciTartim, new { htmlAttributes = new { @class = "form-control" } })
				@Html.ValidationMessageFor(model => model.birinciTartim, "", new { @class = "text-danger" })
			</div>
		</div>

		<div class="form-group">
			@Html.LabelFor(model => model.ikinciTartim, htmlAttributes: new { @class = "control-label col-md-2" })
			<div class="col-md-10">
				@Html.EditorFor(model => model.ikinciTartim, new { htmlAttributes = new { @class = "form-control" } })
				@Html.ValidationMessageFor(model => model.ikinciTartim, "", new { @class = "text-danger" })
			</div>
		</div>

		<div class="form-group">
			@Html.LabelFor(model => model.NetKg, htmlAttributes: new { @class = "control-label col-md-2" })
			<div class="col-md-10">
				@Html.EditorFor(model => model.NetKg, new { htmlAttributes = new { @class = "form-control" } })
				@Html.ValidationMessageFor(model => model.NetKg, "", new { @class = "text-danger" })
			</div>
		</div>

		<div class="form-group">
			@Html.LabelFor(model => model.Plaka, htmlAttributes: new { @class = "control-label col-md-2" })
			<div class="col-md-10">
				@Html.EditorFor(model => model.Plaka, new { htmlAttributes = new { @class = "form-control" } })
				@Html.ValidationMessageFor(model => model.Plaka, "", new { @class = "text-danger" })
			</div>
		</div>

		<div class="form-group">
			@Html.LabelFor(model => model.aciklama, htmlAttributes: new { @class = "control-label col-md-2" })
			<div class="col-md-10">
				@Html.EditorFor(model => model.aciklama, new { htmlAttributes = new { @class = "form-control" } })
				@Html.ValidationMessageFor(model => model.aciklama, "", new { @class = "text-danger" })
			</div>
		</div>

		<div class="form-group">
			@Html.LabelFor(model => model.TartimTarihi, htmlAttributes: new { @class = "control-label col-md-2" })
			<div class="col-md-10">
				@Html.EditorFor(model => model.TartimTarihi, new { htmlAttributes = new { @class = "form-control" } })
				@Html.ValidationMessageFor(model => model.TartimTarihi, "", new { @class = "text-danger" })
			</div>
		</div>

		<div class="form-group">
			<div class="col-md-offset-2 col-md-10">
				<input type="submit" value="Create" class="btn btn-default" />
			</div>
		</div>
	</div>
}

<div>
	@Html.ActionLink("Back to List", "Index")
</div>
