﻿@model IEnumerable<KobiPanel.Models.Vehicles.AlinanYakitlar>

@{
    ViewBag.Title = "Alınan Yakıtlar Listesi";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<p>
    @Html.ActionLink("<PERSON><PERSON> Oluştur", "YakitEkle",null, new {@class="btn btn-success" })
</p>
<table id="example" class="table table-striped table-bordered dt-responsive" style="width:100%">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.Araclar.Plaka)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.YakitTutari)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.YakitinAlindigiYer)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.AlisTarihi)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Litre)
        </th>
        <th>
            İşlem
        </th>
    </tr>

    @foreach (var item in Model)
    {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Araclar.Plaka)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.YakitTutari)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.YakitinAlindigiYer)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.AlisTarihi)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Litre)
            </td>
            <td>
                @Html.ActionLink("Düzenle", "YakitDuzenle", new { id = item.ID }) |
                @Html.ActionLink("Sil", "YakitSil", new { id = item.ID })
            </td>
        </tr>
    }

</table>
