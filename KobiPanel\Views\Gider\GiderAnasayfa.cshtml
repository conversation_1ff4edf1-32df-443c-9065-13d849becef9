﻿@model IEnumerable<KobiPanel.Models.Giderler>

@{
    ViewBag.Title = "Gider Listesi";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<p>
    @Html.ActionLink("<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>",null,new {@class="btn btn-primary" })
</p>
<table id="example" class="table table-striped table-bordered dt-responsive" style="width:100%">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.Konu)
        </th>
        <th>
            Oluşturul<PERSON> Tarihi
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Tutar)₺
        </th>
        <th>İşlem</th>
    </tr>

    @foreach (var item in Model)
    {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Konu)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Tarih)
            </td>
            <td>
                @string.Format("{0:0,0.00}", item.Tutar) ₺
            </td>
            <td>
                @Html.ActionLink("Düzenle", "GiderDuzenle", new { id = item.id }) |
                @Html.ActionLink("Sil", "GiderSil", new { id = item.id })
            </td>
        </tr>
    }

</table>
