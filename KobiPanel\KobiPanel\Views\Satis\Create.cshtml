﻿@model KobiPanel.Models.Satislar

@{
    ViewBag.Title = "Create";
}

<link href="~/Content/css/bootstrap-select.min.css" rel="stylesheet" />
<script src="~/Scripts/bootstrap-select.min.js"></script>

<h2><PERSON><PERSON>ı<PERSON> Ya<PERSON></h2>

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()

<div class="form-horizontal">

    <hr />
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @if (ViewBag.Success != null)
    {
        <div class="alert alert-success alert-dismissible">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            <strong>@ViewBag.Success</strong>
        </div>
    }

    @if (ViewBag.Error != null)
    {
        <div class="alert alert-warning alert-dismissible">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            <strong>@ViewBag.Error</strong>
        </div>
    }
    <div class="form-group">
        <div class="col-md-10">
            @Html.DropDownListFor(m => m.MusteriID, (SelectList)ViewBag.SListMusteriler, "Lütfen Bir Müşteri Seçiniz", new { @class = "selectpicker form-control valid", searchable = "Search here..", aria_invalid = "false", data_live_search = "true", lang = "tr" })
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Tutar, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Tutar, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Tutar, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.SatisTarihi, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.SatisTarihi, new { htmlAttributes = new { @class = "form-control",type="date" } })
            @Html.ValidationMessageFor(model => model.SatisTarihi, "", new { @class = "text-danger" })
        </div>
    </div>



    <div class="radio">
        <label>
            @Html.RadioButtonFor(m => m.ServisMi, true) Adrese Teslim
        </label>
        <label>
            @Html.RadioButtonFor(m => m.ServisMi, true) Yerinde Teslim
        </label>

    </div>

    <div class="radio">
        <label>
            @Html.RadioButtonFor(m => m.TeslimEdildiMi, true) Teslim Edildi
        </label>
        <label>
            @Html.RadioButtonFor(m => m.TeslimEdildiMi, false)Teslim EDİLMEDİ
        </label>
    </div>



    <div class="form-group">
        @Html.LabelFor(model => model.Aciklama, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Aciklama, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Aciklama, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        <div class="col-md-offset-2 col-md-10">
            <input type="submit" value="Create" class="btn btn-default" />
        </div>
    </div>
</div>
}

<script>
    $(document).ready(function () {
        $('.mdb-select').materialSelect({ multiple: false });

    });
</script>
<div>
    @Html.ActionLink("Listeye Geri Dön", "Index")
</div>

<!--Müşteri Arama Script-->
<script>
    /* When the user clicks on the button,
    toggle between hiding and showing the dropdown content */
    function myFunction() {
        document.getElementById("myDropdown").classList.toggle("show");
    }

    function filterFunction() {
        var input, filter, ul, li, a, i;
        input = document.getElementById("myInput");
        filter = input.value.toUpperCase();
        div = document.getElementById("myDropdown");
        a = div.getElementsByTagName("a");
        for (i = 0; i < a.length; i++) {
            txtValue = a[i].textContent || a[i].innerText;
            if (txtValue.toUpperCase().indexOf(filter) > -1) {
                a[i].style.display = "";
            } else {
                a[i].style.display = "none";
            }
        }
    }
</script>