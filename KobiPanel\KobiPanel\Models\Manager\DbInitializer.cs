﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace KobiPanel.Models
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public class KobiPanelDBEntities : DbContext
    {
        public KobiPanelDBEntities()
            : base("name=KobiPanelDBEntities")
        {
            Database.SetInitializer<KobiPanelDBEntities>(new CreateDatabaseIfNotExists<KobiPanelDBEntities>());
        }
        
    
        public virtual DbSet<City> City { get; }
        public virtual DbSet<District> District { get; }
        public virtual DbSet<Musteriler> Musteriler { get; set; }
        public virtual DbSet<Neighborhood> Neighborhood { get; }
        public virtual DbSet<Not> Not { get; set; }
        public virtual DbSet<Satislar> Satislar { get; set; }
        public virtual DbSet<Town> Town { get; }
        public virtual DbSet<Araclar> Araclar { get; set; }
        public virtual DbSet<Giderler> Giderler { get; set; }

        public class VeriTabaniOlusturucu : CreateDatabaseIfNotExists<KobiPanelDBEntities>
        {
            protected override void Seed(KobiPanelDBEntities context)
            {
                base.Seed(context);
            }
        }
    }
}
