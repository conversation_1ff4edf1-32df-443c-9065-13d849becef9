﻿@model KobiPanel.Models.Vehicles.AlinanYakitlar

@{
    ViewBag.Title = "Yakıt Kaydı Ekle";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@using (Html.BeginForm()) 
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="col-md-12">
            <div class="md-form">
                @Html.DropDownList("ID", null, htmlAttributes: new { @class = "mdb-select md-form"})
                @Html.LabelFor(model => model.Araclar.Plaka, htmlAttributes: new { @class = "active" })
                @Html.ValidationMessageFor(model => model.Araclar.Plaka, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.YakitTutari, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.YakitTutari, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.YakitTutari, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.YakitinAlindigiYer, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.YakitinAlindigiYer, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.YakitinAlindigiYer, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.AlisTarihi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.AlisTarihi, new { htmlAttributes = new { @class = "form-control",type="date" } })
                @Html.ValidationMessageFor(model => model.AlisTarihi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Litre, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Litre, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Litre, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Kaydet" class="btn btn-default" />
            </div>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Listeye Dön", "YakitAnasayfa")
</div>
