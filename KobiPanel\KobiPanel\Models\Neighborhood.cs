namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Data.Entity.Spatial;

    [Table("Neighborhood")]
    public class Neighborhood
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Neighborhood()
        {
            Musteriler = new HashSet<Musteriler>();
        }

        public int NeighborhoodID { get; set; }

        [DisplayName("Mahalle")]
        public string NeighborhoodName { get; set; }

        public int DistrictID { get; set; }

        public string ZipCode { get; set; }

        public virtual District District { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Musteriler> <PERSON><PERSON><PERSON> { get; set; }
    }
}
