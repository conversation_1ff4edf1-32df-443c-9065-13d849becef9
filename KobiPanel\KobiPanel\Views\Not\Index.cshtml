﻿@model IEnumerable<KobiPanel.Models.Not>

@{
    ViewBag.Title = "Index";
}

<h2 class="text-center">Notlar</h2>

<p>
    @Html.ActionLink("Yeni Not Oluştur", "Create", null,new {@class="btn btn-primary" })
</p>
<table class="table">
    <tr>
        <th class="th-lg">
            <PERSON>nu
        </th>
        <th class="th-lg">
            Açıklama
        </th>
        <th class="th-lg">
            Oluşturulma Tarihi
        </th>
        <th class="th-lg"></th>
    </tr>

@foreach (var item in Model) {
    <tr>
        <td>
            @Html.DisplayFor(modelItem=>item.konu)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.aciklama)
        </td>
        <td>
            @Html.DisplayFor(modelItem=> item.Tarih)
        </td>
        <td>
            @Html.ActionLink("<PERSON><PERSON><PERSON><PERSON>", "Edit", new { id=item.id }) |
            @Html.ActionLink("Sil", "Delete", new { id=item.id })
        </td>
    </tr>
}

</table>
