﻿@model KobiPanel.Models.Musteriler

@{
    ViewBag.Title = "Edit";
}

<h2><PERSON><PERSON><PERSON><PERSON><PERSON></h2>


@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()

    <div class="form-horizontal">
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        @Html.HiddenFor(model => model.ID)

        <div class="form-group">
            @Html.LabelFor(model => model.adsoyad, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.adsoyad, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.adsoyad, "", new { @class = "text-danger" })
            </div>
        </div>


        <div class="form-group">
            @Html.LabelFor(model => model.tel, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.tel, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.tel, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.yas, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.yas, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.yas, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.CityID, "CityID", htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownList("CityID", null, htmlAttributes: new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.CityID, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.TownID, "TownID", htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownList("TownID", null, htmlAttributes: new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.TownID, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.DistricID, "DistricID", htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownList("DistricID", null, htmlAttributes: new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.DistricID, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.NeighborhoodID, "NeighborhoodID", htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownList("NeighborhoodID", null, htmlAttributes: new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.NeighborhoodID, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.KucukBasHayvanSayisi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.KucukBasHayvanSayisi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.KucukBasHayvanSayisi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.BuyukBasHayvanSayisi, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.BuyukBasHayvanSayisi, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.BuyukBasHayvanSayisi, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Kaydet" class="btn btn-success" />
            </div>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Listeye Geri Dön", "Index")
</div>