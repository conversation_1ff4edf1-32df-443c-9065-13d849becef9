﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>H4sIAAAAAAAEAO1d224cu5V9H2D+odFPmcGJWrKRQY4hJZAlKxF0JAtu+2CSF4PqolqEqqs6VdWKdQbzZfOQT8ovDOvK2+atilXdDg78YvGyeNmLmyzu3dz//L9/nP7x2yaeveAsJ2lyNj85Op7PcLJKI5Ksz+a74vG3v5//8Q///m+nH6LNt9nPbbm3ZTlaM8nP5k9FsX23WOSrJ7xB+dGGrLI0Tx+Lo1W6WaAoXbw5Pv5xcXKywBRiTrFms9NPu6QgG1z9Qf+8SJMV3hY7FN+mEY7zJp3mLCvU2R3a4HyLVvhsfpM+kHuU4PioLjufnccE0X4scfw4n6EkSQtU0F6++5LjZZGlyXq5pQko/vy6xbTcI4pz3PT+HSvuOpDjN+VAFqxiC7Xa5UW68QQ8edvMzEKu3mt+593M0bn7QOe4eC1HXc3f2fw8Q6sYZfOZ3Na7izgry6mze/QzfiKrGOdHTeUfZl2RHzo+UNqU/********************************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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>