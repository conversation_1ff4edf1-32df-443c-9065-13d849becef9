﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.4.1.9004" targetFramework="net461" />
  <package id="bootstrap" version="3.0.0" targetFramework="net461" />
  <package id="EntityFramework" version="6.2.0" targetFramework="net461" />
  <package id="FontAwesome" version="4.2.0" targetFramework="net461" />
  <package id="jQuery" version="3.3.1" targetFramework="net461" />
  <package id="jQuery.Validation" version="1.17.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net461" />
  <package id="Microsoft.jQuery.Unobtrusive.Validation" version="3.2.11" targetFramework="net461" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net461" />
  <package id="Modernizr" version="2.6.2" targetFramework="net461" />
  <package id="Mvc.RazorTools.Base" version="1.0.5" targetFramework="net461" />
  <package id="Mvc.RazorTools.FontAwesome" version="4.3.0.0" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="5.0.4" targetFramework="net461" />
  <package id="WebActivatorEx" version="2.0.6" targetFramework="net461" />
  <package id="WebGrease" version="1.5.2" targetFramework="net461" />
</packages>