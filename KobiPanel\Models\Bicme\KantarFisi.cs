﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace KobiPanel.Models.Bicme
{
    [Table("KantarFisi")]
    public class KantarFisi
    {
        [Key,DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int FisID { get; set; }

        [DisplayName("Tarla No"),Required(ErrorMessage ="Tarla Seçiniz.")]
        public int TarlaID { get; set; }

        
        public int MusteriID { get; set; }

        [Required(ErrorMessage = "1.Tartımı Giriniz."), DisplayName("Birinci Tartım")]
        public int birinciTartim { get; set; }

        [Required (ErrorMessage ="2.Tartımı Giriniz."),DisplayName("İkinci Tartım")]
        public int ikinciTartim { get; set; }

        [DisplayName("Net Kg")]
        public int NetKg { get; set; }

        [Required(AllowEmptyStrings = false,ErrorMessage = "Lütfen Plaka Giriniz.")]
        public string Plaka { get; set; }

        [DisplayName("Şoför Adı"),Required(ErrorMessage = "Şoför Adı Giriniz")]
        public string SoforAdi { get; set; }

        [DisplayName("Açıklama")]
        public string aciklama { get; set; }

        [DataType(DataType.Date)]
        public DateTime TartimTarihi { get; set; }

        [ForeignKey("TarlaID")]
        public BicilenTarlalar BicilenTarlalar { get; set; }
        
    }
}