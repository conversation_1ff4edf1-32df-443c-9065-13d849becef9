﻿<html lang="tr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Kobi Panel - @ViewBag.Title</title>
    <!-- Font Awesome -->
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css">
    <!-- Bootstrap core CSS -->
    <link href="~/Content/Mdb/css/bootstrap.min.css" rel="stylesheet" />
    <!-- Material Design Bootstrap -->
    <link href="~/Content/Mdb/css/mdb.min.css" rel="stylesheet" />
    <link href="~/Content/ism.css" rel="stylesheet" />
    <!-- Dark Theme CSS -->
    <link href="~/Content/css/dark-theme.css" rel="stylesheet" />
    <script src="~/Content/Mdb/js/jquery-3.3.1.min.js"></script>
    <script src="~/Scripts/jquery.validate.min.js"></script>
    <script src="~/Scripts/jquery.validate.unobtrusive.min.js"></script>
    <!-- Your custom styles (optional) -->
</head>
<body class="fixed-sn white-skin">
    <!--Main Navigation-->
    @Html.Partial("_Navbar")
    <!--Main Navigation-->
    <!--Main layout-->
    <main>
        <div class="container-fluid">
            <section class="mb-5" style="margin-top: -20px;">
                <!--Card-->
                <div class="card card-cascade narrower">
                    <!--Section: Chart-->
                    <section>
                        <!--Grid row-->
                        <div class="row">
                            <!--Grid column-->
                            <div class="col-xl-12 col-lg-10 col-md-12 col-sm-6">
                                <!--Card image-->
                                <div class="view view-cascade gradient-card-header light-blue lighten-1">
                                    <h2 class="h2-responsive mb-0">@ViewBag.PageHeader</h2>
                                </div>
                                <!--/Card image-->
                                <!--Card content-->
                                <div class="card-body card-body-cascade pb-0">
                                    @RenderBody()
                                </div>
                                <!--/.Card content-->
                            </div>
                        </div>

                    </section>
                </div>

            </section>
        </div>
    </main>
    <!--Main layout-->
    <!--Footer-->
    @Html.Partial("_Footer")
    <!--/.Footer-->
    <!-- SCRIPTS -->
    <!-- JQuery -->
    <!-- Bootstrap tooltips -->
    <script src="~/Content/Mdb/js/popper.min.js"></script>
    <!-- Bootstrap core JavaScript -->
    <script src="~/Content/Mdb/js/bootstrap.min.js"></script>
    <!-- MDB core JavaScript -->
    <script src="~/Content/Mdb/js/mdb.min.js"></script>
    <!-- Theme Toggle JavaScript -->
    <script src="~/Scripts/theme-toggle.js"></script>
    <!--Initializations-->
    <script>
        // SideNav Initialization
        $(".button-collapse").sideNav();

        var container = document.querySelector('.custom-scrollbar');
        Ps.initialize(container, {
            wheelSpeed: 2,
            wheelPropagation: true,
            minScrollbarLength: 20
        });

        // Data Picker Initialization
        $('.datepicker').pickadate();

        // Material Select Initialization
        $(document).ready(function () {
            $('.mdb-select').materialSelect({
                destroy: true

            });
        });

        // Tooltips Initialization
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })

    </script>
    <div class="drag-target" style="left: 0px;"></div>



    <div class="ff-ext--bootstrapResponsiveHelper">
        <div class="visible-lg-block" attr-tag="lg" attr-version="3"></div><div class="visible-md-block" attr-tag="md" attr-version="3"></div><div class="visible-sm-block" attr-tag="sm" attr-version="3"></div><div class="visible-xs-block" attr-tag="xs" attr-version="3"></div><div class="d-none d-xl-block" attr-tag="xl" attr-version="4"></div><div class="d-none d-lg-block" attr-tag="lg" attr-version="4"></div><div class="d-none d-md-block" attr-tag="md" attr-version="4"></div><div class="d-none d-sm-block" attr-tag="sm" attr-version="4"></div><div class="d-none d-block" attr-tag="xs" attr-version="4"></div>
    </div>
</body>
</html>