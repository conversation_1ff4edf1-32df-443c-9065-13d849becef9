﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace KobiPanel.Models.Vehicles
{
    public class AlinanYakitlar
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity),ForeignKey("Araclar")]
        public int ID { get; set; }

        public int AracID { get; set; }

        [DisplayName("Yakıt Tutarı")]
        public int YakitTutari { get; set; }

        [DisplayName("Yakıtın Alındığı Yer")]
        public string YakitinAlindigiYer { get; set; }

        [DisplayName("Yakıtın Alındığı anki KM")]
        public int? YakitinAlindigiKM { get; set; }

        [DisplayName("Alış Tarihi")]
        public DateTime AlisTarihi { get; set; }

        public int Litre { get; set; }

        [ForeignKey("AracID")]
        public virtual Araclar Araclar { get; set; }
    }
}