﻿@model KobiPanel.Models.Not

@{
    ViewBag.Title = "Edit";
}

<h2>Not Düzenle</h2>


@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        <h4>Not</h4>
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        @Html.HiddenFor(model => model.konu)

        <div class="form-group">
            @Html.LabelFor(model => model.aciklama, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.aciklama, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.aciklama,"", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Kaydet" class="btn btn-success" />
            </div>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Listeye Geri Dön", "Index")
</div>
