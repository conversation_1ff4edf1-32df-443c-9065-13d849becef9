﻿<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{9694393C-7E4B-4D02-AE7E-AC507ACEF093}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>KobiPanel</RootNamespace>
    <AssemblyName>KobiPanel</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress>true</Use64BitIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TypeScriptToolsVersion>3.5</TypeScriptToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <RunCodeAnalysis>true</RunCodeAnalysis>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime, Version=*******, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\packages\Antlr.*******\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="FakeData, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\FakeData.1.0.0\lib\FakeData.dll</HintPath>
    </Reference>
    <Reference Include="Grid.Mvc.Ajax, Version=1.0.2.4, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Grid.Mvc.Ajax.1.0.31\lib\Grid.Mvc.Ajax.dll</HintPath>
    </Reference>
    <Reference Include="GridMvc, Version=2.0.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Grid.Mvc.Redux.1.0.4\lib\GridMvc.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Mvc.RazorTools, Version=1.0.5.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Mvc.RazorTools.Base.1.0.5\lib\net45\Mvc.RazorTools.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PagedList, Version=1.17.0.0, Culture=neutral, PublicKeyToken=abbb863e9397c5e1, processorArchitecture=MSIL">
      <HintPath>..\packages\PagedList.1.17.0.0\lib\net40\PagedList.dll</HintPath>
    </Reference>
    <Reference Include="PagedList.Mvc, Version=4.5.0.0, Culture=neutral, PublicKeyToken=abbb863e9397c5e1, processorArchitecture=MSIL">
      <HintPath>..\packages\PagedList.Mvc.4.5.0.0\lib\net40\PagedList.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization, Version=1.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.2.0\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease, Version=1.6.5135.21930, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Web.Infrastructure">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\font-awesome.css" />
    <Content Include="Content\font-awesome.min.css" />
    <Content Include="Content\fontawesome\css\all.min.css" />
    <Content Include="Content\fontawesome\LICENSE.txt" />
    <Content Include="Content\fontawesome\webfonts\fa-brands-400.svg" />
    <Content Include="Content\fontawesome\webfonts\fa-light-300.svg" />
    <Content Include="Content\fontawesome\webfonts\fa-regular-400.svg" />
    <Content Include="Content\fontawesome\webfonts\fa-solid-900.svg" />
    <Content Include="Content\footable\css\footable.bootstrap.css" />
    <Content Include="Content\footable\css\footable.bootstrap.min.css" />
    <Content Include="Content\footable\js\footable.js" />
    <Content Include="Content\footable\js\footable.min.js" />
    <Content Include="Content\Gridmvc.css" />
    <Content Include="Content\gridmvc.datepicker.css" />
    <Content Include="Content\gridmvc.datepicker.min.css" />
    <Content Include="Content\ism.css" />
    <Content Include="Content\ladda-bootstrap\ladda-themeless.min.css" />
    <Content Include="Content\Mdb\css\addons-pro\cards-extended.css" />
    <Content Include="Content\Mdb\css\addons-pro\cards-extended.min.css" />
    <Content Include="Content\Mdb\css\addons-pro\chat.css" />
    <Content Include="Content\Mdb\css\addons-pro\chat.min.css" />
    <Content Include="Content\Mdb\css\addons-pro\stepper.css" />
    <Content Include="Content\Mdb\css\addons-pro\stepper.min.css" />
    <Content Include="Content\Mdb\css\addons-pro\timeline.css" />
    <Content Include="Content\Mdb\css\addons-pro\timeline.min.css" />
    <Content Include="Content\Mdb\css\addons\datatables.css" />
    <Content Include="Content\Mdb\css\addons\datatables.min.css" />
    <Content Include="Content\Mdb\css\bootstrap.css" />
    <Content Include="Content\Mdb\css\bootstrap.min.css" />
    <Content Include="Content\Mdb\css\mdb.css" />
    <Content Include="Content\Mdb\css\mdb.lite.css" />
    <Content Include="Content\Mdb\css\mdb.lite.min.css" />
    <Content Include="Content\Mdb\css\mdb.min.css" />
    <Content Include="Content\Mdb\css\modules\accordion-extended.css" />
    <Content Include="Content\Mdb\css\modules\accordion-extended.min.css" />
    <Content Include="Content\Mdb\css\modules\animations-extended.css" />
    <Content Include="Content\Mdb\css\modules\animations-extended.min.css" />
    <Content Include="Content\Mdb\css\modules\charts.css" />
    <Content Include="Content\Mdb\css\modules\charts.min.css" />
    <Content Include="Content\Mdb\css\modules\lightbox.css" />
    <Content Include="Content\Mdb\css\modules\lightbox.min.css" />
    <Content Include="Content\Mdb\css\modules\megamenu.css" />
    <Content Include="Content\Mdb\css\modules\megamenu.min.css" />
    <Content Include="Content\Mdb\css\modules\parallax.css" />
    <Content Include="Content\Mdb\css\modules\parallax.min.css" />
    <Content Include="Content\Mdb\css\style.css" />
    <Content Include="Content\Mdb\css\style.min.css" />
    <Content Include="Content\Mdb\img\AydinSilajLogo.png" />
    <Content Include="Content\Mdb\img\bgLogin.jpg" />
    <Content Include="Content\Mdb\img\svg\bgLogin.jpg" />
    <Content Include="Content\Mdb\img\lightbox\default-skin.png" />
    <Content Include="Content\Mdb\img\lightbox\default-skin.svg" />
    <Content Include="Content\Mdb\img\lightbox\preloader.gif" />
    <Content Include="Content\Mdb\img\overlays\01.png" />
    <Content Include="Content\Mdb\img\overlays\02.png" />
    <Content Include="Content\Mdb\img\overlays\03.png" />
    <Content Include="Content\Mdb\img\overlays\04.png" />
    <Content Include="Content\Mdb\img\overlays\05.png" />
    <Content Include="Content\Mdb\img\overlays\06.png" />
    <Content Include="Content\Mdb\img\overlays\07.png" />
    <Content Include="Content\Mdb\img\overlays\08.png" />
    <Content Include="Content\Mdb\img\overlays\09.png" />
    <Content Include="Content\Mdb\img\svg\arrow_left.svg" />
    <Content Include="Content\Mdb\img\svg\arrow_right.svg" />
    <Content Include="Content\Mdb\js\addons-pro\stepper.js" />
    <Content Include="Content\Mdb\js\addons-pro\stepper.min.js" />
    <Content Include="Content\Mdb\js\addons-pro\timeline.js" />
    <Content Include="Content\Mdb\js\addons\datatables.js" />
    <Content Include="Content\Mdb\js\addons\datatables.min.js" />
    <Content Include="Content\Mdb\js\bootstrap.js" />
    <Content Include="Content\Mdb\js\bootstrap.min.js" />
    <Content Include="Content\Mdb\js\jquery-3.3.1.min.js" />
    <Content Include="Content\Mdb\js\mdb.js" />
    <Content Include="Content\Mdb\js\mdb.lite.js" />
    <Content Include="Content\Mdb\js\mdb.lite.min.js" />
    <Content Include="Content\Mdb\js\mdb.min.js" />
    <Content Include="Content\Mdb\js\modules\buttons.js" />
    <Content Include="Content\Mdb\js\modules\cards.js" />
    <Content Include="Content\Mdb\js\modules\character-counter.js" />
    <Content Include="Content\Mdb\js\modules\chips.js" />
    <Content Include="Content\Mdb\js\modules\collapsible.js" />
    <Content Include="Content\Mdb\js\modules\default-file-input.js" />
    <Content Include="Content\Mdb\js\modules\dropdown.js" />
    <Content Include="Content\Mdb\js\modules\file-input.js" />
    <Content Include="Content\Mdb\js\modules\forms-free.js" />
    <Content Include="Content\Mdb\js\modules\material-select.js" />
    <Content Include="Content\Mdb\js\modules\mdb-autocomplete.js" />
    <Content Include="Content\Mdb\js\modules\preloading.js" />
    <Content Include="Content\Mdb\js\modules\range-input.js" />
    <Content Include="Content\Mdb\js\modules\scrolling-navbar.js" />
    <Content Include="Content\Mdb\js\modules\sidenav.js" />
    <Content Include="Content\Mdb\js\modules\smooth-scroll.js" />
    <Content Include="Content\Mdb\js\modules\sticky.js" />
    <Content Include="Content\Mdb\js\popper.min.js" />
    <Content Include="Content\PagedList.css" />
    <Content Include="Migrations\29-02-2020-Musteri-Satis.sql" />
    <Content Include="Migrations\il_ilce.sql" />
    <Content Include="Global.asax" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Bold.eot" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Bold.ttf" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Bold.woff" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Bold.woff2" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Light.eot" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Light.ttf" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Light.woff" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Light.woff2" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Medium.eot" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Medium.ttf" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Medium.woff" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Medium.woff2" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Regular.eot" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Regular.ttf" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Regular.woff" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Regular.woff2" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Thin.eot" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Thin.ttf" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Thin.woff" />
    <Content Include="Content\Mdb\font\roboto\Roboto-Thin.woff2" />
    <Content Include="Content\Mdb\License.pdf" />
    <Content Include="Grid.mvc.readme" />
    <Content Include="Grid.Mvc.Ajax.readme" />
    <Content Include="Content\fontawesome\webfonts\fa-brands-400.eot" />
    <Content Include="Content\fontawesome\webfonts\fa-brands-400.ttf" />
    <Content Include="Content\fontawesome\webfonts\fa-brands-400.woff" />
    <Content Include="Content\fontawesome\webfonts\fa-brands-400.woff2" />
    <Content Include="Content\fontawesome\webfonts\fa-light-300.eot" />
    <Content Include="Content\fontawesome\webfonts\fa-light-300.ttf" />
    <Content Include="Content\fontawesome\webfonts\fa-light-300.woff" />
    <Content Include="Content\fontawesome\webfonts\fa-light-300.woff2" />
    <Content Include="Content\fontawesome\webfonts\fa-regular-400.eot" />
    <Content Include="Content\fontawesome\webfonts\fa-regular-400.ttf" />
    <Content Include="Content\fontawesome\webfonts\fa-regular-400.woff" />
    <Content Include="Content\fontawesome\webfonts\fa-regular-400.woff2" />
    <Content Include="Content\fontawesome\webfonts\fa-solid-900.eot" />
    <Content Include="Content\fontawesome\webfonts\fa-solid-900.ttf" />
    <Content Include="Content\fontawesome\webfonts\fa-solid-900.woff" />
    <Content Include="Content\fontawesome\webfonts\fa-solid-900.woff2" />
    <None Include="Properties\PublishProfiles\CustomProfile.pubxml" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Scripts\bootstrap-datepicker.js" />
    <Content Include="Scripts\gridmvc-ext.js" />
    <Content Include="Scripts\gridmvc.customwidgets.js" />
    <Content Include="Scripts\gridmvc.lang.tr.js" />
    <Content Include="Scripts\gridmvc.lang.fr.js" />
    <Content Include="Scripts\gridmvc.min.js.map" />
    <None Include="Scripts\jquery-3.4.1.intellisense.js" />
    <Content Include="Scripts\gridmvc.js" />
    <Content Include="Scripts\gridmvc.lang.ru.js" />
    <Content Include="Scripts\gridmvc.min.js" />
    <Content Include="Scripts\inactivity.js" />
    <Content Include="Scripts\jquery-3.4.1.js" />
    <Content Include="Scripts\jquery-3.4.1.min.js" />
    <Content Include="Scripts\jquery-3.4.1.slim.js" />
    <Content Include="Scripts\jquery-3.4.1.slim.min.js" />
    <Content Include="Scripts\jquery-3.4.1.slim.min.map" />
    <Content Include="Scripts\jquery-3.4.1.min.map" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\jquery.mask.min.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Scripts\ladda-bootstrap\ladda.min.js" />
    <Content Include="Scripts\ladda-bootstrap\spin.min.js" />
    <Content Include="Scripts\modernizr-2.6.2.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Scripts\URI.js" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Musteri\MusteriEkle.cshtml" />
    <Content Include="Views\Musteri\MusteriSil.cshtml" />
    <Content Include="Views\Musteri\MusteriDetay.cshtml" />
    <Content Include="Views\Musteri\MusteriAnasayfa.cshtml" />
    <Content Include="Views\Not\NotEkle.cshtml" />
    <Content Include="Views\Not\NotSil.cshtml" />
    <Content Include="Views\Not\NotDuzenle.cshtml" />
    <Content Include="Views\Not\NotAnasayfa.cshtml" />
    <Content Include="Views\Shared\_Navbar.cshtml" />
    <Content Include="Views\Shared\_Footer.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="Controllers\AracController.cs" />
    <Compile Include="Controllers\BicmeController.cs" />
    <Compile Include="Controllers\GiderController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\MusteriController.cs" />
    <Compile Include="Controllers\NotController.cs" />
    <Compile Include="Controllers\SatisController.cs" />
    <Compile Include="Controllers\UrunController.cs" />
    <Compile Include="Filters\ActFilter.cs" />
    <Compile Include="Filters\ExcFilter.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Helpers\IPAutorizeAttribute.cs" />
    <Compile Include="Migrations\Configuration.cs" />
    <Compile Include="Models\Bicme\BicilenTarlalar.cs" />
    <Compile Include="Models\Bicme\KantarFisi.cs" />
    <Compile Include="Models\Kullanicilar.cs" />
    <Compile Include="Models\Log.cs" />
    <Compile Include="Models\Urun.cs" />
    <Compile Include="Models\Vehicles\AlinanYakitlar.cs" />
    <Compile Include="Models\Vehicles\Araclar.cs" />
    <Compile Include="Models\Adres\City.cs" />
    <Compile Include="Models\Adres\District.cs" />
    <Compile Include="Models\Giderler.cs" />
    <Compile Include="Models\KobiPanelDbContext.cs" />
    <Compile Include="Models\Musteriler.cs" />
    <Compile Include="Models\MyEntityBase.cs" />
    <Compile Include="Models\Adres\Neighborhood.cs" />
    <Compile Include="Models\Not.cs" />
    <Compile Include="Models\Satislar.cs" />
    <Compile Include="Models\ViewModels\LoginViewModel.cs" />
    <Compile Include="Models\ViewModels\SatislarViewModels.cs" />
    <Compile Include="Models\Town.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\web.config">
      <SubType>Designer</SubType>
    </Content>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <Content Include="Views\Gider\GiderEkle.cshtml" />
    <Content Include="Views\Gider\GiderSil.cshtml" />
    <Content Include="Views\Gider\GiderDuzenle.cshtml" />
    <Content Include="Views\Gider\GiderAnasayfa.cshtml" />
    <Content Include="Views\Satis\SatisEkle.cshtml" />
    <Content Include="Views\Satis\SatisSil.cshtml" />
    <Content Include="Views\Satis\SatisDetay.cshtml" />
    <Content Include="Views\Satis\SatisAnasayfa.cshtml" />
    <Content Include="Views\Shared\_LeftSideNavbar.cshtml" />
    <Content Include="Views\Shared\_TopBar.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Arac\AracEkle.cshtml" />
    <Content Include="Views\Arac\AracSil.cshtml" />
    <Content Include="Views\Arac\AracDuzenle.cshtml" />
    <Content Include="Views\Arac\AracAnasayfa.cshtml" />
    <Content Include="Views\Arac\YakitEkle.cshtml" />
    <Content Include="Views\Arac\YakitSil.cshtml" />
    <Content Include="Views\Arac\YakitDuzenle.cshtml" />
    <Content Include="Views\Arac\YakitAnaSayfa.cshtml" />
    <Content Include="Views\Shared\_LayoutBos.cshtml" />
    <Content Include="Views\Urun\UrunEkle.cshtml" />
    <Content Include="Views\Urun\UrunSil.cshtml" />
    <Content Include="Views\Urun\UrunDetay.cshtml" />
    <Content Include="Views\Urun\UrunDuzenle.cshtml" />
    <Content Include="Views\Urun\UrunAnasayfa.cshtml" />
    <Content Include="Views\Satis\TeslimEdilmeyenSatis.cshtml" />
    <Content Include="Views\Satis\SatisOzeti.cshtml" />
    <Content Include="Views\Satis\SatisDuzenle.cshtml" />
    <Content Include="Views\Musteri\MusteriDuzenle.cshtml" />
    <Content Include="Views\Shared\_GridPager.cshtml" />
    <Content Include="Views\Shared\_Grid.cshtml" />
    <Content Include="Views\Shared\_AjaxGridPager.cshtml" />
    <Content Include="Views\Satis\SatisAnasayfaGrid.cshtml" />
    <Content Include="Views\Bicme\TarlaEkle.cshtml" />
    <Content Include="Views\Bicme\TarlaSil.cshtml" />
    <Content Include="Views\Bicme\TarlaDetay.cshtml" />
    <Content Include="Views\Bicme\TarlaDuzenle.cshtml" />
    <Content Include="Views\Bicme\TarlaAnasayfa.cshtml" />
    <Content Include="Views\Bicme\KantarFisiEkle.cshtml" />
    <Content Include="Views\Bicme\KantarFisiSil.cshtml" />
    <Content Include="Views\Bicme\KantarFisiDetay.cshtml" />
    <Content Include="Views\Bicme\KantarFisiDuzenle.cshtml" />
    <Content Include="Views\Bicme\KantarFisiAnasayfa.cshtml" />
    <Content Include="Views\Home\Login.cshtml" />
    <Content Include="Views\Satis\SatisEkleDeneme.cshtml" />
    <Content Include="Views\Home\GunOzeti.cshtml" />
    <Content Include="Views\Musteri\MusteriAnasayfaGrid.cshtml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Content\fontawesome\js\" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>58259</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:58259/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>