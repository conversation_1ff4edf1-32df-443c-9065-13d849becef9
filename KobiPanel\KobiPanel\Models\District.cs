namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Data.Entity.Spatial;

    [Table("District")]
    public class District
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public District()
        {
            Musteriler = new HashSet<Musteriler>();
            Neighborhood = new HashSet<Neighborhood>();
        }

        public int DistrictID { get; set; }

        public int TownID { get; set; }

        [DisplayName("Semt")]
        public string DistrictName { get; set; }

        public virtual Town Town { get; set; }

        
        public virtual ICollection<Musteriler> Musteriler { get; set; }

        
        public virtual ICollection<Neighborhood> Neighborhood { get; set; }
    }
}
