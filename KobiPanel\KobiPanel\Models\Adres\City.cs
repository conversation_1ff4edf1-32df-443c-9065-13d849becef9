//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("City")]
    public class City
    {

        public City()
        {
            this.Musteriler = new HashSet<Musteriler>();
            this.Town = new HashSet<Town>();
        }
        [Key]   
        public int CityID { get; set; }
        public string CityName { get; set; }
        public string PlateNo { get; set; }
        public string PhoneCode { get; set; }
    
        
        public virtual ICollection<Musteriler> Musteriler { get; set; }
        
        public virtual ICollection<Town> Town { get; set; }
    }
}
