﻿
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title><PERSON><PERSON><PERSON><PERSON> - @ViewBag.Title</title>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css">
    <!-- Bootstrap core CSS -->
    <link href="~/Content/Mdb/css/bootstrap.min.css" rel="stylesheet" />
    <!-- Material Design Bootstrap -->
    <link href="~/Content/Mdb/css/mdb.min.css" rel="stylesheet" />

    <link href="~/Content/ism.css" rel="stylesheet" />
    <script src="~/Content/Mdb/js/jquery-3.3.1.min.js"></script>
    <!-- Your custom styles (optional) -->
    <link href="~/Content/Gridmvc.css" rel="stylesheet" />
    <link href="~/Content/gridmvc.datepicker.min.css" rel="stylesheet" />
    <script src="~/Scripts/gridmvc.min.js"></script>
    <script src="~/Scripts/bootstrap-datepicker.js"></script>
    <link href="~/Content/PagedList.css" rel="stylesheet" />
    <script src="~/Scripts/gridmvc.lang.tr.js"></script>

</head>
<body class="fixed-sn white-skin">
    <!--Main Navigation-->
    @Html.Partial("_Navbar")
    <!--Main Navigation-->
    <!--Main layout-->
    <main>
        <div class="container-fluid">
            @RenderBody()
        </div>
    </main>
    <!--Main layout-->
    <!--Footer-->
    @Html.Partial("_Footer")
    <!--/.Footer-->
    <!-- SCRIPTS -->
    <!-- JQuery -->
    <!-- Bootstrap tooltips -->
    <script src="~/Content/Mdb/js/popper.min.js"></script>
    <!-- Bootstrap core JavaScript -->
    <script src="~/Content/Mdb/js/bootstrap.min.js"></script>
    <!-- MDB core JavaScript -->
    <script src="~/Content/Mdb/js/mdb.min.js"></script>
    <!--Initializations-->
    <script>
        // SideNav Initialization
        $(".button-collapse").sideNav();

        var container = document.querySelector('.custom-scrollbar');
        Ps.initialize(container, {
            wheelSpeed: 2,
            wheelPropagation: true,
            minScrollbarLength: 20
        });

        // Data Picker Initialization
        $('.datepicker').pickadate();

        // Material Select Initialization
        $(document).ready(function () {
            $('.mdb-select').materialSelect({
                destroy: true

            });
        });

        // Tooltips Initialization
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })

    </script>
    <div class="drag-target" style="left: 0px;"></div>

    @*
        Chart Scripti
    *@

    <script>
        // Small chart
        $(function () {
            $('.min-chart#chart-sales').easyPieChart({
                barColor: "#FF5252",
                onStep: function (from, to, percent) {
                    $(this.el).find('.percent').text(Math.round(percent));
                }
            });
        });

        // Main chart
        var ctxL = document.getElementById("lineChart").getContext('2d');
        var myLineChart = new Chart(ctxL, {
            type: 'line',
            data: {
                labels: ["January", "February", "March", "April", "May", "June", "July"],
                datasets: [{
                    label: "My First dataset",
                    fillColor: "#fff",
                    backgroundColor: 'rgba(255, 255, 255, .3)',
                    borderColor: 'rgba(255, 255, 255)',
                    data: [0, 10, 5, 2, 20, 30, 45],
                }]
            },
            options: {
                legend: {
                    labels: {
                        fontColor: "#fff",
                    }
                },
                scales: {
                    xAxes: [{
                        gridLines: {
                            display: true,
                            color: "rgba(255,255,255,.25)"
                        },
                        ticks: {
                            fontColor: "#fff",
                        },
                    }],
                    yAxes: [{
                        display: true,
                        gridLines: {
                            display: true,
                            color: "rgba(255,255,255,.25)"
                        },
                        ticks: {
                            fontColor: "#fff",
                        },
                    }],
                }
            }
        });

        $(function () {
            $('#dark-mode').on('click', function (e) {

                e.preventDefault();
                $('h4, button').not('.check').toggleClass('dark-grey-text text-white');
                $('.list-panel a').toggleClass('dark-grey-text');

                $('footer, .card').toggleClass('dark-card-admin');
                $('body, .navbar').toggleClass('white-skin navy-blue-skin');
                $(this).toggleClass('white text-dark btn-outline-black');
                $('body').toggleClass('dark-bg-admin');
                $('h6, .card, p, td, th, i, li a, h4, input, label').not(
                    '#slide-out i, #slide-out a, .dropdown-item i, .dropdown-item').toggleClass('text-white');
                $('.btn-dash').toggleClass('grey blue').toggleClass('lighten-3 darken-3');
                $('.gradient-card-header').toggleClass('white black lighten-4');
                $('.list-panel a').toggleClass('navy-blue-bg-a text-white').toggleClass('list-group-border');

            });
        });

    </script>

    <div class="ff-ext--bootstrapResponsiveHelper">
        <div class="visible-lg-block" attr-tag="lg" attr-version="3"></div><div class="visible-md-block" attr-tag="md" attr-version="3"></div><div class="visible-sm-block" attr-tag="sm" attr-version="3"></div><div class="visible-xs-block" attr-tag="xs" attr-version="3"></div><div class="d-none d-xl-block" attr-tag="xl" attr-version="4"></div><div class="d-none d-lg-block" attr-tag="lg" attr-version="4"></div><div class="d-none d-md-block" attr-tag="md" attr-version="4"></div><div class="d-none d-sm-block" attr-tag="sm" attr-version="4"></div><div class="d-none d-block" attr-tag="xs" attr-version="4"></div>
    </div>
</body>
</html>