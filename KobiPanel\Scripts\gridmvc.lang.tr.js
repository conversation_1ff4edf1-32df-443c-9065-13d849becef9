﻿/***
* Grid.Mvc Turkish language (tr-TR) http://gridmvc.codeplex.com/
*/
window.GridMvc = window.GridMvc || {};
window.GridMvc.lang = window.GridMvc.lang || {};
GridMvc.lang.tr = {
    filterTypeLabel: "Filtre Giriniz ",
    filterValueLabel: "Değ<PERSON> :",
    applyFilterButtonText: "Uygula",
    filterSelectTypes: {
        Equals: "Eşitse",
        StartsWith: "ile Başlayan",
        Contains: "İçeren",
        EndsWith: "ile Biten",
        GreaterThan: "<PERSON><PERSON><PERSON><PERSON>ktür",
        LessThan: "<PERSON><PERSON><PERSON><PERSON>ktür",
        GreaterThanOrEquals: "Büyük veya Eşitse",
        LessThanOrEquals: "Küçük veya eşitse",
    },
    code: 'tr',
    boolTrueLabel: "<PERSON><PERSON>",
    boolFalseLabel: "<PERSON><PERSON><PERSON>",
    clearFilterLabel: "<PERSON><PERSON><PERSON><PERSON> Temizle"
};