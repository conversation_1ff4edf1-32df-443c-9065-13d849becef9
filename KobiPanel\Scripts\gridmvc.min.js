﻿window.pageGrids=window.pageGrids||{};$.fn.extend({gridmvc:function(){var n=[];return($(this).each(function(){if($(this).data("gridmvc"))n.push($(this).data("gridmvc"));else{var i={lang:$(this).attr("data-lang"),selectable:$(this).attr("data-selectable")=="true",multiplefilters:$(this).attr("data-multiplefilters")=="true"},t=new GridMvc(this,i),r=$(this).attr("data-gridname");r.length>0&&(window.pageGrids[$(this).attr("data-gridname")]=t);n.push(t);$(this).data("gridmvc",t)}}),n.length==1)?n[0]:n}});GridMvc=function(n){function t(t,i){this.jqContainer=n(t);i=i||{};this.options=n.extend({},this.defaults(),i);this.init()}return t.prototype.init=function(){this.lang=GridMvc.lang[this.options.lang];typeof this.lang=="undefined"&&(this.lang=GridMvc.lang.en);this.events=[];this.options.selectable&&this.initGridRowsEvents();this.filterWidgets=[];this.addFilterWidget(new TextFilterWidget);this.addFilterWidget(new NumberFilterWidget);this.addFilterWidget(new DateTimeFilterWidget);this.addFilterWidget(new BooleanFilterWidget);this.openedMenuBtn=null;this.initFilters()},t.prototype.initGridRowsEvents=function(){var n=this;this.jqContainer.on("click",".grid-row",function(){n.rowClicked.call(this,n)})},t.prototype.rowClicked=function(t){var i,r,u;t.options.selectable&&((i=n(this).closest(".grid-row"),i.length<=0)||(r={},i.find(".grid-cell").each(function(){var t=n(this).attr("data-name");t.length>0&&(r[t]=n(this).text())}),u=n.Event("RowClicked"),t.notifyOnRowSelect(r,u),u.isDefaultPrevented()||t.markRowSelected(i)))},t.prototype.markRowSelected=function(n){this.jqContainer.find(".grid-row.grid-row-selected").removeClass("grid-row-selected");n.addClass("grid-row-selected")},t.prototype.defaults=function(){return{selectable:!0,multiplefilters:!1,lang:"en"}},t.prototype.onRowSelect=function(n){this.events.push({name:"onRowSelect",callback:n})},t.prototype.notifyOnRowSelect=function(n,t){t.row=n;this.notifyEvent("onRowSelect",t)},t.prototype.notifyEvent=function(n,t){for(var i=0;i<this.events.length;i++)if(this.events[i].name==n&&!this.events[i].callback(t))break},t.prototype.initFilters=function(){var i=this.filterMenuHtml(),t=this;this.jqContainer.find(".grid-filter").each(function(){n(this).click(function(){return t.openFilterPopup.call(this,t,i)})})},t.prototype.openFilterPopup=function(t,i){var o=n(this).attr("data-type")||"",r=t.getFilterWidgetForType(o),u,h,e,c;if(r==null)return!1;if(this.hasAttribute("data-rendered"))return u=t.openMenuOnClick.call(this,t),t.setupPopupInitialPosition(n(this)),u||typeof r.onShow=="undefined"||r.onShow(),u;var s=n(this).attr("data-name")||"",l=n(this).attr("data-filterdata")||"",a=n(this).attr("data-widgetdata")||"{}",v=t.parseFilterValues(l)||{},f=n(this).attr("data-url")||"";if(n(this).attr("data-rendered","1"),n(this).append(i),h=n(this).find(".grid-popup-widget"),typeof r.onRender!="undefined")r.onRender(h,t.lang,o,v,function(n){t.closeOpenedPopups();t.applyFilterValues(f,s,n,!1)},n.parseJSON(a));return n(this).find(".grid-filter-btn").hasClass("filtered")&&r.showClearFilterButton()&&(e=n(this).find(".grid-popup-additional"),e.append(t.getClearFilterButton(f)),e.find(".grid-filter-clear").click(function(){t.applyFilterValues(f,s,"",!0)})),c=t.openMenuOnClick.call(this,t),typeof r.onShow!="undefined"&&r.onShow(),t.setupPopupInitialPosition(n(this)),c},t.prototype.setupPopupInitialPosition=function(t){function e(){var n=t.find(".grid-dropdown-arrow");return{arrow:n,currentDropLeft:parseInt(r.css("left")),currentArrowLeft:parseInt(n.css("left"))}}var r=t.find(".grid-dropdown"),u=r.offset().left,o,f,i;if(u<0){i=e();i.arrow.css({left:i.currentArrowLeft+u-10+"px"});r.css({left:i.currentDropLeft-u+10+"px"});return}o=r.width();f=n(window).width()+n(window).scrollLeft()-(u+o);f<0&&(i=e(),i.arrow.css({left:i.currentArrowLeft-f+5+"px"}),r.css({left:i.currentDropLeft+f-5+"px"}))},t.prototype.filterMenuHtml=function(){return'<div class="dropdown dropdown-menu grid-dropdown" style="display: none;">                    <div class="grid-dropdown-arrow"><\/div>                    <div class="grid-dropdown-inner">                            <div class="grid-popup-widget"><\/div>                            <div class="grid-popup-additional"><\/div>                    <\/div>                <\/div>'},t.prototype.getClearFilterButton=function(){return'<ul class="menu-list">                    <li><a class="grid-filter-clear" href="javascript:void(0);">'+this.lang.clearFilterLabel+"<\/a><\/li>                <\/ul>"},t.prototype.addFilterWidget=function(n){this.filterWidgets.push(n)},t.prototype.parseFilterValues=function(t){for(var r=n.parseJSON(t),u=[],i=0;i<r.length;i++)u.push({filterValue:this.urldecode(r[i].FilterValue),filterType:r[i].FilterType,columnName:r[i].ColumnName});return u},t.prototype.urldecode=function(n){return decodeURIComponent((n+"").replace(/\+/g,"%20"))},t.prototype.getFilterWidgetForType=function(t){for(var i=0;i<this.filterWidgets.length;i++)if(n.inArray(t,this.filterWidgets[i].getAssociatedTypes())>=0)return this.filterWidgets[i];return null},t.prototype.replaceFilterWidget=function(t,i){for(var r=0;r<this.filterWidgets.length;r++)if(n.inArray(t,this.filterWidgets[r].getAssociatedTypes())>=0)return this.filterWidgets.splice(r,1),this.addFilterWidget(i),!0;return!1},t.prototype.applyFilterValues=function(t,i,r,u){var o=this.jqContainer.find(".grid-filter"),f,e,s;if(t.length>0&&(t+="&"),f="",u||(f+=this.getFilterQueryData(i,r)),this.options.multiplefilters)for(e=0;e<o.length;e++)if(n(o[e]).attr("data-name")!=i){if(s=this.parseFilterValues(n(o[e]).attr("data-filterdata")),s.length==0)continue;f.length>0&&(f+="&");f+=this.getFilterQueryData(n(o[e]).attr("data-name"),s)}else continue;window.location.search=t+f},t.prototype.getFilterQueryData=function(n,t){for(var r="",i=0;i<t.length;i++)r+="grid-filter="+encodeURIComponent(n)+"__"+t[i].filterType+"__"+encodeURIComponent(t[i].filterValue),i!=t.length-1&&(r+="&");return r},t.prototype.openMenuOnClick=function(t){if(n(this).hasClass("clicked"))return!0;t.closeOpenedPopups();n(this).addClass("clicked");var i=n(this).find(".dropdown-menu");return i.length==0?!0:(i.show(),i.addClass("opened"),t.openedMenuBtn=n(this),n(document).bind("click.gridmvc",function(n){t.documentCallback(n,t)}),!1)},t.prototype.documentCallback=function(t,i){t=t||event;var r=t.target||t.srcElement,u=n(".dropdown-menu.opened").get(0),f=n("html").get(0);if(typeof u!="undefined")do{if(u==r)return;f==r&&(u.style.display="none",n(u).removeClass("opened"));r=r.parentNode}while(r);i.openedMenuBtn!=null&&i.openedMenuBtn.removeClass("clicked");n(document).unbind("click.gridmvc")},t.prototype.closeOpenedPopups=function(){var t=n(".dropdown-menu.opened");t.hide();t.removeClass("opened");this.openedMenuBtn!=null&&this.openedMenuBtn.removeClass("clicked")},t.prototype.selectable=function(n){this.options.selectable=n},t}(window.jQuery);typeof GridMvc.lang=="undefined"&&(GridMvc.lang={});GridMvc.lang.en={filterTypeLabel:"Type: ",filterValueLabel:"Value:",applyFilterButtonText:"Apply",filterSelectTypes:{Equals:"Equals",StartsWith:"StartsWith",Contains:"Contains",EndsWith:"EndsWith",GreaterThan:"Greater than",LessThan:"Less than",GreaterThanOrEquals:"Greater than or equals",LessThanOrEquals:"Less than or equals"},code:"en",boolTrueLabel:"Yes",boolFalseLabel:"No",clearFilterLabel:"Clear filter"};TextFilterWidget=function(){function n(){}return n.prototype.getAssociatedTypes=function(){return["System.String"]},n.prototype.onShow=function(){var n=this.container.find(".grid-filter-input");n.length<=0||n.focus()},n.prototype.showClearFilterButton=function(){return!0},n.prototype.onRender=function(n,t,i,r,u){this.cb=u;this.container=n;this.lang=t;this.value=r.length>0?r[0]:{filterType:1,filterValue:""};this.renderWidget();this.registerEvents()},n.prototype.renderWidget=function(){var n='<div class="form-group">                        <label>'+this.lang.filterTypeLabel+'<\/label>                        <select class="grid-filter-type form-control">                            <option value="1" '+(this.value.filterType=="1"?'selected="selected"':"")+">"+this.lang.filterSelectTypes.Equals+'<\/option>                            <option value="2" '+(this.value.filterType=="2"?'selected="selected"':"")+">"+this.lang.filterSelectTypes.Contains+'<\/option>                            <option value="3" '+(this.value.filterType=="3"?'selected="selected"':"")+">"+this.lang.filterSelectTypes.StartsWith+'<\/option>                            <option value="4" '+(this.value.filterType=="4"?'selected="selected"':"")+">"+this.lang.filterSelectTypes.EndsWith+'<\/option>                        <\/select>                    <\/div>                    <div class="form-group">                        <label>'+this.lang.filterValueLabel+'<\/label>                        <input type="text" class="grid-filter-input form-control" value="'+this.value.filterValue+'" />                    <\/div>                    <div class="grid-filter-buttons">                        <button type="button" class="btn btn-primary grid-apply" >'+this.lang.applyFilterButtonText+"<\/button>                    <\/div>";this.container.append(n)},n.prototype.registerEvents=function(){var t=this.container.find(".grid-apply"),n=this;t.click(function(){var t=n.container.find(".grid-filter-type").val(),i=n.container.find(".grid-filter-input").val(),r=[{filterType:t,filterValue:i}];n.cb(r)});this.container.find(".grid-filter-input").keyup(function(n){n.keyCode==13&&t.click();n.keyCode==27&&GridMvc.closeOpenedPopups()})},n}(window.jQuery);NumberFilterWidget=function(){function n(){}return n.prototype.showClearFilterButton=function(){return!0},n.prototype.getAssociatedTypes=function(){return["System.Int32","System.Double","System.Decimal","System.Byte","System.Single","System.Float","System.Int64","System.Int16","System.UInt64","System.UInt32","System.UInt16"]},n.prototype.onShow=function(){var n=this.container.find(".grid-filter-input");n.length<=0||n.focus()},n.prototype.onRender=function(n,t,i,r,u){this.cb=u;this.container=n;this.lang=t;this.typeName=i;this.value=r.length>0?r[0]:{filterType:1,filterValue:""};this.renderWidget();this.registerEvents()},n.prototype.renderWidget=function(){var n='<div class="form-group">                        <label>'+this.lang.filterTypeLabel+'<\/label>                        <select class="grid-filter-type form-control">                            <option value="1" '+(this.value.filterType=="1"?'selected="selected"':"")+">"+this.lang.filterSelectTypes.Equals+'<\/option>                            <option value="5" '+(this.value.filterType=="5"?'selected="selected"':"")+">"+this.lang.filterSelectTypes.GreaterThan+'<\/option>                            <option value="6" '+(this.value.filterType=="6"?'selected="selected"':"")+">"+this.lang.filterSelectTypes.LessThan+'<\/option>                        <\/select>                    <\/div>                    <div class="form-group">                        <label>'+this.lang.filterValueLabel+'<\/label>                        <input type="text" class="grid-filter-input form-control" value="'+this.value.filterValue+'" />                    <\/div>                    <div class="grid-filter-buttons">                        <button type="button" class="btn btn-primary grid-apply">'+this.lang.applyFilterButtonText+"<\/button>                    <\/div>";this.container.append(n)},n.prototype.registerEvents=function(){var n=this,i=this.container.find(".grid-apply"),t;i.click(function(){var t=n.container.find(".grid-filter-type").val(),i=n.container.find(".grid-filter-input").val(),r=[{filterType:t,filterValue:i}];n.cb(r)});t=this.container.find(".grid-filter-input");t.keyup(function(n){n.keyCode==13&&i.click();n.keyCode==27&&GridMvc.closeOpenedPopups()}).keypress(function(t){return n.validateInput.call(n,t)});this.typeName=="System.Byte"&&t.attr("maxlength","3")},n.prototype.validateInput=function(n){var t=n||window.event,i=t.keyCode||t.which,r;i=String.fromCharCode(i);switch(this.typeName){case"System.Byte":case"System.Int32":case"System.Int64":case"System.UInt32":case"System.UInt64":r=/[0-9]/;break;default:r=/[0-9]|\.|\,/}r.test(i)||(t.returnValue=!1,t.preventDefault&&t.preventDefault())},n}(window.jQuery);DateTimeFilterWidget=function(n){function t(){}return t.prototype.getAssociatedTypes=function(){return["System.DateTime","System.Date","System.DateTimeOffset"]},t.prototype.showClearFilterButton=function(){return!0},t.prototype.onRender=function(t,i,r,u,f,e){this.datePickerIncluded=typeof n.fn.datepicker!="undefined";this.cb=f;this.data=e;this.typeName=r;this.container=t;this.lang=i;this.value=u.length>0?u[0]:{filterType:1,filterValue:""};this.renderWidget();this.registerEvents()},t.prototype.renderWidget=function(){var r='<div class="form-group">                        <label>'+this.lang.filterTypeLabel+'<\/label>                        <select class="grid-filter-type form-control">                            <option value="1" '+(this.value.filterType=="1"?'selected="selected"':"")+">"+this.lang.filterSelectTypes.Equals+'<\/option>                            <option value="5" '+(this.value.filterType=="5"?'selected="selected"':"")+">"+this.lang.filterSelectTypes.GreaterThan+'<\/option>                            <option value="6" '+(this.value.filterType=="6"?'selected="selected"':"")+">"+this.lang.filterSelectTypes.LessThan+"<\/option>                        <\/select>                    <\/div>"+(this.datePickerIncluded?'<div class="grid-filter-datepicker"><\/div>':'<div class="form-group">                                <label>'+this.lang.filterValueLabel+'<\/label>                                <input type="text" class="grid-filter-input form-control" value="'+this.value.filterValue+'" />                             <\/div>                             <div class="grid-filter-buttons">                                <input type="button" class="btn btn-primary grid-apply" value="'+this.lang.applyFilterButtonText+'" />                             <\/div>'),n,t,i;if(this.container.append(r),this.datePickerIncluded){n=this.data||{};n.format=n.format||"yyyy-mm-dd";n.language=n.language||this.lang.code;t=this;i=this.container.find(".grid-filter-datepicker");i.datepicker(n).on("changeDate",function(n){var i=t.container.find(".grid-filter-type").val(),r=[{filterType:i,filterValue:n.format()}];t.cb(r)});this.value.filterValue&&i.datepicker("update",this.value.filterValue)}},t.prototype.registerEvents=function(){var n=this,t=this.container.find(".grid-apply");t.click(function(){var t=n.container.find(".grid-filter-type").val(),i=n.container.find(".grid-filter-input").val(),r=[{filterType:t,filterValue:i}];n.cb(r)});this.container.find(".grid-filter-input").keyup(function(n){n.keyCode==13&&t.click()})},t}(window.jQuery);BooleanFilterWidget=function(n){function t(){}return t.prototype.getAssociatedTypes=function(){return["System.Boolean"]},t.prototype.showClearFilterButton=function(){return!0},t.prototype.onRender=function(n,t,i,r,u){this.cb=u;this.container=n;this.lang=t;this.value=r.length>0?r[0]:{filterType:1,filterValue:""};this.renderWidget();this.registerEvents()},t.prototype.renderWidget=function(){var n="<label>"+this.lang.filterValueLabel+'<\/label>                    <ul class="menu-list">                        <li><a class="grid-filter-choose '+(this.value.filterValue=="true"?"choose-selected":"")+'" data-value="true" href="javascript:void(0);">'+this.lang.boolTrueLabel+'<\/a><\/li>                        <li><a class="grid-filter-choose '+(this.value.filterValue=="false"?"choose-selected":"")+'" data-value="false" href="javascript:void(0);">'+this.lang.boolFalseLabel+"<\/a><\/li>                    <\/ul>";this.container.append(n)},t.prototype.registerEvents=function(){var t=this,i=this.container.find(".grid-filter-choose");i.click(function(){var i=[{filterType:"1",filterValue:n(this).attr("data-value")}];t.cb(i)})},t}(window.jQuery),function(n){n&&n(function(){n(".grid-mvc").each(function(){n(".grid-mvc").gridmvc()})})}(window.jQuery);