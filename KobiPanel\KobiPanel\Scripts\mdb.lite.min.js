var WOW;jQuery.easing.jswing=jQuery.easing.swing,jQuery.extend(jQuery.easing,{def:"easeOutQuad",swing:function(t,e,i,n,o){return jQuery.easing[jQuery.easing.def](t,e,i,n,o)},easeInQuad:function(t,e,i,n,o){return n*(e/=o)*e+i},easeOutQuad:function(t,e,i,n,o){return-n*(e/=o)*(e-2)+i},easeInOutQuad:function(t,e,i,n,o){return(e/=o/2)<1?n/2*e*e+i:-n/2*(--e*(e-2)-1)+i},easeInCubic:function(t,e,i,n,o){return n*(e/=o)*e*e+i},easeOutCubic:function(t,e,i,n,o){return n*((e=e/o-1)*e*e+1)+i},easeInOutCubic:function(t,e,i,n,o){return(e/=o/2)<1?n/2*e*e*e+i:n/2*((e-=2)*e*e+2)+i},easeInQuart:function(t,e,i,n,o){return n*(e/=o)*e*e*e+i},easeOutQuart:function(t,e,i,n,o){return-n*((e=e/o-1)*e*e*e-1)+i},easeInOutQuart:function(t,e,i,n,o){return(e/=o/2)<1?n/2*e*e*e*e+i:-n/2*((e-=2)*e*e*e-2)+i},easeInQuint:function(t,e,i,n,o){return n*(e/=o)*e*e*e*e+i},easeOutQuint:function(t,e,i,n,o){return n*((e=e/o-1)*e*e*e*e+1)+i},easeInOutQuint:function(t,e,i,n,o){return(e/=o/2)<1?n/2*e*e*e*e*e+i:n/2*((e-=2)*e*e*e*e+2)+i},easeInSine:function(t,e,i,n,o){return-n*Math.cos(e/o*(Math.PI/2))+n+i},easeOutSine:function(t,e,i,n,o){return n*Math.sin(e/o*(Math.PI/2))+i},easeInOutSine:function(t,e,i,n,o){return-n/2*(Math.cos(Math.PI*e/o)-1)+i},easeInExpo:function(t,e,i,n,o){return 0==e?i:n*Math.pow(2,10*(e/o-1))+i},easeOutExpo:function(t,e,i,n,o){return e==o?i+n:n*(1-Math.pow(2,-10*e/o))+i},easeInOutExpo:function(t,e,i,n,o){return 0==e?i:e==o?i+n:(e/=o/2)<1?n/2*Math.pow(2,10*(e-1))+i:n/2*(2-Math.pow(2,-10*--e))+i},easeInCirc:function(t,e,i,n,o){return-n*(Math.sqrt(1-(e/=o)*e)-1)+i},easeOutCirc:function(t,e,i,n,o){return n*Math.sqrt(1-(e=e/o-1)*e)+i},easeInOutCirc:function(t,e,i,n,o){return(e/=o/2)<1?-n/2*(Math.sqrt(1-e*e)-1)+i:n/2*(Math.sqrt(1-(e-=2)*e)+1)+i},easeInElastic:function(t,e,i,n,o){var a=1.70158,r=0,s=n;if(0==e)return i;if(1==(e/=o))return i+n;if(r||(r=.3*o),s<Math.abs(n)){s=n;a=r/4}else a=r/(2*Math.PI)*Math.asin(n/s);return-s*Math.pow(2,10*(e-=1))*Math.sin((e*o-a)*(2*Math.PI)/r)+i},easeOutElastic:function(t,e,i,n,o){var a=1.70158,r=0,s=n;if(0==e)return i;if(1==(e/=o))return i+n;if(r||(r=.3*o),s<Math.abs(n)){s=n;a=r/4}else a=r/(2*Math.PI)*Math.asin(n/s);return s*Math.pow(2,-10*e)*Math.sin((e*o-a)*(2*Math.PI)/r)+n+i},easeInOutElastic:function(t,e,i,n,o){var a=1.70158,r=0,s=n;if(0==e)return i;if(2==(e/=o/2))return i+n;if(r||(r=o*(.3*1.5)),s<Math.abs(n)){s=n;a=r/4}else a=r/(2*Math.PI)*Math.asin(n/s);return e<1?s*Math.pow(2,10*(e-=1))*Math.sin((e*o-a)*(2*Math.PI)/r)*-.5+i:s*Math.pow(2,-10*(e-=1))*Math.sin((e*o-a)*(2*Math.PI)/r)*.5+n+i},easeInBack:function(t,e,i,n,o,a){return void 0==a&&(a=1.70158),n*(e/=o)*e*((a+1)*e-a)+i},easeOutBack:function(t,e,i,n,o,a){return void 0==a&&(a=1.70158),n*((e=e/o-1)*e*((a+1)*e+a)+1)+i},easeInOutBack:function(t,e,i,n,o,a){return void 0==a&&(a=1.70158),(e/=o/2)<1?n/2*(e*e*((1+(a*=1.525))*e-a))+i:n/2*((e-=2)*e*((1+(a*=1.525))*e+a)+2)+i},easeInBounce:function(t,e,i,n,o){return n-jQuery.easing.easeOutBounce(t,o-e,0,n,o)+i},easeOutBounce:function(t,e,i,n,o){return(e/=o)<1/2.75?n*(7.5625*e*e)+i:e<2/2.75?n*(7.5625*(e-=1.5/2.75)*e+.75)+i:e<2.5/2.75?n*(7.5625*(e-=2.25/2.75)*e+.9375)+i:n*(7.5625*(e-=2.625/2.75)*e+.984375)+i},easeInOutBounce:function(t,e,i,n,o){return e<o/2?.5*jQuery.easing.easeInBounce(t,2*e,0,n,o)+i:.5*jQuery.easing.easeOutBounce(t,2*e-o,0,n,o)+.5*n+i}}),jQuery.Velocity?console.log("Velocity is already loaded. You may be needlessly importing Velocity again; note that Materialize includes Velocity."):(function(t){function e(t){var e=t.length,n=i.type(t);return"function"!==n&&!i.isWindow(t)&&(!(1!==t.nodeType||!e)||("array"===n||0===e||"number"==typeof e&&e>0&&e-1 in t))}if(!t.jQuery){var i=function(t,e){return new i.fn.init(t,e)};i.isWindow=function(t){return null!=t&&t==t.window},i.type=function(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?o[r.call(t)]||"object":typeof t},i.isArray=Array.isArray||function(t){return"array"===i.type(t)},i.isPlainObject=function(t){var e;if(!t||"object"!==i.type(t)||t.nodeType||i.isWindow(t))return!1;try{if(t.constructor&&!a.call(t,"constructor")&&!a.call(t.constructor.prototype,"isPrototypeOf"))return!1}catch(t){return!1}for(e in t);return void 0===e||a.call(t,e)},i.each=function(t,i,n){var o=0,a=t.length,r=e(t);if(n){if(r)for(;a>o&&!1!==i.apply(t[o],n);o++);else for(o in t)if(!1===i.apply(t[o],n))break}else if(r)for(;a>o&&!1!==i.call(t[o],o,t[o]);o++);else for(o in t)if(!1===i.call(t[o],o,t[o]))break;return t},i.data=function(t,e,o){if(void 0===o){var a=(r=t[i.expando])&&n[r];if(void 0===e)return a;if(a&&e in a)return a[e]}else if(void 0!==e){var r=t[i.expando]||(t[i.expando]=++i.uuid);return n[r]=n[r]||{},n[r][e]=o,o}},i.removeData=function(t,e){var o=t[i.expando],a=o&&n[o];a&&i.each(e,function(t,e){delete a[e]})},i.extend=function(){var t,e,n,o,a,r,s=arguments[0]||{},l=1,c=arguments.length,u=!1;for("boolean"==typeof s&&(u=s,s=arguments[l]||{},l++),"object"!=typeof s&&"function"!==i.type(s)&&(s={}),l===c&&(s=this,l--);c>l;l++)if(null!=(a=arguments[l]))for(o in a)t=s[o],s!==(n=a[o])&&(u&&n&&(i.isPlainObject(n)||(e=i.isArray(n)))?(e?(e=!1,r=t&&i.isArray(t)?t:[]):r=t&&i.isPlainObject(t)?t:{},s[o]=i.extend(u,r,n)):void 0!==n&&(s[o]=n));return s},i.queue=function(t,n,o){if(t){n=(n||"fx")+"queue";var a=i.data(t,n);return o?(!a||i.isArray(o)?a=i.data(t,n,function(t,i){var n=i||[];return null!=t&&(e(Object(t))?function(t,e){for(var i=+e.length,n=0,o=t.length;i>n;)t[o++]=e[n++];if(i!=i)for(;void 0!==e[n];)t[o++]=e[n++];t.length=o}(n,"string"==typeof t?[t]:t):[].push.call(n,t)),n}(o)):a.push(o),a):a||[]}},i.dequeue=function(t,e){i.each(t.nodeType?[t]:t,function(t,n){e=e||"fx";var o=i.queue(n,e),a=o.shift();"inprogress"===a&&(a=o.shift()),a&&("fx"===e&&o.unshift("inprogress"),a.call(n,function(){i.dequeue(n,e)}))})},i.fn=i.prototype={init:function(t){if(t.nodeType)return this[0]=t,this;throw new Error("Not a DOM node.")},offset:function(){var e=this[0].getBoundingClientRect?this[0].getBoundingClientRect():{top:0,left:0};return{top:e.top+(t.pageYOffset||document.scrollTop||0)-(document.clientTop||0),left:e.left+(t.pageXOffset||document.scrollLeft||0)-(document.clientLeft||0)}},position:function(){function t(){for(var t=this.offsetParent||document;t&&"html"===!t.nodeType.toLowerCase&&"static"===t.style.position;)t=t.offsetParent;return t||document}var e=this[0],t=t.apply(e),n=this.offset(),o=/^(?:body|html)$/i.test(t.nodeName)?{top:0,left:0}:i(t).offset();return n.top-=parseFloat(e.style.marginTop)||0,n.left-=parseFloat(e.style.marginLeft)||0,t.style&&(o.top+=parseFloat(t.style.borderTopWidth)||0,o.left+=parseFloat(t.style.borderLeftWidth)||0),{top:n.top-o.top,left:n.left-o.left}}};var n={};i.expando="velocity"+(new Date).getTime(),i.uuid=0;for(var o={},a=o.hasOwnProperty,r=o.toString,s="Boolean Number String Function Array Date RegExp Object Error".split(" "),l=0;l<s.length;l++)o["[object "+s[l]+"]"]=s[l].toLowerCase();i.fn.init.prototype=i.fn,t.Velocity={Utilities:i}}}(window),function(t){"object"==typeof module&&"object"==typeof module.exports?module.exports=t():"function"==typeof define&&define.amd?define(t):t()}(function(){return function(t,e,i,n){function o(t){return p.isWrapped(t)?t=[].slice.call(t):p.isNode(t)&&(t=[t]),t}function a(t){var e=u.data(t,"velocity");return null===e?n:e}function r(t,i,n,o){function a(t,e){return 1-3*e+3*t}function r(t,e){return 3*e-6*t}function s(t){return 3*t}function l(t,e,i){return((a(e,i)*t+r(e,i))*t+s(e))*t}function c(t,e,i){return 3*a(e,i)*t*t+2*r(e,i)*t+s(e)}function u(e,i){for(var o=0;p>o;++o){var a=c(i,t,n);if(0===a)return i;i-=(l(i,t,n)-e)/a}return i}function d(e,i,o){var a,r,s=0;do{(a=l(r=i+(o-i)/2,t,n)-e)>0?o=r:i=r}while(Math.abs(a)>v&&++s<g);return r}function h(){x=!0,(t!=i||n!=o)&&function(){for(var e=0;m>e;++e)S[e]=l(e*b,t,n)}()}var p=4,f=.001,v=1e-7,g=10,m=11,b=1/(m-1),y="Float32Array"in e;if(4!==arguments.length)return!1;for(var w=0;4>w;++w)if("number"!=typeof arguments[w]||isNaN(arguments[w])||!isFinite(arguments[w]))return!1;t=Math.min(t,1),n=Math.min(n,1),t=Math.max(t,0),n=Math.max(n,0);var S=y?new Float32Array(m):new Array(m),x=!1,T=function(e){return x||h(),t===i&&n===o?e:0===e?0:1===e?1:l(function(e){for(var i=0,o=1,a=m-1;o!=a&&S[o]<=e;++o)i+=b;var r=i+(e-S[--o])/(S[o+1]-S[o])*b,s=c(r,t,n);return s>=f?u(e,r):0==s?r:d(e,i,i+b)}(e),i,o)};T.getControlPoints=function(){return[{x:t,y:i},{x:n,y:o}]};var C="generateBezier("+[t,i,n,o]+")";return T.toString=function(){return C},T}function s(t,e){var i=t;return p.isString(t)?m.Easings[t]||(i=!1):i=p.isArray(t)&&1===t.length?function(t){return function(e){return Math.round(e*t)*(1/t)}}.apply(null,t):p.isArray(t)&&2===t.length?b.apply(null,t.concat([e])):!(!p.isArray(t)||4!==t.length)&&r.apply(null,t),!1===i&&(i=m.Easings[m.defaults.easing]?m.defaults.easing:g),i}function l(t){if(t){var e=(new Date).getTime(),i=m.State.calls.length;i>1e4&&(m.State.calls=function(t){for(var e=-1,i=t?t.length:0,n=[];++e<i;){var o=t[e];o&&n.push(o)}return n}(m.State.calls));for(var o=0;i>o;o++)if(m.State.calls[o]){var r=m.State.calls[o],s=r[0],d=r[2],h=r[3],f=!!h,v=null;h||(h=m.State.calls[o][3]=e-16);for(var g=Math.min((e-h)/d.duration,1),b=0,w=s.length;w>b;b++){var x=s[b],T=x.element;if(a(T)){var C=!1;if(d.display!==n&&null!==d.display&&"none"!==d.display){if("flex"===d.display){u.each(["-webkit-box","-moz-box","-ms-flexbox","-webkit-flex"],function(t,e){y.setPropertyValue(T,"display",e)})}y.setPropertyValue(T,"display",d.display)}for(var k in d.visibility!==n&&"hidden"!==d.visibility&&y.setPropertyValue(T,"visibility",d.visibility),x)if("element"!==k){var E,O=x[k],$=p.isString(O.easing)?m.Easings[O.easing]:O.easing;if(1===g)E=O.endValue;else{var P=O.endValue-O.startValue;if(E=O.startValue+P*$(g,d,P),!f&&E===O.currentValue)continue}if(O.currentValue=E,"tween"===k)v=E;else{if(y.Hooks.registered[k]){var M=y.Hooks.getRoot(k),L=a(T).rootPropertyValueCache[M];L&&(O.rootPropertyValue=L)}var A=y.setPropertyValue(T,k,O.currentValue+(0===parseFloat(E)?"":O.unitType),O.rootPropertyValue,O.scrollData);y.Hooks.registered[k]&&(a(T).rootPropertyValueCache[M]=y.Normalizations.registered[M]?y.Normalizations.registered[M]("extract",null,A[1]):A[1]),"transform"===A[0]&&(C=!0)}}d.mobileHA&&a(T).transformCache.translate3d===n&&(a(T).transformCache.translate3d="(0px, 0px, 0px)",C=!0),C&&y.flushTransformCache(T)}}d.display!==n&&"none"!==d.display&&(m.State.calls[o][2].display=!1),d.visibility!==n&&"hidden"!==d.visibility&&(m.State.calls[o][2].visibility=!1),d.progress&&d.progress.call(r[1],r[1],g,Math.max(0,h+d.duration-e),h,v),1===g&&c(o)}}m.State.isTicking&&S(l)}function c(t,e){if(!m.State.calls[t])return!1;for(var i=m.State.calls[t][0],o=m.State.calls[t][1],r=m.State.calls[t][2],s=m.State.calls[t][4],l=!1,c=0,d=i.length;d>c;c++){var h=i[c].element;if(e||r.loop||("none"===r.display&&y.setPropertyValue(h,"display",r.display),"hidden"===r.visibility&&y.setPropertyValue(h,"visibility",r.visibility)),!0!==r.loop&&(u.queue(h)[1]===n||!/\.velocityQueueEntryFlag/i.test(u.queue(h)[1]))&&a(h)){a(h).isAnimating=!1,a(h).rootPropertyValueCache={};var p=!1;u.each(y.Lists.transforms3D,function(t,e){var i=/^scale/.test(e)?1:0,o=a(h).transformCache[e];a(h).transformCache[e]!==n&&new RegExp("^\\("+i+"[^.]").test(o)&&(p=!0,delete a(h).transformCache[e])}),r.mobileHA&&(p=!0,delete a(h).transformCache.translate3d),p&&y.flushTransformCache(h),y.Values.removeClass(h,"velocity-animating")}if(!e&&r.complete&&!r.loop&&c===d-1)try{r.complete.call(o,o)}catch(t){setTimeout(function(){throw t},1)}s&&!0!==r.loop&&s(o),a(h)&&!0===r.loop&&!e&&(u.each(a(h).tweensContainer,function(t,e){/^rotate/.test(t)&&360===parseFloat(e.endValue)&&(e.endValue=0,e.startValue=360),/^backgroundPosition/.test(t)&&100===parseFloat(e.endValue)&&"%"===e.unitType&&(e.endValue=0,e.startValue=100)}),m(h,"reverse",{loop:!0,delay:r.delay})),!1!==r.queue&&u.dequeue(h,r.queue)}m.State.calls[t]=!1;for(var f=0,v=m.State.calls.length;v>f;f++)if(!1!==m.State.calls[f]){l=!0;break}!1===l&&(m.State.isTicking=!1,delete m.State.calls,m.State.calls=[])}var u,d=function(){if(i.documentMode)return i.documentMode;for(var t=7;t>4;t--){var e=i.createElement("div");if(e.innerHTML="\x3c!--[if IE "+t+"]><span></span><![endif]--\x3e",e.getElementsByTagName("span").length)return e=null,t}return n}(),h=function(){var t=0;return e.webkitRequestAnimationFrame||e.mozRequestAnimationFrame||function(e){var i,n=(new Date).getTime();return i=Math.max(0,16-(n-t)),t=n+i,setTimeout(function(){e(n+i)},i)}}(),p={isString:function(t){return"string"==typeof t},isArray:Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},isFunction:function(t){return"[object Function]"===Object.prototype.toString.call(t)},isNode:function(t){return t&&t.nodeType},isNodeList:function(t){return"object"==typeof t&&/^\[object (HTMLCollection|NodeList|Object)\]$/.test(Object.prototype.toString.call(t))&&t.length!==n&&(0===t.length||"object"==typeof t[0]&&t[0].nodeType>0)},isWrapped:function(t){return t&&(t.jquery||e.Zepto&&e.Zepto.zepto.isZ(t))},isSVG:function(t){return e.SVGElement&&t instanceof e.SVGElement},isEmptyObject:function(t){for(var e in t)return!1;return!0}},f=!1;if(t.fn&&t.fn.jquery?(u=t,f=!0):u=e.Velocity.Utilities,8>=d&&!f)throw new Error("Velocity: IE8 and below require jQuery to be loaded before Velocity.");if(!(7>=d)){var v=400,g="swing",m={State:{isMobile:/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),isAndroid:/Android/i.test(navigator.userAgent),isGingerbread:/Android 2\.3\.[3-7]/i.test(navigator.userAgent),isChrome:e.chrome,isFirefox:/Firefox/i.test(navigator.userAgent),prefixElement:i.createElement("div"),prefixMatches:{},scrollAnchor:null,scrollPropertyLeft:null,scrollPropertyTop:null,isTicking:!1,calls:[]},CSS:{},Utilities:u,Redirects:{},Easings:{},Promise:e.Promise,defaults:{queue:"",duration:v,easing:g,begin:n,complete:n,progress:n,display:n,visibility:n,loop:!1,delay:!1,mobileHA:!0,_cacheValues:!0},init:function(t){u.data(t,"velocity",{isSVG:p.isSVG(t),isAnimating:!1,computedStyle:null,tweensContainer:null,rootPropertyValueCache:{},transformCache:{}})},hook:null,mock:!1,version:{major:1,minor:2,patch:2},debug:!1};e.pageYOffset!==n?(m.State.scrollAnchor=e,m.State.scrollPropertyLeft="pageXOffset",m.State.scrollPropertyTop="pageYOffset"):(m.State.scrollAnchor=i.documentElement||i.body.parentNode||i.body,m.State.scrollPropertyLeft="scrollLeft",m.State.scrollPropertyTop="scrollTop");var b=function(){function t(t){return-t.tension*t.x-t.friction*t.v}function e(e,i,n){var o={x:e.x+n.dx*i,v:e.v+n.dv*i,tension:e.tension,friction:e.friction};return{dx:o.v,dv:t(o)}}function i(i,n){var o={dx:i.v,dv:t(i)},a=e(i,.5*n,o),r=e(i,.5*n,a),s=e(i,n,r),l=1/6*(o.dx+2*(a.dx+r.dx)+s.dx),c=1/6*(o.dv+2*(a.dv+r.dv)+s.dv);return i.x=i.x+l*n,i.v=i.v+c*n,i}return function t(e,n,o){var a,r,s,l={x:-1,v:0,tension:null,friction:null},c=[0],u=0;for(e=parseFloat(e)||500,n=parseFloat(n)||20,o=o||null,l.tension=e,l.friction=n,(a=null!==o)?r=(u=t(e,n))/o*.016:r=.016;s=i(s||l,r),c.push(1+s.x),u+=16,Math.abs(s.x)>1e-4&&Math.abs(s.v)>1e-4;);return a?function(t){return c[t*(c.length-1)|0]}:u}}();m.Easings={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},spring:function(t){return 1-Math.cos(4.5*t*Math.PI)*Math.exp(6*-t)}},u.each([["ease",[.25,.1,.25,1]],["ease-in",[.42,0,1,1]],["ease-out",[0,0,.58,1]],["ease-in-out",[.42,0,.58,1]],["easeInSine",[.47,0,.745,.715]],["easeOutSine",[.39,.575,.565,1]],["easeInOutSine",[.445,.05,.55,.95]],["easeInQuad",[.55,.085,.68,.53]],["easeOutQuad",[.25,.46,.45,.94]],["easeInOutQuad",[.455,.03,.515,.955]],["easeInCubic",[.55,.055,.675,.19]],["easeOutCubic",[.215,.61,.355,1]],["easeInOutCubic",[.645,.045,.355,1]],["easeInQuart",[.895,.03,.685,.22]],["easeOutQuart",[.165,.84,.44,1]],["easeInOutQuart",[.77,0,.175,1]],["easeInQuint",[.755,.05,.855,.06]],["easeOutQuint",[.23,1,.32,1]],["easeInOutQuint",[.86,0,.07,1]],["easeInExpo",[.95,.05,.795,.035]],["easeOutExpo",[.19,1,.22,1]],["easeInOutExpo",[1,0,0,1]],["easeInCirc",[.6,.04,.98,.335]],["easeOutCirc",[.075,.82,.165,1]],["easeInOutCirc",[.785,.135,.15,.86]]],function(t,e){m.Easings[e[0]]=r.apply(null,e[1])});var y=m.CSS={RegEx:{isHex:/^#([A-f\d]{3}){1,2}$/i,valueUnwrap:/^[A-z]+\((.*)\)$/i,wrappedValueAlreadyExtracted:/[0-9.]+ [0-9.]+ [0-9.]+( [0-9.]+)?/,valueSplit:/([A-z]+\(.+\))|(([A-z0-9#-.]+?)(?=\s|$))/gi},Lists:{colors:["fill","stroke","stopColor","color","backgroundColor","borderColor","borderTopColor","borderRightColor","borderBottomColor","borderLeftColor","outlineColor"],transformsBase:["translateX","translateY","scale","scaleX","scaleY","skewX","skewY","rotateZ"],transforms3D:["transformPerspective","translateZ","scaleZ","rotateX","rotateY"]},Hooks:{templates:{textShadow:["Color X Y Blur","black 0px 0px 0px"],boxShadow:["Color X Y Blur Spread","black 0px 0px 0px 0px"],clip:["Top Right Bottom Left","0px 0px 0px 0px"],backgroundPosition:["X Y","0% 0%"],transformOrigin:["X Y Z","50% 50% 0px"],perspectiveOrigin:["X Y","50% 50%"]},registered:{},register:function(){for(var t=0;t<y.Lists.colors.length;t++){var e="color"===y.Lists.colors[t]?"0 0 0 1":"255 255 255 1";y.Hooks.templates[y.Lists.colors[t]]=["Red Green Blue Alpha",e]}var i,n,o;if(d)for(i in y.Hooks.templates){o=(n=y.Hooks.templates[i])[0].split(" ");var a=n[1].match(y.RegEx.valueSplit);"Color"===o[0]&&(o.push(o.shift()),a.push(a.shift()),y.Hooks.templates[i]=[o.join(" "),a.join(" ")])}for(i in y.Hooks.templates)for(var t in o=(n=y.Hooks.templates[i])[0].split(" ")){var r=i+o[t],s=t;y.Hooks.registered[r]=[i,s]}},getRoot:function(t){var e=y.Hooks.registered[t];return e?e[0]:t},cleanRootPropertyValue:function(t,e){return y.RegEx.valueUnwrap.test(e)&&(e=e.match(y.RegEx.valueUnwrap)[1]),y.Values.isCSSNullValue(e)&&(e=y.Hooks.templates[t][1]),e},extractValue:function(t,e){var i=y.Hooks.registered[t];if(i){var n=i[0],o=i[1];return(e=y.Hooks.cleanRootPropertyValue(n,e)).toString().match(y.RegEx.valueSplit)[o]}return e},injectValue:function(t,e,i){var n=y.Hooks.registered[t];if(n){var o,a=n[0],r=n[1];return(o=(i=y.Hooks.cleanRootPropertyValue(a,i)).toString().match(y.RegEx.valueSplit))[r]=e,o.join(" ")}return i}},Normalizations:{registered:{clip:function(t,e,i){switch(t){case"name":return"clip";case"extract":var n;return y.RegEx.wrappedValueAlreadyExtracted.test(i)?n=i:n=(n=i.toString().match(y.RegEx.valueUnwrap))?n[1].replace(/,(\s+)?/g," "):i,n;case"inject":return"rect("+i+")"}},blur:function(t,e,i){switch(t){case"name":return m.State.isFirefox?"filter":"-webkit-filter";case"extract":var n=parseFloat(i);if(!n&&0!==n){var o=i.toString().match(/blur\(([0-9]+[A-z]+)\)/i);n=o?o[1]:0}return n;case"inject":return parseFloat(i)?"blur("+i+")":"none"}},opacity:function(t,e,i){if(8>=d)switch(t){case"name":return"filter";case"extract":var n=i.toString().match(/alpha\(opacity=(.*)\)/i);return n?n[1]/100:1;case"inject":return e.style.zoom=1,parseFloat(i)>=1?"":"alpha(opacity="+parseInt(100*parseFloat(i),10)+")"}else switch(t){case"name":return"opacity";case"extract":case"inject":return i}}},register:function(){9>=d||m.State.isGingerbread||(y.Lists.transformsBase=y.Lists.transformsBase.concat(y.Lists.transforms3D));for(var t=0;t<y.Lists.transformsBase.length;t++)!function(){var e=y.Lists.transformsBase[t];y.Normalizations.registered[e]=function(t,i,o){switch(t){case"name":return"transform";case"extract":return a(i)===n||a(i).transformCache[e]===n?/^scale/i.test(e)?1:0:a(i).transformCache[e].replace(/[()]/g,"");case"inject":var r=!1;switch(e.substr(0,e.length-1)){case"translate":r=!/(%|px|em|rem|vw|vh|\d)$/i.test(o);break;case"scal":case"scale":m.State.isAndroid&&a(i).transformCache[e]===n&&1>o&&(o=1),r=!/(\d)$/i.test(o);break;case"skew":r=!/(deg|\d)$/i.test(o);break;case"rotate":r=!/(deg|\d)$/i.test(o)}return r||(a(i).transformCache[e]="("+o+")"),a(i).transformCache[e]}}}();for(t=0;t<y.Lists.colors.length;t++)!function(){var e=y.Lists.colors[t];y.Normalizations.registered[e]=function(t,i,o){switch(t){case"name":return e;case"extract":var a;if(y.RegEx.wrappedValueAlreadyExtracted.test(o))a=o;else{var r,s={black:"rgb(0, 0, 0)",blue:"rgb(0, 0, 255)",gray:"rgb(128, 128, 128)",green:"rgb(0, 128, 0)",red:"rgb(255, 0, 0)",white:"rgb(255, 255, 255)"};/^[A-z]+$/i.test(o)?r=s[o]!==n?s[o]:s.black:y.RegEx.isHex.test(o)?r="rgb("+y.Values.hexToRgb(o).join(" ")+")":/^rgba?\(/i.test(o)||(r=s.black),a=(r||o).toString().match(y.RegEx.valueUnwrap)[1].replace(/,(\s+)?/g," ")}return 8>=d||3!==a.split(" ").length||(a+=" 1"),a;case"inject":return 8>=d?4===o.split(" ").length&&(o=o.split(/\s+/).slice(0,3).join(" ")):3===o.split(" ").length&&(o+=" 1"),(8>=d?"rgb":"rgba")+"("+o.replace(/\s+/g,",").replace(/\.(\d)+(?=,)/g,"")+")"}}}()}},Names:{camelCase:function(t){return t.replace(/-(\w)/g,function(t,e){return e.toUpperCase()})},SVGAttribute:function(t){var e="width|height|x|y|cx|cy|r|rx|ry|x1|x2|y1|y2";return(d||m.State.isAndroid&&!m.State.isChrome)&&(e+="|transform"),new RegExp("^("+e+")$","i").test(t)},prefixCheck:function(t){if(m.State.prefixMatches[t])return[m.State.prefixMatches[t],!0];for(var e=["","Webkit","Moz","ms","O"],i=0,n=e.length;n>i;i++){var o;if(o=0===i?t:e[i]+t.replace(/^\w/,function(t){return t.toUpperCase()}),p.isString(m.State.prefixElement.style[o]))return m.State.prefixMatches[t]=o,[o,!0]}return[t,!1]}},Values:{hexToRgb:function(t){var e;return t=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,function(t,e,i,n){return e+e+i+i+n+n}),(e=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t))?[parseInt(e[1],16),parseInt(e[2],16),parseInt(e[3],16)]:[0,0,0]},isCSSNullValue:function(t){return 0==t||/^(none|auto|transparent|(rgba\(0, ?0, ?0, ?0\)))$/i.test(t)},getUnitType:function(t){return/^(rotate|skew)/i.test(t)?"deg":/(^(scale|scaleX|scaleY|scaleZ|alpha|flexGrow|flexHeight|zIndex|fontWeight)$)|((opacity|red|green|blue|alpha)$)/i.test(t)?"":"px"},getDisplayType:function(t){var e=t&&t.tagName.toString().toLowerCase();return/^(b|big|i|small|tt|abbr|acronym|cite|code|dfn|em|kbd|strong|samp|var|a|bdo|br|img|map|object|q|script|span|sub|sup|button|input|label|select|textarea)$/i.test(e)?"inline":/^(li)$/i.test(e)?"list-item":/^(tr)$/i.test(e)?"table-row":/^(table)$/i.test(e)?"table":/^(tbody)$/i.test(e)?"table-row-group":"block"},addClass:function(t,e){t.classList?t.classList.add(e):t.className+=(t.className.length?" ":"")+e},removeClass:function(t,e){t.classList?t.classList.remove(e):t.className=t.className.toString().replace(new RegExp("(^|\\s)"+e.split(" ").join("|")+"(\\s|$)","gi")," ")}},getPropertyValue:function(t,i,o,r){function s(t,i){function o(){h&&y.setPropertyValue(t,"display","none")}var l=0;if(8>=d)l=u.css(t,i);else{var c,h=!1;if(/^(width|height)$/.test(i)&&0===y.getPropertyValue(t,"display")&&(h=!0,y.setPropertyValue(t,"display",y.Values.getDisplayType(t))),!r){if("height"===i&&"border-box"!==y.getPropertyValue(t,"boxSizing").toString().toLowerCase()){var p=t.offsetHeight-(parseFloat(y.getPropertyValue(t,"borderTopWidth"))||0)-(parseFloat(y.getPropertyValue(t,"borderBottomWidth"))||0)-(parseFloat(y.getPropertyValue(t,"paddingTop"))||0)-(parseFloat(y.getPropertyValue(t,"paddingBottom"))||0);return o(),p}if("width"===i&&"border-box"!==y.getPropertyValue(t,"boxSizing").toString().toLowerCase()){var f=t.offsetWidth-(parseFloat(y.getPropertyValue(t,"borderLeftWidth"))||0)-(parseFloat(y.getPropertyValue(t,"borderRightWidth"))||0)-(parseFloat(y.getPropertyValue(t,"paddingLeft"))||0)-(parseFloat(y.getPropertyValue(t,"paddingRight"))||0);return o(),f}}c=a(t)===n?e.getComputedStyle(t,null):a(t).computedStyle?a(t).computedStyle:a(t).computedStyle=e.getComputedStyle(t,null),"borderColor"===i&&(i="borderTopColor"),(""===(l=9===d&&"filter"===i?c.getPropertyValue(i):c[i])||null===l)&&(l=t.style[i]),o()}if("auto"===l&&/^(top|right|bottom|left)$/i.test(i)){var v=s(t,"position");("fixed"===v||"absolute"===v&&/top|left/i.test(i))&&(l=u(t).position()[i]+"px")}return l}var l;if(y.Hooks.registered[i]){var c=i,h=y.Hooks.getRoot(c);o===n&&(o=y.getPropertyValue(t,y.Names.prefixCheck(h)[0])),y.Normalizations.registered[h]&&(o=y.Normalizations.registered[h]("extract",t,o)),l=y.Hooks.extractValue(c,o)}else if(y.Normalizations.registered[i]){var p,f;"transform"!==(p=y.Normalizations.registered[i]("name",t))&&(f=s(t,y.Names.prefixCheck(p)[0]),y.Values.isCSSNullValue(f)&&y.Hooks.templates[i]&&(f=y.Hooks.templates[i][1])),l=y.Normalizations.registered[i]("extract",t,f)}if(!/^[\d-]/.test(l))if(a(t)&&a(t).isSVG&&y.Names.SVGAttribute(i))if(/^(height|width)$/i.test(i))try{l=t.getBBox()[i]}catch(t){l=0}else l=t.getAttribute(i);else l=s(t,y.Names.prefixCheck(i)[0]);return y.Values.isCSSNullValue(l)&&(l=0),m.debug>=2&&console.log("Get "+i+": "+l),l},setPropertyValue:function(t,i,n,o,r){var s=i;if("scroll"===i)r.container?r.container["scroll"+r.direction]=n:"Left"===r.direction?e.scrollTo(n,r.alternateValue):e.scrollTo(r.alternateValue,n);else if(y.Normalizations.registered[i]&&"transform"===y.Normalizations.registered[i]("name",t))y.Normalizations.registered[i]("inject",t,n),s="transform",n=a(t).transformCache[i];else{if(y.Hooks.registered[i]){var l=i,c=y.Hooks.getRoot(i);o=o||y.getPropertyValue(t,c),n=y.Hooks.injectValue(l,n,o),i=c}if(y.Normalizations.registered[i]&&(n=y.Normalizations.registered[i]("inject",t,n),i=y.Normalizations.registered[i]("name",t)),s=y.Names.prefixCheck(i)[0],8>=d)try{t.style[s]=n}catch(t){m.debug&&console.log("Browser does not support ["+n+"] for ["+s+"]")}else a(t)&&a(t).isSVG&&y.Names.SVGAttribute(i)?t.setAttribute(i,n):t.style[s]=n;m.debug>=2&&console.log("Set "+i+" ("+s+"): "+n)}return[s,n]},flushTransformCache:function(t){function e(e){return parseFloat(y.getPropertyValue(t,e))}var i="";if((d||m.State.isAndroid&&!m.State.isChrome)&&a(t).isSVG){var n={translate:[e("translateX"),e("translateY")],skewX:[e("skewX")],skewY:[e("skewY")],scale:1!==e("scale")?[e("scale"),e("scale")]:[e("scaleX"),e("scaleY")],rotate:[e("rotateZ"),0,0]};u.each(a(t).transformCache,function(t){/^translate/i.test(t)?t="translate":/^scale/i.test(t)?t="scale":/^rotate/i.test(t)&&(t="rotate"),n[t]&&(i+=t+"("+n[t].join(" ")+") ",delete n[t])})}else{var o,r;u.each(a(t).transformCache,function(e){return o=a(t).transformCache[e],"transformPerspective"===e?(r=o,!0):(9===d&&"rotateZ"===e&&(e="rotate"),void(i+=e+o+" "))}),r&&(i="perspective"+r+" "+i)}y.setPropertyValue(t,"transform",i)}};y.Hooks.register(),y.Normalizations.register(),m.hook=function(t,e,i){var r=n;return t=o(t),u.each(t,function(t,o){if(a(o)===n&&m.init(o),i===n)r===n&&(r=m.CSS.getPropertyValue(o,e));else{var s=m.CSS.setPropertyValue(o,e,i);"transform"===s[0]&&m.CSS.flushTransformCache(o),r=s}}),r};var w=function(){function t(){return d?O.promise||null:h}function r(){function t(t){function h(t,e){var i=n,o=n,a=n;return p.isArray(t)?(i=t[0],!p.isArray(t[1])&&/^[\d-]/.test(t[1])||p.isFunction(t[1])||y.RegEx.isHex.test(t[1])?a=t[1]:(p.isString(t[1])&&!y.RegEx.isHex.test(t[1])||p.isArray(t[1]))&&(o=e?t[1]:s(t[1],c.duration),t[2]!==n&&(a=t[2]))):i=t,e||(o=o||c.easing),p.isFunction(i)&&(i=i.call(r,C,T)),p.isFunction(a)&&(a=a.call(r,C,T)),[i||0,o,a]}function f(t,e){var i,n;return n=(e||"0").toString().toLowerCase().replace(/[%A-z]+$/,function(t){return i=t,""}),i||(i=y.Values.getUnitType(t)),[n,i]}function v(){var t={myParent:r.parentNode||i.body,position:y.getPropertyValue(r,"position"),fontSize:y.getPropertyValue(r,"fontSize")},n=t.position===W.lastPosition&&t.myParent===W.lastParent,o=t.fontSize===W.lastFontSize;W.lastParent=t.myParent,W.lastPosition=t.position,W.lastFontSize=t.fontSize;var s=100,l={};if(o&&n)l.emToPx=W.lastEmToPx,l.percentToPxWidth=W.lastPercentToPxWidth,l.percentToPxHeight=W.lastPercentToPxHeight;else{var c=a(r).isSVG?i.createElementNS("http://www.w3.org/2000/svg","rect"):i.createElement("div");m.init(c),t.myParent.appendChild(c),u.each(["overflow","overflowX","overflowY"],function(t,e){m.CSS.setPropertyValue(c,e,"hidden")}),m.CSS.setPropertyValue(c,"position",t.position),m.CSS.setPropertyValue(c,"fontSize",t.fontSize),m.CSS.setPropertyValue(c,"boxSizing","content-box"),u.each(["minWidth","maxWidth","width","minHeight","maxHeight","height"],function(t,e){m.CSS.setPropertyValue(c,e,s+"%")}),m.CSS.setPropertyValue(c,"paddingLeft",s+"em"),l.percentToPxWidth=W.lastPercentToPxWidth=(parseFloat(y.getPropertyValue(c,"width",null,!0))||1)/s,l.percentToPxHeight=W.lastPercentToPxHeight=(parseFloat(y.getPropertyValue(c,"height",null,!0))||1)/s,l.emToPx=W.lastEmToPx=(parseFloat(y.getPropertyValue(c,"paddingLeft"))||1)/s,t.myParent.removeChild(c)}return null===W.remToPx&&(W.remToPx=parseFloat(y.getPropertyValue(i.body,"fontSize"))||16),null===W.vwToPx&&(W.vwToPx=parseFloat(e.innerWidth)/100,W.vhToPx=parseFloat(e.innerHeight)/100),l.remToPx=W.remToPx,l.vwToPx=W.vwToPx,l.vhToPx=W.vhToPx,m.debug>=1&&console.log("Unit ratios: "+JSON.stringify(l),r),l}if(c.begin&&0===C)try{c.begin.call(g,g)}catch(t){setTimeout(function(){throw t},1)}if("scroll"===E){var w,x,k,$=/^x$/i.test(c.axis)?"Left":"Top",P=parseFloat(c.offset)||0;c.container?p.isWrapped(c.container)||p.isNode(c.container)?(c.container=c.container[0]||c.container,k=(w=c.container["scroll"+$])+u(r).position()[$.toLowerCase()]+P):c.container=null:(w=m.State.scrollAnchor[m.State["scrollProperty"+$]],x=m.State.scrollAnchor[m.State["scrollProperty"+("Left"===$?"Top":"Left")]],k=u(r).offset()[$.toLowerCase()]+P),d={scroll:{rootPropertyValue:!1,startValue:w,currentValue:w,endValue:k,unitType:"",easing:c.easing,scrollData:{container:c.container,direction:$,alternateValue:x}},element:r},m.debug&&console.log("tweensContainer (scroll): ",d.scroll,r)}else if("reverse"===E){if(!a(r).tweensContainer)return void u.dequeue(r,c.queue);"none"===a(r).opts.display&&(a(r).opts.display="auto"),"hidden"===a(r).opts.visibility&&(a(r).opts.visibility="visible"),a(r).opts.loop=!1,a(r).opts.begin=null,a(r).opts.complete=null,S.easing||delete c.easing,S.duration||delete c.duration,c=u.extend({},a(r).opts,c);var M=u.extend(!0,{},a(r).tweensContainer);for(var L in M)if("element"!==L){var A=M[L].startValue;M[L].startValue=M[L].currentValue=M[L].endValue,M[L].endValue=A,p.isEmptyObject(S)||(M[L].easing=c.easing),m.debug&&console.log("reverse tweensContainer ("+L+"): "+JSON.stringify(M[L]),r)}d=M}else if("start"===E){for(var I in a(r).tweensContainer&&!0===a(r).isAnimating&&(M=a(r).tweensContainer),u.each(b,function(t,e){if(RegExp("^"+y.Lists.colors.join("$|^")+"$").test(t)){var i=h(e,!0),o=i[0],a=i[1],r=i[2];if(y.RegEx.isHex.test(o)){for(var s=["Red","Green","Blue"],l=y.Values.hexToRgb(o),c=r?y.Values.hexToRgb(r):n,u=0;u<s.length;u++){var d=[l[u]];a&&d.push(a),c!==n&&d.push(c[u]),b[t+s[u]]=d}delete b[t]}}}),b){var _=h(b[I]),X=_[0],R=_[1],Y=_[2];I=y.Names.camelCase(I);var D=y.Hooks.getRoot(I),V=!1;if(a(r).isSVG||"tween"===D||!1!==y.Names.prefixCheck(D)[1]||y.Normalizations.registered[D]!==n){(c.display!==n&&null!==c.display&&"none"!==c.display||c.visibility!==n&&"hidden"!==c.visibility)&&/opacity|filter/.test(I)&&!Y&&0!==X&&(Y=0),c._cacheValues&&M&&M[I]?(Y===n&&(Y=M[I].endValue+M[I].unitType),V=a(r).rootPropertyValueCache[D]):y.Hooks.registered[I]?Y===n?(V=y.getPropertyValue(r,D),Y=y.getPropertyValue(r,I,V)):V=y.Hooks.templates[D][1]:Y===n&&(Y=y.getPropertyValue(r,I));var N,j,q,z=!1;if(Y=(N=f(I,Y))[0],q=N[1],X=(N=f(I,X))[0].replace(/^([+-\/*])=/,function(t,e){return z=e,""}),j=N[1],Y=parseFloat(Y)||0,X=parseFloat(X)||0,"%"===j&&(/^(fontSize|lineHeight)$/.test(I)?(X/=100,j="em"):/^scale/.test(I)?(X/=100,j=""):/(Red|Green|Blue)$/i.test(I)&&(X=X/100*255,j="")),/[\/*]/.test(z))j=q;else if(q!==j&&0!==Y)if(0===X)j=q;else{o=o||v();var F=/margin|padding|left|right|width|text|word|letter/i.test(I)||/X$/.test(I)||"x"===I?"x":"y";switch(q){case"%":Y*="x"===F?o.percentToPxWidth:o.percentToPxHeight;break;case"px":break;default:Y*=o[q+"ToPx"]}switch(j){case"%":Y*=1/("x"===F?o.percentToPxWidth:o.percentToPxHeight);break;case"px":break;default:Y*=1/o[j+"ToPx"]}}switch(z){case"+":X=Y+X;break;case"-":X=Y-X;break;case"*":X*=Y;break;case"/":X=Y/X}d[I]={rootPropertyValue:V,startValue:Y,currentValue:Y,endValue:X,unitType:j,easing:R},m.debug&&console.log("tweensContainer ("+I+"): "+JSON.stringify(d[I]),r)}else m.debug&&console.log("Skipping ["+D+"] due to a lack of browser support.")}d.element=r}d.element&&(y.Values.addClass(r,"velocity-animating"),H.push(d),""===c.queue&&(a(r).tweensContainer=d,a(r).opts=c),a(r).isAnimating=!0,C===T-1?(m.State.calls.push([H,g,c,null,O.resolver]),!1===m.State.isTicking&&(m.State.isTicking=!0,l())):C++)}var o,r=this,c=u.extend({},m.defaults,S),d={};switch(a(r)===n&&m.init(r),parseFloat(c.delay)&&!1!==c.queue&&u.queue(r,c.queue,function(t){m.velocityQueueEntryFlag=!0,a(r).delayTimer={setTimeout:setTimeout(t,parseFloat(c.delay)),next:t}}),c.duration.toString().toLowerCase()){case"fast":c.duration=200;break;case"normal":c.duration=v;break;case"slow":c.duration=600;break;default:c.duration=parseFloat(c.duration)||1}!1!==m.mock&&(!0===m.mock?c.duration=c.delay=1:(c.duration*=parseFloat(m.mock)||1,c.delay*=parseFloat(m.mock)||1)),c.easing=s(c.easing,c.duration),c.begin&&!p.isFunction(c.begin)&&(c.begin=null),c.progress&&!p.isFunction(c.progress)&&(c.progress=null),c.complete&&!p.isFunction(c.complete)&&(c.complete=null),c.display!==n&&null!==c.display&&(c.display=c.display.toString().toLowerCase(),"auto"===c.display&&(c.display=m.CSS.Values.getDisplayType(r))),c.visibility!==n&&null!==c.visibility&&(c.visibility=c.visibility.toString().toLowerCase()),c.mobileHA=c.mobileHA&&m.State.isMobile&&!m.State.isGingerbread,!1===c.queue?c.delay?setTimeout(t,c.delay):t():u.queue(r,c.queue,function(e,i){return!0===i?(O.promise&&O.resolver(g),!0):(m.velocityQueueEntryFlag=!0,void t())}),""!==c.queue&&"fx"!==c.queue||"inprogress"===u.queue(r)[0]||u.dequeue(r)}var d,h,f,g,b,S,x=arguments[0]&&(arguments[0].p||u.isPlainObject(arguments[0].properties)&&!arguments[0].properties.names||p.isString(arguments[0].properties));if(p.isWrapped(this)?(d=!1,f=0,g=this,h=this):(d=!0,f=1,g=x?arguments[0].elements||arguments[0].e:arguments[0]),g=o(g)){x?(b=arguments[0].properties||arguments[0].p,S=arguments[0].options||arguments[0].o):(b=arguments[f],S=arguments[f+1]);var T=g.length,C=0;if(!/^(stop|finish)$/i.test(b)&&!u.isPlainObject(S)){S={};for(var k=f+1;k<arguments.length;k++)p.isArray(arguments[k])||!/^(fast|normal|slow)$/i.test(arguments[k])&&!/^\d/.test(arguments[k])?p.isString(arguments[k])||p.isArray(arguments[k])?S.easing=arguments[k]:p.isFunction(arguments[k])&&(S.complete=arguments[k]):S.duration=arguments[k]}var E,O={promise:null,resolver:null,rejecter:null};switch(d&&m.Promise&&(O.promise=new m.Promise(function(t,e){O.resolver=t,O.rejecter=e})),b){case"scroll":E="scroll";break;case"reverse":E="reverse";break;case"finish":case"stop":u.each(g,function(t,e){a(e)&&a(e).delayTimer&&(clearTimeout(a(e).delayTimer.setTimeout),a(e).delayTimer.next&&a(e).delayTimer.next(),delete a(e).delayTimer)});var $=[];return u.each(m.State.calls,function(t,e){e&&u.each(e[1],function(i,o){var r=S===n?"":S;return!0!==r&&e[2].queue!==r&&(S!==n||!1!==e[2].queue)||void u.each(g,function(i,n){n===o&&((!0===S||p.isString(S))&&(u.each(u.queue(n,p.isString(S)?S:""),function(t,e){p.isFunction(e)&&e(null,!0)}),u.queue(n,p.isString(S)?S:"",[])),"stop"===b?(a(n)&&a(n).tweensContainer&&!1!==r&&u.each(a(n).tweensContainer,function(t,e){e.endValue=e.currentValue}),$.push(t)):"finish"===b&&(e[2].duration=1))})})}),"stop"===b&&(u.each($,function(t,e){c(e,!0)}),O.promise&&O.resolver(g)),t();default:if(!u.isPlainObject(b)||p.isEmptyObject(b)){if(p.isString(b)&&m.Redirects[b]){var P=(I=u.extend({},S)).duration,M=I.delay||0;return!0===I.backwards&&(g=u.extend(!0,[],g).reverse()),u.each(g,function(t,e){parseFloat(I.stagger)?I.delay=M+parseFloat(I.stagger)*t:p.isFunction(I.stagger)&&(I.delay=M+I.stagger.call(e,t,T)),I.drag&&(I.duration=parseFloat(P)||(/^(callout|transition)/.test(b)?1e3:v),I.duration=Math.max(I.duration*(I.backwards?1-t/T:(t+1)/T),.75*I.duration,200)),m.Redirects[b].call(e,e,I||{},t,T,g,O.promise?O:n)}),t()}var L="Velocity: First argument ("+b+") was not a property map, a known action, or a registered redirect. Aborting.";return O.promise?O.rejecter(new Error(L)):console.log(L),t()}E="start"}var A,I,W={lastParent:null,lastPosition:null,lastFontSize:null,lastPercentToPxWidth:null,lastPercentToPxHeight:null,lastEmToPx:null,remToPx:null,vwToPx:null,vhToPx:null},H=[];if(u.each(g,function(t,e){p.isNode(e)&&r.call(e)}),(I=u.extend({},m.defaults,S)).loop=parseInt(I.loop),A=2*I.loop-1,I.loop)for(var _=0;A>_;_++){var X={delay:I.delay,progress:I.progress};_===A-1&&(X.display=I.display,X.visibility=I.visibility,X.complete=I.complete),w(g,"reverse",X)}return t()}};(m=u.extend(w,m)).animate=w;var S=e.requestAnimationFrame||h;return m.State.isMobile||i.hidden===n||i.addEventListener("visibilitychange",function(){i.hidden?(S=function(t){return setTimeout(function(){t(!0)},16)},l()):S=e.requestAnimationFrame||h}),t.Velocity=m,t!==e&&(t.fn.velocity=w,t.fn.velocity.defaults=m.defaults),u.each(["Down","Up"],function(t,e){m.Redirects["slide"+e]=function(t,i,o,a,r,s){var l=u.extend({},i),c=l.begin,d=l.complete,h={height:"",marginTop:"",marginBottom:"",paddingTop:"",paddingBottom:""},p={};l.display===n&&(l.display="Down"===e?"inline"===m.CSS.Values.getDisplayType(t)?"inline-block":"block":"none"),l.begin=function(){for(var i in c&&c.call(r,r),h){p[i]=t.style[i];var n=m.CSS.getPropertyValue(t,i);h[i]="Down"===e?[n,0]:[0,n]}p.overflow=t.style.overflow,t.style.overflow="hidden"},l.complete=function(){for(var e in p)t.style[e]=p[e];d&&d.call(r,r),s&&s.resolver(r)},m(t,h,l)}}),u.each(["In","Out"],function(t,e){m.Redirects["fade"+e]=function(t,i,o,a,r,s){var l=u.extend({},i),c={opacity:"In"===e?1:0},d=l.complete;l.complete=o!==a-1?l.begin=null:function(){d&&d.call(r,r),s&&s.resolver(r)},l.display===n&&(l.display="In"===e?"auto":"none"),m(this,c,l)}}),m}jQuery.fn.velocity=jQuery.fn.animate}(window.jQuery||window.Zepto||window,window,document)})),function(t){WOW=function(){return{init:function(){var e=[],i=1;function n(){var i=window.innerHeight,n=window.scrollY;t(".wow").each(function(){if("visible"!=t(this).css("visibility")&&(i+n-100>o(this)&&n<o(this)||i+n-100>o(this)+t(this).height()&&n<o(this)+t(this).height()||i+n==t(document).height()&&o(this)+100>t(document).height())){var a=t(this).index(".wow"),r=t(this).attr("data-wow-delay");if(r){r=t(this).attr("data-wow-delay").slice(0,-1);var s=this;parseFloat(r);t(s).addClass("animated"),t(s).css({visibility:"visible"}),t(s).css({"animation-delay":r}),t(s).css({"animation-name":e[a]});var l=1e3*t(this).css("animation-duration").slice(0,-1);t(this).attr("data-wow-delay")&&(l+=1e3*t(this).attr("data-wow-delay").slice(0,-1));s=this;setTimeout(function(){t(s).removeClass("animated")},l)}else{t(this).addClass("animated"),t(this).css({visibility:"visible"}),t(this).css({"animation-name":e[a]});l=1e3*t(this).css("animation-duration").slice(0,-1),s=this;setTimeout(function(){t(s).removeClass("animated")},l)}}})}function o(t){var e=t.getBoundingClientRect(),i=document.body,n=document.documentElement,o=window.pageYOffset||n.scrollTop||i.scrollTop,a=n.clientTop||i.clientTop||0,r=e.top+o-a;return Math.round(r)}t(".wow").each(function(){t(this).css({visibility:"hidden"}),e[t(this).index(".wow")]=t(this).css("animation-name"),t(this).css({"animation-name":"none"})}),t(window).scroll(function(){var e,a;i?(e=window.innerHeight,a=window.scrollY,t(".wow.animated").each(function(){if(e+a-100>o(this)&&a>o(this)+100||e+a-100<o(this)&&a<o(this)+100||o(this)+t(this).height>t(document).height()-100)t(this).removeClass("animated"),t(this).css({"animation-name":"none"}),t(this).css({visibility:"hidden"});else{var i=1e3*t(this).css("animation-duration").slice(0,-1);t(this).attr("data-wow-delay")&&(i+=1e3*t(this).attr("data-wow-delay").slice(0,-1));var n=this;setTimeout(function(){t(n).removeClass("animated")},i)}}),n(),i--):n()}),t(".wow").each(function(){var i=t(this).index(".wow"),n=t(this).attr("data-wow-delay");n?(n=t(this).attr("data-wow-delay").slice(0,-1),parseFloat(n),t(this).addClass("animated"),t(this).css({visibility:"visible"}),t(this).css({"animation-delay":n+"s"}),t(this).css({"animation-name":e[i]})):(t(this).addClass("animated"),t(this).css({visibility:"visible"}),t(this).css({"animation-name":e[i]}))})}}}}(jQuery),function(t){t(window).on("scroll",function(){var e=t(".navbar");e.length&&(e.offset().top>50?t(".scrolling-navbar").addClass("top-nav-collapse"):t(".scrolling-navbar").removeClass("top-nav-collapse"))})}(jQuery),function(t,e){"use strict";"function"==typeof define&&define.amd?define([],function(){return t.Waves=e.call(t),t.Waves}):"object"==typeof exports?module.exports=e.call(t):t.Waves=e.call(t)}("object"==typeof global?global:this,function(){"use strict";var t=t||{},e=document.querySelectorAll.bind(document),i=Object.prototype.toString,n="ontouchstart"in window;function o(t){var e=typeof t;return"function"===e||"object"===e&&!!t}function a(t){var n,a=i.call(t);return"[object String]"===a?e(t):o(t)&&/^\[object (Array|HTMLCollection|NodeList|Object)\]$/.test(a)&&t.hasOwnProperty("length")?t:o(n=t)&&n.nodeType>0?[t]:[]}function r(t){var e,i,n={top:0,left:0},o=t&&t.ownerDocument;return e=o.documentElement,void 0!==t.getBoundingClientRect&&(n=t.getBoundingClientRect()),i=function(t){return null!==(e=t)&&e===e.window?t:9===t.nodeType&&t.defaultView;var e}(o),{top:n.top+i.pageYOffset-e.clientTop,left:n.left+i.pageXOffset-e.clientLeft}}function s(t){var e="";for(var i in t)t.hasOwnProperty(i)&&(e+=i+":"+t[i]+";");return e}var l={duration:750,delay:200,show:function(t,e,i){if(2===t.button)return!1;e=e||this;var n=document.createElement("div");n.className="waves-ripple waves-rippling",e.appendChild(n);var o=r(e),a=0,c=0;"touches"in t&&t.touches.length?(a=t.touches[0].pageY-o.top,c=t.touches[0].pageX-o.left):(a=t.pageY-o.top,c=t.pageX-o.left),c=c>=0?c:0,a=a>=0?a:0;var u="scale("+e.clientWidth/100*3+")",d="translate(0,0)";i&&(d="translate("+i.x+"px, "+i.y+"px)"),n.setAttribute("data-hold",Date.now()),n.setAttribute("data-x",c),n.setAttribute("data-y",a),n.setAttribute("data-scale",u),n.setAttribute("data-translate",d);var h={top:a+"px",left:c+"px"};n.classList.add("waves-notransition"),n.setAttribute("style",s(h)),n.classList.remove("waves-notransition"),h["-webkit-transform"]=u+" "+d,h["-moz-transform"]=u+" "+d,h["-ms-transform"]=u+" "+d,h["-o-transform"]=u+" "+d,h.transform=u+" "+d,h.opacity="1";var p="mousemove"===t.type?2500:l.duration;h["-webkit-transition-duration"]=p+"ms",h["-moz-transition-duration"]=p+"ms",h["-o-transition-duration"]=p+"ms",h["transition-duration"]=p+"ms",n.setAttribute("style",s(h))},hide:function(t,e){for(var i=(e=e||this).getElementsByClassName("waves-rippling"),o=0,a=i.length;o<a;o++)u(t,e,i[o]);n&&(e.removeEventListener("touchend",l.hide),e.removeEventListener("touchcancel",l.hide)),e.removeEventListener("mouseup",l.hide),e.removeEventListener("mouseleave",l.hide)}},c={input:function(t){var e=t.parentNode;if("span"!==e.tagName.toLowerCase()||!e.classList.contains("waves-effect")){var i=document.createElement("span");i.className="waves-input-wrapper",e.replaceChild(i,t),i.appendChild(t)}},img:function(t){var e=t.parentNode;if("i"!==e.tagName.toLowerCase()||!e.classList.contains("waves-effect")){var i=document.createElement("i");e.replaceChild(i,t),i.appendChild(t)}}};function u(t,e,i){if(i){i.classList.remove("waves-rippling");var n=i.getAttribute("data-x"),o=i.getAttribute("data-y"),a=i.getAttribute("data-scale"),r=i.getAttribute("data-translate"),c=350-(Date.now()-Number(i.getAttribute("data-hold")));c<0&&(c=0),"mousemove"===t.type&&(c=150);var u="mousemove"===t.type?2500:l.duration;setTimeout(function(){var t={top:o+"px",left:n+"px",opacity:"0","-webkit-transition-duration":u+"ms","-moz-transition-duration":u+"ms","-o-transition-duration":u+"ms","transition-duration":u+"ms","-webkit-transform":a+" "+r,"-moz-transform":a+" "+r,"-ms-transform":a+" "+r,"-o-transform":a+" "+r,transform:a+" "+r};i.setAttribute("style",s(t)),setTimeout(function(){try{e.removeChild(i)}catch(t){return!1}},u)},c)}}var d={touches:0,allowEvent:function(t){var e=!0;return/^(mousedown|mousemove)$/.test(t.type)&&d.touches&&(e=!1),e},registerEvent:function(t){var e=t.type;"touchstart"===e?d.touches+=1:/^(touchend|touchcancel)$/.test(e)&&setTimeout(function(){d.touches&&(d.touches-=1)},500)}};function h(t){var e=function(t){if(!1===d.allowEvent(t))return null;for(var e=null,i=t.target||t.srcElement;i.parentElement;){if(!(i instanceof SVGElement)&&i.classList.contains("waves-effect")){e=i;break}i=i.parentElement}return e}(t);if(null!==e){if(e.disabled||e.getAttribute("disabled")||e.classList.contains("disabled"))return;if(d.registerEvent(t),"touchstart"===t.type&&l.delay){var i=!1,o=setTimeout(function(){o=null,l.show(t,e)},l.delay),a=function(n){o&&(clearTimeout(o),o=null,l.show(t,e)),i||(i=!0,l.hide(n,e)),s()},r=function(t){o&&(clearTimeout(o),o=null),a(t),s()};e.addEventListener("touchmove",r,!1),e.addEventListener("touchend",a,!1),e.addEventListener("touchcancel",a,!1);var s=function(){e.removeEventListener("touchmove",r),e.removeEventListener("touchend",a),e.removeEventListener("touchcancel",a)}}else l.show(t,e),n&&(e.addEventListener("touchend",l.hide,!1),e.addEventListener("touchcancel",l.hide,!1)),e.addEventListener("mouseup",l.hide,!1),e.addEventListener("mouseleave",l.hide,!1)}}return t.init=function(t){var e=document.body;"duration"in(t=t||{})&&(l.duration=t.duration),"delay"in t&&(l.delay=t.delay),n&&(e.addEventListener("touchstart",h,!1),e.addEventListener("touchcancel",d.registerEvent,!1),e.addEventListener("touchend",d.registerEvent,!1)),e.addEventListener("mousedown",h,!1)},t.attach=function(t,e){var n,o;t=a(t),"[object Array]"===i.call(e)&&(e=e.join(" ")),e=e?" "+e:"";for(var r=0,s=t.length;r<s;r++)o=(n=t[r]).tagName.toLowerCase(),-1!==["input","img"].indexOf(o)&&(c[o](n),n=n.parentElement),-1===n.className.indexOf("waves-effect")&&(n.className+=" waves-effect"+e)},t.ripple=function(t,e){var i=(t=a(t)).length;if((e=e||{}).wait=e.wait||0,e.position=e.position||null,i)for(var n,o,s,c={},u=0,d={type:"mousedown",button:1},h=function(t,e){return function(){l.hide(t,e)}};u<i;u++)if(n=t[u],o=e.position||{x:n.clientWidth/2,y:n.clientHeight/2},s=r(n),c.x=s.left+o.x,c.y=s.top+o.y,d.pageX=c.x,d.pageY=c.y,l.show(d,n),e.wait>=0&&null!==e.wait){setTimeout(h({type:"mouseup",button:1},n),e.wait)}},t.calm=function(t){for(var e={type:"mouseup",button:1},i=0,n=(t=a(t)).length;i<n;i++)l.hide(e,t[i])},t.displayEffect=function(e){console.error("Waves.displayEffect() has been deprecated and will be removed in future version. Please use Waves.init() to initialize Waves effect"),t.init(e)},t}),Waves.attach(".btn:not(.btn-flat), .btn-floating",["waves-light"]),Waves.attach(".btn-flat",["waves-effect"]),Waves.attach(".chip",["waves-effect"]),Waves.attach(".view a .mask",["waves-light"]),Waves.attach(".waves-light",["waves-light"]),Waves.attach(".navbar-nav a:not(.navbar-brand), .nav-icons li a, .nav-tabs .nav-item:not(.dropdown)",["waves-light"]),Waves.attach(".pager li a",["waves-light"]),Waves.attach(".pagination .page-item .page-link",["waves-effect"]),Waves.init();var _this=void 0;!function(t){var e,i,n="".concat(["text","password","email","url","tel","number","search","search-md"].map(function(t){return"input[type=".concat(t,"]")}).join(", "),", textarea"),o=function(t){var e=t.siblings("label, i"),i=t.val().length,n=t.attr("placeholder");e["".concat(i||n?"add":"remove","Class")]("active")},a=function(t){if(t.hasClass("validate")){var e=t.val(),i=!e.length,n=!t[0].validity.badInput;if(i&&n)t.removeClass("valid").removeClass("invalid");else{var o=t.is(":valid"),a=Number(t.attr("length"))||0;o&&(!a||a>e.length)?t.removeClass("invalid").addClass("valid"):t.removeClass("valid").addClass("invalid")}}},r=function(){var e=t(_this);if(e.val().length){var i=t(".hiddendiv"),n=e.css("font-family"),o=e.css("font-size");o&&i.css("font-size",o),n&&i.css("font-family",n),"off"===e.attr("wrap")&&i.css("overflow-wrap","normal").css("white-space","pre"),i.text("".concat(e.val(),"\n"));var a=i.html().replace(/\n/g,"<br>");i.html(a),i.css("width",e.is(":visible")?e.width():t(window).width()/2),e.css("height",i.height())}};t(n).each(function(e,i){var n=t(i),a=n.siblings("label, i");o(n),i.validity.badInput&&a.addClass("active")}),t(document).on("focus",n,function(e){t(e.target).siblings("label, i").addClass("active")}),t(document).on("blur",n,function(e){var i=t(e.target),n=!i.val(),o=!e.target.validity.badInput,r=void 0===i.attr("placeholder");n&&o&&r&&i.siblings("label, i").removeClass("active"),a(i)}),t(document).on("change",n,function(e){var i=t(e.target);o(i),a(i)}),t("input[autofocus]").siblings("label, i").addClass("active"),t(document).on("reset",function(e){var i=t(e.target);i.is("form")&&(i.find(n).removeClass("valid").removeClass("invalid").each(function(e,i){var n=t(i),o=!n.val(),a=!n.attr("placeholder");o&&a&&n.siblings("label, i").removeClass("active")}),i.find("select.initialized").each(function(e,i){var n=t(i),o=n.siblings("input.select-dropdown"),a=n.children("[selected]").val();n.val(a),o.val(a)}))}),(i=t(".md-textarea-auto")).length&&(e=window.attachEvent?function(t,e,i){t.attachEvent("on".concat(e),i)}:function(t,e,i){t.addEventListener(e,i,!1)},i.each(function(){var t=this;function i(){t.style.height="auto",t.style.height="".concat(t.scrollHeight,"px")}function n(){window.setTimeout(i,0)}e(t,"change",i),e(t,"cut",n),e(t,"paste",n),e(t,"drop",n),e(t,"keydown",n),i()}));var s=t("body");if(!t(".hiddendiv").first().length){var l=t('<div class="hiddendiv common"></div>');s.append(l)}t(".materialize-textarea").each(r),s.on("keyup keydown",".materialize-textarea",r)}(jQuery),$(document).ready(function(){$("body").attr("aria-busy",!0),$("#preloader-markup").load("mdb-addons/preloader.html",function(){$(window).on("load",function(){$("#mdb-preloader").fadeOut("slow"),$("body").removeAttr("aria-busy")})})}),function(t){t(document).on("click.card",".card",function(e){if(t(this).find(".card-reveal").length){var i=t(e.target),n=i.is(".card-reveal .card-title"),o=i.is(".card-reveal .card-title i"),a=i.is(".card .activator"),r=i.is(".card .activator i");n||o?t(this).find(".card-reveal").velocity({translateY:0},{duration:225,queue:!1,easing:"easeInOutQuad",complete:function(){t(this).css({display:"none"})}}):(a||r)&&t(this).find(".card-reveal").css({display:"block"}).velocity("stop",!1).velocity({translateY:"-100%"},{duration:300,queue:!1,easing:"easeInOutQuad"})}}),t(".rotate-btn").on("click",function(){var e=t(this).attr("data-card");t("#".concat(e)).toggleClass("flipped")});var e=t(".front").outerHeight(),i=t(".back").outerHeight();e>i?t(".card-wrapper, .back").height(e):e>i?t(".card-wrapper, .front").height(i):t(".card-wrapper").height(i),t(".card-share > a").on("click",function(e){e.preventDefault(),t(this).toggleClass("share-expanded").parent().find("div").toggleClass("social-reveal-active")})}(jQuery),function(t){function e(){var e=Number(t(this).attr("length")),i=Number(t(this).val().length),n=i<=e;t(this).parent().find('span[class="character-counter"]').html("".concat(i,"/").concat(e)),function(t,e){var i=e.hasClass("invalid");t&&i?e.removeClass("invalid"):t||i||(e.removeClass("valid"),e.addClass("invalid"))}(n,t(this))}function i(){t(this).parent().find('span[class="character-counter"]').html("")}t.fn.characterCounter=function(){return this.each(function(){var n,o;void 0!==t(this).attr("length")&&(t(this).on("input",e),t(this).on("focus",e),t(this).on("blur",i),n=t(this),o=t("<span/>").addClass("character-counter").css("float","right").css("font-size","12px").css("height",1),n.parent().append(o))})},t(document).ready(function(){t("input, textarea").characterCounter()})}(jQuery),function(t){t(["jquery"],function(t){return function(){var e,i,n,o=0,a={error:"error",info:"info",success:"success",warning:"warning"},r={clear:function(i,n){var o=d();e||s(o);l(i,o,n)||function(i){for(var n=e.children(),o=n.length-1;o>=0;o--)l(t(n[o]),i)}(o)},remove:function(i){var n=d();e||s(n);if(i&&0===t(":focus",i).length)return void h(i);e.children().length&&e.remove()},error:function(t,e,i){return u({type:a.error,iconClass:d().iconClasses.error,message:t,optionsOverride:i,title:e})},getContainer:s,info:function(t,e,i){return u({type:a.info,iconClass:d().iconClasses.info,message:t,optionsOverride:i,title:e})},options:{},subscribe:function(t){i=t},success:function(t,e,i){return u({type:a.success,iconClass:d().iconClasses.success,message:t,optionsOverride:i,title:e})},version:"2.1.1",warning:function(t,e,i){return u({type:a.warning,iconClass:d().iconClasses.warning,message:t,optionsOverride:i,title:e})}};return r;function s(i,n){return i||(i=d()),(e=t("#"+i.containerId)).length?e:(n&&(e=function(i){return(e=t("<div/>").attr("id",i.containerId).addClass(i.positionClass).attr("aria-live","polite").attr("role","alert")).appendTo(t(i.target)),e}(i)),e)}function l(e,i,n){var o=!(!n||!n.force)&&n.force;return!(!e||!o&&0!==t(":focus",e).length)&&(e[i.hideMethod]({duration:i.hideDuration,easing:i.hideEasing,complete:function(){h(e)}}),!0)}function c(t){i&&i(t)}function u(i){var a=d(),r=i.iconClass||a.iconClass;if(void 0!==i.optionsOverride&&(a=t.extend(a,i.optionsOverride),r=i.optionsOverride.iconClass||r),!function(t,e){if(t.preventDuplicates){if(e.message===n)return!0;n=e.message}return!1}(a,i)){o++,e=s(a,!0);var l=null,u=t("<div/>"),p=t("<div/>"),f=t("<div/>"),v=t("<div/>"),g=t(a.closeHtml),m={intervalId:null,hideEta:null,maxHideTime:null},b={toastId:o,state:"visible",startTime:new Date,options:a,map:i};return i.iconClass&&u.addClass(a.toastClass).addClass(r),i.title&&(p.append(i.title).addClass(a.titleClass),u.append(p)),i.message&&(f.append(i.message).addClass(a.messageClass),u.append(f)),a.closeButton&&(g.addClass("toast-close-button").attr("role","button"),u.prepend(g)),a.progressBar&&(v.addClass("toast-progress"),u.prepend(v)),a.newestOnTop?e.prepend(u):e.append(u),u.hide(),u[a.showMethod]({duration:a.showDuration,easing:a.showEasing,complete:a.onShown}),a.timeOut>0&&(l=setTimeout(y,a.timeOut),m.maxHideTime=parseFloat(a.timeOut),m.hideEta=(new Date).getTime()+m.maxHideTime,a.progressBar&&(m.intervalId=setInterval(x,10))),function(){u.hover(S,w),!a.onclick&&a.tapToDismiss&&u.click(y);a.closeButton&&g&&g.click(function(t){t.stopPropagation?t.stopPropagation():void 0!==t.cancelBubble&&!0!==t.cancelBubble&&(t.cancelBubble=!0),y(!0)});a.onclick&&u.click(function(){a.onclick(),y()})}(),c(b),a.debug&&console&&console.log(b),u}function y(e){if(!t(":focus",u).length||e)return clearTimeout(m.intervalId),u[a.hideMethod]({duration:a.hideDuration,easing:a.hideEasing,complete:function(){h(u),a.onHidden&&"hidden"!==b.state&&a.onHidden(),b.state="hidden",b.endTime=new Date,c(b)}})}function w(){(a.timeOut>0||a.extendedTimeOut>0)&&(l=setTimeout(y,a.extendedTimeOut),m.maxHideTime=parseFloat(a.extendedTimeOut),m.hideEta=(new Date).getTime()+m.maxHideTime)}function S(){clearTimeout(l),m.hideEta=0,u.stop(!0,!0)[a.showMethod]({duration:a.showDuration,easing:a.showEasing})}function x(){var t=(m.hideEta-(new Date).getTime())/m.maxHideTime*100;v.width(t+"%")}}function d(){return t.extend({},{tapToDismiss:!0,toastClass:"toast",containerId:"toast-container",debug:!1,showMethod:"fadeIn",showDuration:300,showEasing:"swing",onShown:void 0,hideMethod:"fadeOut",hideDuration:1e3,hideEasing:"swing",onHidden:void 0,extendedTimeOut:1e3,iconClasses:{error:"toast-error",info:"toast-info",success:"toast-success",warning:"toast-warning"},iconClass:"toast-info",positionClass:"toast-top-right",timeOut:5e3,titleClass:"toast-title",messageClass:"toast-message",target:"body",closeHtml:'<button type="button">&times;</button>',newestOnTop:!0,preventDuplicates:!1,progressBar:!1},r.options)}function h(t){e||(e=s()),t.is(":visible")||(t.remove(),t=null,0===e.children().length&&(e.remove(),n=void 0))}}()})}("function"==typeof define&&define.amd?define:function(t,e){"undefined"!=typeof module&&module.exports?module.exports=e(require("jquery")):window.toastr=e(window.jQuery)});var SMOOTH_SCROLL_DURATION=700;$(".smooth-scroll").on("click","a",function(){var t=$(this).attr("href");if(void 0!==t&&0===t.indexOf("#")){var e=$(this).attr("data-offset")?$(this).attr("data-offset"):0,i=$(this).parentsUntil(".smooth-scroll").last().parent().attr("data-allow-hashes");return $("body,html").animate({scrollTop:$(t).offset().top-e},SMOOTH_SCROLL_DURATION),void 0!==i&&!1!==i&&history.replaceState(null,null,t),!1}}),function(t){t.fn.scrollTo=function(e){return t(this).scrollTop(t(this).scrollTop()-t(this).offset().top+t(e).offset().top),this},t.fn.dropdown=function(e){this.each(function(){var i=t(this),n=t.extend({},t.fn.dropdown.defaults,e),o=!1,a=t("#".concat(i.attr("data-activates")));function r(){void 0!==i.data("induration")&&(n.inDuration=i.data("inDuration")),void 0!==i.data("outduration")&&(n.outDuration=i.data("outDuration")),void 0!==i.data("constrainwidth")&&(n.constrain_width=i.data("constrainwidth")),void 0!==i.data("hover")&&(n.hover=i.data("hover")),void 0!==i.data("gutter")&&(n.gutter=i.data("gutter")),void 0!==i.data("beloworigin")&&(n.belowOrigin=i.data("beloworigin")),void 0!==i.data("alignment")&&(n.alignment=i.data("alignment"))}function s(e){"focus"===e&&(o=!0),r(),a.addClass("active"),i.addClass("active"),!0===n.constrain_width?a.css("width",i.outerWidth()):a.css("white-space","nowrap");var s=window.innerHeight,l=i.innerHeight(),c=i.offset().left,u=i.offset().top-t(window).scrollTop(),d=n.alignment,h=0,p=0,f=0;!0===n.belowOrigin&&(f=l);var v=0,g=i.parent();if(!g.is("body")&&g[0].scrollHeight>g[0].clientHeight&&(v=g[0].scrollTop),c+a.innerWidth()>t(window).width()?d="right":c-a.innerWidth()+i.innerWidth()<0&&(d="left"),u+a.innerHeight()>s)if(u+l-a.innerHeight()<0){var m=s-u-f;a.css("max-height",m)}else f||(f+=l),f-=a.innerHeight();if("left"===d)h=n.gutter,p=i.position().left+h;else if("right"===d){p=i.position().left+i.outerWidth()-a.outerWidth()+(h=-n.gutter)}a.css({position:"absolute",top:i.position().top+f+v,left:p}),a.stop(!0,!0).css("opacity",0).slideDown({queue:!1,duration:n.inDuration,easing:"easeOutCubic",complete:function(){t(this).css("height","")}}).animate({opacity:1,scrollTop:0},{queue:!1,duration:n.inDuration,easing:"easeOutSine"})}function l(){o=!1,a.fadeOut(n.outDuration),a.removeClass("active"),i.removeClass("active"),setTimeout(function(){a.css("max-height","")},n.outDuration)}if(r(),i.after(a),n.hover){var c=!1;i.unbind("click.".concat(i.attr("id"))),i.on("mouseenter",function(){!1===c&&(s(),c=!0)}),i.on("mouseleave",function(e){var i=e.toElement||e.relatedTarget;t(i).closest(".dropdown-content").is(a)||(a.stop(!0,!0),l(),c=!1)}),a.on("mouseleave",function(e){var n=e.toElement||e.relatedTarget;t(n).closest(".dropdown-button").is(i)||(a.stop(!0,!0),l(),c=!1)})}else i.unbind("click.".concat(i.attr("id"))),i.bind("click.".concat(i.attr("id")),function(e){o||(i[0]!==e.currentTarget||i.hasClass("active")||0!==t(e.target).closest(".dropdown-content").length?i.hasClass("active")&&(l(),t(document).unbind("click.".concat(a.attr("id")," touchstart.").concat(a.attr("id")))):(e.preventDefault(),s("click")),a.hasClass("active")&&t(document).bind("click.".concat(a.attr("id")," touchstart.").concat(a.attr("id")),function(e){a.is(e.target)||i.is(e.target)||i.find(e.target).length||(l(),t(document).unbind("click.".concat(a.attr("id")," touchstart.").concat(a.attr("id"))))}))});i.on("open",function(t,e){s(e)}),i.on("close",l)})},t.fn.dropdown.defaults={inDuration:300,outDuration:225,constrain_width:!0,hover:!1,gutter:0,belowOrigin:!1,alignment:"left"},t(".dropdown-button").dropdown(),t.fn.mdbDropSearch=function(e){var i=t(this).find("input");this.filter(function(e){t(this).on("keyup",e,function(){for(var t=i.closest("div[id]").find("a, li"),e=0;e<t.length;e++)t.eq(e).html().toUpperCase().indexOf(i.val().toUpperCase())>-1?t.eq(e).css({display:""}):t.eq(e).css({display:"none"})})});var n=t.extend({color:"#000",backgroundColor:"",fontSize:".9rem",fontWeight:"400",borderRadius:"",borderColor:""},e);return this.css({color:n.color,backgroundColor:n.backgroundColor,fontSize:n.fontSize,fontWeight:n.fontWeight,borderRadius:n.borderRadius,border:n.border,margin:n.margin})}}(jQuery);var dropdownSelectors=$(".dropdown, .dropup");function dropdownEffectData(t){var e="fadeIn",i="fadeOut",n=$(t),o=$(".dropdown-menu",t),a=n.parents("ul.nav");return a.height>0&&(e=a.data("dropdown-in")||null,i=a.data("dropdown-out")||null),{target:t,dropdown:n,dropdownMenu:o,effectIn:o.data("dropdown-in")||e,effectOut:o.data("dropdown-out")||i}}function dropdownEffectStart(t,e){e&&(t.dropdown.addClass("dropdown-animating"),t.dropdownMenu.addClass(["animated",e].join(" ")))}function dropdownEffectEnd(t,e){t.dropdown.one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend",function(){t.dropdown.removeClass("dropdown-animating"),t.dropdownMenu.removeClass(["animated",t.effectIn,t.effectOut].join(" ")),"function"==typeof e&&e()})}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function _createClass(t,e,i){return e&&_defineProperties(t.prototype,e),i&&_defineProperties(t,i),t}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function _createClass(t,e,i){return e&&_defineProperties(t.prototype,e),i&&_defineProperties(t,i),t}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function _createClass(t,e,i){return e&&_defineProperties(t.prototype,e),i&&_defineProperties(t,i),t}dropdownSelectors.on({"show.bs.dropdown":function(){var t=dropdownEffectData(this);dropdownEffectStart(t,t.effectIn)},"shown.bs.dropdown":function(){var t=dropdownEffectData(this);t.effectIn&&t.effectOut&&dropdownEffectEnd(t)},"hide.bs.dropdown":function(t){var e=dropdownEffectData(this);e.effectOut&&(t.preventDefault(),dropdownEffectStart(e,e.effectOut),dropdownEffectEnd(e,function(){e.dropdown.removeClass("show"),e.dropdownMenu.removeClass("show")}))}}),function(t){var e=this;t(document).ready(function(){t(document).on("mouseenter",".fixed-action-btn",function(){var e=t(this);i(e)}),t(document).on("mouseleave",".fixed-action-btn",function(){var e=t(this);n(e)}),t(document).on("click",".fixed-action-btn > a",function(){var e=t(this).parent();e.hasClass("active")?i(e):n(e),e.hasClass("active")?n(e):i(e)})}),t.fn.extend({openFAB:function(){i(t(this))},closeFAB:function(){n(t(this))}});var i=function(t){var e=t;e.hasClass("active")||(e.addClass("active"),document.querySelectorAll("ul .btn-floating").forEach(function(t){t.classList.add("shown")}))},n=function(t){t.removeClass("active"),document.querySelectorAll("ul .btn-floating").forEach(function(t){t.classList.remove("shown")})};t(".fixed-action-btn:not(.smooth-scroll) > .btn-floating").on("click",function(o){if(!t(e).hasClass("smooth-scroll"))return o.preventDefault(),a=t(".fixed-action-btn"),(r=a).hasClass("active")?n(r):i(r),!1;var a,r})}(jQuery),function(t,e,i,n){"use strict";var o,a=["","webkit","Moz","MS","ms","o"],r=e.createElement("div"),s="function",l=Math.round,c=Math.abs,u=Date.now;function d(t,e,i){return setTimeout(b(t,i),e)}function h(t,e,i){return!!Array.isArray(t)&&(p(t,i[e],i),!0)}function p(t,e,i){var o;if(t)if(t.forEach)t.forEach(e,i);else if(t.length!==n)for(o=0;o<t.length;)e.call(i,t[o],o,t),o++;else for(o in t)t.hasOwnProperty(o)&&e.call(i,t[o],o,t)}function f(e,i,n){var o="DEPRECATED METHOD: "+i+"\n"+n+" AT \n";return function(){var i=new Error("get-stack-trace"),n=i&&i.stack?i.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",a=t.console&&(t.console.warn||t.console.log);return a&&a.call(t.console,o,n),e.apply(this,arguments)}}o="function"!=typeof Object.assign?function(t){if(t===n||null===t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),i=1;i<arguments.length;i++){var o=arguments[i];if(o!==n&&null!==o)for(var a in o)o.hasOwnProperty(a)&&(e[a]=o[a])}return e}:Object.assign;var v=f(function(t,e,i){for(var o=Object.keys(e),a=0;a<o.length;)(!i||i&&t[o[a]]===n)&&(t[o[a]]=e[o[a]]),a++;return t},"extend","Use `assign`."),g=f(function(t,e){return v(t,e,!0)},"merge","Use `assign`.");function m(t,e,i){var n,a=e.prototype;(n=t.prototype=Object.create(a)).constructor=t,n._super=a,i&&o(n,i)}function b(t,e){return function(){return t.apply(e,arguments)}}function y(t,e){return typeof t==s?t.apply(e&&e[0]||n,e):t}function w(t,e){return t===n?e:t}function S(t,e,i){p(k(e),function(e){t.addEventListener(e,i,!1)})}function x(t,e,i){p(k(e),function(e){t.removeEventListener(e,i,!1)})}function T(t,e){for(;t;){if(t==e)return!0;t=t.parentNode}return!1}function C(t,e){return t.indexOf(e)>-1}function k(t){return t.trim().split(/\s+/g)}function E(t,e,i){if(t.indexOf&&!i)return t.indexOf(e);for(var n=0;n<t.length;){if(i&&t[n][i]==e||!i&&t[n]===e)return n;n++}return-1}function O(t){return Array.prototype.slice.call(t,0)}function $(t,e,i){for(var n=[],o=[],a=0;a<t.length;){var r=e?t[a][e]:t[a];E(o,r)<0&&n.push(t[a]),o[a]=r,a++}return i&&(n=e?n.sort(function(t,i){return t[e]>i[e]}):n.sort()),n}function P(t,e){for(var i,o,r=e[0].toUpperCase()+e.slice(1),s=0;s<a.length;){if((o=(i=a[s])?i+r:e)in t)return o;s++}return n}var M=1;function L(e){var i=e.ownerDocument||e;return i.defaultView||i.parentWindow||t}var A="ontouchstart"in t,I=P(t,"PointerEvent")!==n,W=A&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),H=25,_=1,X=2,R=4,Y=8,D=1,V=2,N=4,j=8,q=16,z=V|N,F=j|q,B=z|F,U=["x","y"],Q=["clientX","clientY"];function K(t,e){var i=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){y(t.options.enable,[t])&&i.handler(e)},this.init()}function G(t,e,i){var o=i.pointers.length,a=i.changedPointers.length,r=e&_&&o-a==0,s=e&(R|Y)&&o-a==0;i.isFirst=!!r,i.isFinal=!!s,r&&(t.session={}),i.eventType=e,function(t,e){var i=t.session,o=e.pointers,a=o.length;i.firstInput||(i.firstInput=Z(e));a>1&&!i.firstMultiple?i.firstMultiple=Z(e):1===a&&(i.firstMultiple=!1);var r=i.firstInput,s=i.firstMultiple,l=s?s.center:r.center,d=e.center=J(o);e.timeStamp=u(),e.deltaTime=e.timeStamp-r.timeStamp,e.angle=nt(l,d),e.distance=it(l,d),function(t,e){var i=e.center,n=t.offsetDelta||{},o=t.prevDelta||{},a=t.prevInput||{};e.eventType!==_&&a.eventType!==R||(o=t.prevDelta={x:a.deltaX||0,y:a.deltaY||0},n=t.offsetDelta={x:i.x,y:i.y});e.deltaX=o.x+(i.x-n.x),e.deltaY=o.y+(i.y-n.y)}(i,e),e.offsetDirection=et(e.deltaX,e.deltaY);var h=tt(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=h.x,e.overallVelocityY=h.y,e.overallVelocity=c(h.x)>c(h.y)?h.x:h.y,e.scale=s?(p=s.pointers,f=o,it(f[0],f[1],Q)/it(p[0],p[1],Q)):1,e.rotation=s?function(t,e){return nt(e[1],e[0],Q)+nt(t[1],t[0],Q)}(s.pointers,o):0,e.maxPointers=i.prevInput?e.pointers.length>i.prevInput.maxPointers?e.pointers.length:i.prevInput.maxPointers:e.pointers.length,function(t,e){var i,o,a,r,s=t.lastInterval||e,l=e.timeStamp-s.timeStamp;if(e.eventType!=Y&&(l>H||s.velocity===n)){var u=e.deltaX-s.deltaX,d=e.deltaY-s.deltaY,h=tt(l,u,d);o=h.x,a=h.y,i=c(h.x)>c(h.y)?h.x:h.y,r=et(u,d),t.lastInterval=e}else i=s.velocity,o=s.velocityX,a=s.velocityY,r=s.direction;e.velocity=i,e.velocityX=o,e.velocityY=a,e.direction=r}(i,e);var p,f;var v=t.element;T(e.srcEvent.target,v)&&(v=e.srcEvent.target);e.target=v}(t,i),t.emit("hammer.input",i),t.recognize(i),t.session.prevInput=i}function Z(t){for(var e=[],i=0;i<t.pointers.length;)e[i]={clientX:l(t.pointers[i].clientX),clientY:l(t.pointers[i].clientY)},i++;return{timeStamp:u(),pointers:e,center:J(e),deltaX:t.deltaX,deltaY:t.deltaY}}function J(t){var e=t.length;if(1===e)return{x:l(t[0].clientX),y:l(t[0].clientY)};for(var i=0,n=0,o=0;o<e;)i+=t[o].clientX,n+=t[o].clientY,o++;return{x:l(i/e),y:l(n/e)}}function tt(t,e,i){return{x:e/t||0,y:i/t||0}}function et(t,e){return t===e?D:c(t)>=c(e)?t<0?V:N:e<0?j:q}function it(t,e,i){i||(i=U);var n=e[i[0]]-t[i[0]],o=e[i[1]]-t[i[1]];return Math.sqrt(n*n+o*o)}function nt(t,e,i){i||(i=U);var n=e[i[0]]-t[i[0]],o=e[i[1]]-t[i[1]];return 180*Math.atan2(o,n)/Math.PI}K.prototype={handler:function(){},init:function(){this.evEl&&S(this.element,this.evEl,this.domHandler),this.evTarget&&S(this.target,this.evTarget,this.domHandler),this.evWin&&S(L(this.element),this.evWin,this.domHandler)},destroy:function(){this.evEl&&x(this.element,this.evEl,this.domHandler),this.evTarget&&x(this.target,this.evTarget,this.domHandler),this.evWin&&x(L(this.element),this.evWin,this.domHandler)}};var ot={mousedown:_,mousemove:X,mouseup:R},at="mousedown",rt="mousemove mouseup";function st(){this.evEl=at,this.evWin=rt,this.pressed=!1,K.apply(this,arguments)}m(st,K,{handler:function(t){var e=ot[t.type];e&_&&0===t.button&&(this.pressed=!0),e&X&&1!==t.which&&(e=R),this.pressed&&(e&R&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:"mouse",srcEvent:t}))}});var lt={pointerdown:_,pointermove:X,pointerup:R,pointercancel:Y,pointerout:Y},ct={2:"touch",3:"pen",4:"mouse",5:"kinect"},ut="pointerdown",dt="pointermove pointerup pointercancel";function ht(){this.evEl=ut,this.evWin=dt,K.apply(this,arguments),this.store=this.manager.session.pointerEvents=[]}t.MSPointerEvent&&!t.PointerEvent&&(ut="MSPointerDown",dt="MSPointerMove MSPointerUp MSPointerCancel"),m(ht,K,{handler:function(t){var e=this.store,i=!1,n=t.type.toLowerCase().replace("ms",""),o=lt[n],a=ct[t.pointerType]||t.pointerType,r="touch"==a,s=E(e,t.pointerId,"pointerId");o&_&&(0===t.button||r)?s<0&&(e.push(t),s=e.length-1):o&(R|Y)&&(i=!0),s<0||(e[s]=t,this.callback(this.manager,o,{pointers:e,changedPointers:[t],pointerType:a,srcEvent:t}),i&&e.splice(s,1))}});var pt={touchstart:_,touchmove:X,touchend:R,touchcancel:Y},ft="touchstart",vt="touchstart touchmove touchend touchcancel";function gt(){this.evTarget=ft,this.evWin=vt,this.started=!1,K.apply(this,arguments)}m(gt,K,{handler:function(t){var e=pt[t.type];if(e===_&&(this.started=!0),this.started){var i=function(t,e){var i=O(t.touches),n=O(t.changedTouches);e&(R|Y)&&(i=$(i.concat(n),"identifier",!0));return[i,n]}.call(this,t,e);e&(R|Y)&&i[0].length-i[1].length==0&&(this.started=!1),this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:"touch",srcEvent:t})}}});var mt={touchstart:_,touchmove:X,touchend:R,touchcancel:Y},bt="touchstart touchmove touchend touchcancel";function yt(){this.evTarget=bt,this.targetIds={},K.apply(this,arguments)}m(yt,K,{handler:function(t){var e=mt[t.type],i=function(t,e){var i=O(t.touches),n=this.targetIds;if(e&(_|X)&&1===i.length)return n[i[0].identifier]=!0,[i,i];var o,a,r=O(t.changedTouches),s=[],l=this.target;if(a=i.filter(function(t){return T(t.target,l)}),e===_)for(o=0;o<a.length;)n[a[o].identifier]=!0,o++;o=0;for(;o<r.length;)n[r[o].identifier]&&s.push(r[o]),e&(R|Y)&&delete n[r[o].identifier],o++;if(!s.length)return;return[$(a.concat(s),"identifier",!0),s]}.call(this,t,e);i&&this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:"touch",srcEvent:t})}});var wt=2500,St=25;function xt(){K.apply(this,arguments);var t=b(this.handler,this);this.touch=new yt(this.manager,t),this.mouse=new st(this.manager,t),this.primaryTouch=null,this.lastTouches=[]}function Tt(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var i={x:e.clientX,y:e.clientY};this.lastTouches.push(i);var n=this.lastTouches;setTimeout(function(){var t=n.indexOf(i);t>-1&&n.splice(t,1)},wt)}}m(xt,K,{handler:function(t,e,i){var n="touch"==i.pointerType,o="mouse"==i.pointerType;if(!(o&&i.sourceCapabilities&&i.sourceCapabilities.firesTouchEvents)){if(n)(function(t,e){t&_?(this.primaryTouch=e.changedPointers[0].identifier,Tt.call(this,e)):t&(R|Y)&&Tt.call(this,e)}).call(this,e,i);else if(o&&function(t){for(var e=t.srcEvent.clientX,i=t.srcEvent.clientY,n=0;n<this.lastTouches.length;n++){var o=this.lastTouches[n],a=Math.abs(e-o.x),r=Math.abs(i-o.y);if(a<=St&&r<=St)return!0}return!1}.call(this,i))return;this.callback(t,e,i)}},destroy:function(){this.touch.destroy(),this.mouse.destroy()}});var Ct=P(r.style,"touchAction"),kt=Ct!==n,Et="auto",Ot="manipulation",$t="none",Pt="pan-x",Mt="pan-y",Lt=function(){if(!kt)return!1;var e={},i=t.CSS&&t.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach(function(n){e[n]=!i||t.CSS.supports("touch-action",n)}),e}();function At(t,e){this.manager=t,this.set(e)}At.prototype={set:function(t){"compute"==t&&(t=this.compute()),kt&&this.manager.element.style&&Lt[t]&&(this.manager.element.style[Ct]=t),this.actions=t.toLowerCase().trim()},update:function(){this.set(this.manager.options.touchAction)},compute:function(){var t=[];return p(this.manager.recognizers,function(e){y(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))}),function(t){if(C(t,$t))return $t;var e=C(t,Pt),i=C(t,Mt);if(e&&i)return $t;if(e||i)return e?Pt:Mt;if(C(t,Ot))return Ot;return Et}(t.join(" "))},preventDefaults:function(t){var e=t.srcEvent,i=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var n=this.actions,o=C(n,$t)&&!Lt[$t],a=C(n,Mt)&&!Lt[Mt],r=C(n,Pt)&&!Lt[Pt];if(o){var s=1===t.pointers.length,l=t.distance<2,c=t.deltaTime<250;if(s&&l&&c)return}if(!r||!a)return o||a&&i&z||r&&i&F?this.preventSrc(e):void 0}},preventSrc:function(t){this.manager.session.prevented=!0,t.preventDefault()}};var It=1,Wt=2,Ht=4,_t=8,Xt=_t,Rt=16;function Yt(t){this.options=o({},this.defaults,t||{}),this.id=M++,this.manager=null,this.options.enable=w(this.options.enable,!0),this.state=It,this.simultaneous={},this.requireFail=[]}function Dt(t){return t&Rt?"cancel":t&_t?"end":t&Ht?"move":t&Wt?"start":""}function Vt(t){return t==q?"down":t==j?"up":t==V?"left":t==N?"right":""}function Nt(t,e){var i=e.manager;return i?i.get(t):t}function jt(){Yt.apply(this,arguments)}function qt(){jt.apply(this,arguments),this.pX=null,this.pY=null}function zt(){jt.apply(this,arguments)}function Ft(){Yt.apply(this,arguments),this._timer=null,this._input=null}function Bt(){jt.apply(this,arguments)}function Ut(){jt.apply(this,arguments)}function Qt(){Yt.apply(this,arguments),this.pTime=!1,this.pCenter=!1,this._timer=null,this._input=null,this.count=0}function Kt(t,e){return(e=e||{}).recognizers=w(e.recognizers,Kt.defaults.preset),new Gt(t,e)}Yt.prototype={defaults:{},set:function(t){return o(this.options,t),this.manager&&this.manager.touchAction.update(),this},recognizeWith:function(t){if(h(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=Nt(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},dropRecognizeWith:function(t){return h(t,"dropRecognizeWith",this)?this:(t=Nt(t,this),delete this.simultaneous[t.id],this)},requireFailure:function(t){if(h(t,"requireFailure",this))return this;var e=this.requireFail;return-1===E(e,t=Nt(t,this))&&(e.push(t),t.requireFailure(this)),this},dropRequireFailure:function(t){if(h(t,"dropRequireFailure",this))return this;t=Nt(t,this);var e=E(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},hasRequireFailures:function(){return this.requireFail.length>0},canRecognizeWith:function(t){return!!this.simultaneous[t.id]},emit:function(t){var e=this,i=this.state;function n(i){e.manager.emit(i,t)}i<_t&&n(e.options.event+Dt(i)),n(e.options.event),t.additionalEvent&&n(t.additionalEvent),i>=_t&&n(e.options.event+Dt(i))},tryEmit:function(t){if(this.canEmit())return this.emit(t);this.state=32},canEmit:function(){for(var t=0;t<this.requireFail.length;){if(!(this.requireFail[t].state&(32|It)))return!1;t++}return!0},recognize:function(t){var e=o({},t);if(!y(this.options.enable,[this,e]))return this.reset(),void(this.state=32);this.state&(Xt|Rt|32)&&(this.state=It),this.state=this.process(e),this.state&(Wt|Ht|_t|Rt)&&this.tryEmit(e)},process:function(t){},getTouchAction:function(){},reset:function(){}},m(jt,Yt,{defaults:{pointers:1},attrTest:function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},process:function(t){var e=this.state,i=t.eventType,n=e&(Wt|Ht),o=this.attrTest(t);return n&&(i&Y||!o)?e|Rt:n||o?i&R?e|_t:e&Wt?e|Ht:Wt:32}}),m(qt,jt,{defaults:{event:"pan",threshold:10,pointers:1,direction:B},getTouchAction:function(){var t=this.options.direction,e=[];return t&z&&e.push(Mt),t&F&&e.push(Pt),e},directionTest:function(t){var e=this.options,i=!0,n=t.distance,o=t.direction,a=t.deltaX,r=t.deltaY;return o&e.direction||(e.direction&z?(o=0===a?D:a<0?V:N,i=a!=this.pX,n=Math.abs(t.deltaX)):(o=0===r?D:r<0?j:q,i=r!=this.pY,n=Math.abs(t.deltaY))),t.direction=o,i&&n>e.threshold&&o&e.direction},attrTest:function(t){return jt.prototype.attrTest.call(this,t)&&(this.state&Wt||!(this.state&Wt)&&this.directionTest(t))},emit:function(t){this.pX=t.deltaX,this.pY=t.deltaY;var e=Vt(t.direction);e&&(t.additionalEvent=this.options.event+e),this._super.emit.call(this,t)}}),m(zt,jt,{defaults:{event:"pinch",threshold:0,pointers:2},getTouchAction:function(){return[$t]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.scale-1)>this.options.threshold||this.state&Wt)},emit:function(t){if(1!==t.scale){var e=t.scale<1?"in":"out";t.additionalEvent=this.options.event+e}this._super.emit.call(this,t)}}),m(Ft,Yt,{defaults:{event:"press",pointers:1,time:251,threshold:9},getTouchAction:function(){return[Et]},process:function(t){var e=this.options,i=t.pointers.length===e.pointers,n=t.distance<e.threshold,o=t.deltaTime>e.time;if(this._input=t,!n||!i||t.eventType&(R|Y)&&!o)this.reset();else if(t.eventType&_)this.reset(),this._timer=d(function(){this.state=Xt,this.tryEmit()},e.time,this);else if(t.eventType&R)return Xt;return 32},reset:function(){clearTimeout(this._timer)},emit:function(t){this.state===Xt&&(t&&t.eventType&R?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=u(),this.manager.emit(this.options.event,this._input)))}}),m(Bt,jt,{defaults:{event:"rotate",threshold:0,pointers:2},getTouchAction:function(){return[$t]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.rotation)>this.options.threshold||this.state&Wt)}}),m(Ut,jt,{defaults:{event:"swipe",threshold:10,velocity:.3,direction:z|F,pointers:1},getTouchAction:function(){return qt.prototype.getTouchAction.call(this)},attrTest:function(t){var e,i=this.options.direction;return i&(z|F)?e=t.overallVelocity:i&z?e=t.overallVelocityX:i&F&&(e=t.overallVelocityY),this._super.attrTest.call(this,t)&&i&t.offsetDirection&&t.distance>this.options.threshold&&t.maxPointers==this.options.pointers&&c(e)>this.options.velocity&&t.eventType&R},emit:function(t){var e=Vt(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)}}),m(Qt,Yt,{defaults:{event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},getTouchAction:function(){return[Ot]},process:function(t){var e=this.options,i=t.pointers.length===e.pointers,n=t.distance<e.threshold,o=t.deltaTime<e.time;if(this.reset(),t.eventType&_&&0===this.count)return this.failTimeout();if(n&&o&&i){if(t.eventType!=R)return this.failTimeout();var a=!this.pTime||t.timeStamp-this.pTime<e.interval,r=!this.pCenter||it(this.pCenter,t.center)<e.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,r&&a?this.count+=1:this.count=1,this._input=t,0===this.count%e.taps)return this.hasRequireFailures()?(this._timer=d(function(){this.state=Xt,this.tryEmit()},e.interval,this),Wt):Xt}return 32},failTimeout:function(){return this._timer=d(function(){this.state=32},this.options.interval,this),32},reset:function(){clearTimeout(this._timer)},emit:function(){this.state==Xt&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))}}),Kt.VERSION="2.0.7",Kt.defaults={domEvents:!1,touchAction:"compute",enable:!0,inputTarget:null,inputClass:null,preset:[[Bt,{enable:!1}],[zt,{enable:!1},["rotate"]],[Ut,{direction:z}],[qt,{direction:z},["swipe"]],[Qt],[Qt,{event:"doubletap",taps:2},["tap"]],[Ft]],cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}};function Gt(t,e){var i;this.options=o({},Kt.defaults,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((i=this).options.inputClass||(I?ht:W?yt:A?xt:st))(i,G),this.touchAction=new At(this,this.options.touchAction),Zt(this,!0),p(this.options.recognizers,function(t){var e=this.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])},this)}function Zt(t,e){var i,n=t.element;n.style&&(p(t.options.cssProps,function(o,a){i=P(n.style,a),e?(t.oldCssProps[i]=n.style[i],n.style[i]=o):n.style[i]=t.oldCssProps[i]||""}),e||(t.oldCssProps={}))}Gt.prototype={set:function(t){return o(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},stop:function(t){this.session.stopped=t?2:1},recognize:function(t){var e=this.session;if(!e.stopped){var i;this.touchAction.preventDefaults(t);var n=this.recognizers,o=e.curRecognizer;(!o||o&&o.state&Xt)&&(o=e.curRecognizer=null);for(var a=0;a<n.length;)i=n[a],2===e.stopped||o&&i!=o&&!i.canRecognizeWith(o)?i.reset():i.recognize(t),!o&&i.state&(Wt|Ht|_t)&&(o=e.curRecognizer=i),a++}},get:function(t){if(t instanceof Yt)return t;for(var e=this.recognizers,i=0;i<e.length;i++)if(e[i].options.event==t)return e[i];return null},add:function(t){if(h(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},remove:function(t){if(h(t,"remove",this))return this;if(t=this.get(t)){var e=this.recognizers,i=E(e,t);-1!==i&&(e.splice(i,1),this.touchAction.update())}return this},on:function(t,e){if(t!==n&&e!==n){var i=this.handlers;return p(k(t),function(t){i[t]=i[t]||[],i[t].push(e)}),this}},off:function(t,e){if(t!==n){var i=this.handlers;return p(k(t),function(t){e?i[t]&&i[t].splice(E(i[t],e),1):delete i[t]}),this}},emit:function(t,i){this.options.domEvents&&function(t,i){var n=e.createEvent("Event");n.initEvent(t,!0,!0),n.gesture=i,i.target.dispatchEvent(n)}(t,i);var n=this.handlers[t]&&this.handlers[t].slice();if(n&&n.length){i.type=t,i.preventDefault=function(){i.srcEvent.preventDefault()};for(var o=0;o<n.length;)n[o](i),o++}},destroy:function(){this.element&&Zt(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null}},o(Kt,{INPUT_START:_,INPUT_MOVE:X,INPUT_END:R,INPUT_CANCEL:Y,STATE_POSSIBLE:It,STATE_BEGAN:Wt,STATE_CHANGED:Ht,STATE_ENDED:_t,STATE_RECOGNIZED:Xt,STATE_CANCELLED:Rt,STATE_FAILED:32,DIRECTION_NONE:D,DIRECTION_LEFT:V,DIRECTION_RIGHT:N,DIRECTION_UP:j,DIRECTION_DOWN:q,DIRECTION_HORIZONTAL:z,DIRECTION_VERTICAL:F,DIRECTION_ALL:B,Manager:Gt,Input:K,TouchAction:At,TouchInput:yt,MouseInput:st,PointerEventInput:ht,TouchMouseInput:xt,SingleTouchInput:gt,Recognizer:Yt,AttrRecognizer:jt,Tap:Qt,Pan:qt,Swipe:Ut,Pinch:zt,Rotate:Bt,Press:Ft,on:S,off:x,each:p,merge:g,extend:v,assign:o,inherit:m,bindFn:b,prefixed:P}),(void 0!==t?t:"undefined"!=typeof self?self:{}).Hammer=Kt,"function"==typeof define&&define.amd?define(function(){return Kt}):"undefined"!=typeof module&&module.exports?module.exports=Kt:t.Hammer=Kt}(window,document),function(t){"function"==typeof define&&define.amd?define(["jquery","hammerjs"],t):"object"==typeof exports?t(require("jquery"),require("hammerjs")):t(jQuery,Hammer)}(function(t,e){var i;t.fn.hammer=function(i){return this.each(function(){!function(i,n){var o=t(i);o.data("hammer")||o.data("hammer",new e(o[0],n))}(this,i)})},e.Manager.prototype.emit=(i=e.Manager.prototype.emit,function(e,n){i.call(this,e,n),t(this.element).trigger({type:e,gesture:n})})}),function(t){var e=240,i=function(){function i(n,o){_classCallCheck(this,i),this.defaults={MENU_WIDTH:e,edge:"left",closeOnClick:!1},this.$element=n,this.options=this.assignOptions(o),this.menuOut=!1,this.$body=t("body"),this.$menu=t("#".concat(this.$element.attr("data-activates"))),this.$sidenavOverlay=t("#sidenav-overlay"),this.$dragTarget=t('<div class="drag-target"></div>'),this.$body.append(this.$dragTarget),this.init()}return _createClass(i,[{key:"init",value:function(){this.setMenuWidth(),this.setMenuTranslation(),this.closeOnClick(),this.openOnClick(),this.bindTouchEvents()}},{key:"bindTouchEvents",value:function(){var t=this;this.$dragTarget.on("click",function(){t.removeMenu()}),this.$dragTarget.hammer({prevent_default:!1}).bind("pan",this.panEventHandler.bind(this)).bind("panend",this.panendEventHandler.bind(this))}},{key:"panEventHandler",value:function(t){if("touch"===t.gesture.pointerType){var e=t.gesture.center.x;this.disableScrolling(),0!==this.$sidenavOverlay.length||this.buildSidenavOverlay(),"left"===this.options.edge&&(e>this.options.MENU_WIDTH?e=this.options.MENU_WIDTH:e<0&&(e=0)),this.translateSidenavX(e),this.updateOverlayOpacity(e)}}},{key:"translateSidenavX",value:function(t){if("left"===this.options.edge){var e=t>=this.options.MENU_WIDTH/2;this.menuOut=e,this.$menu.css("transform","translateX(".concat(t-this.options.MENU_WIDTH,"px)"))}else{var i=t<window.innerWidth-this.options.MENU_WIDTH/2;this.menuOut=i;var n=t-this.options.MENU_WIDTH/2;n<0&&(n=0),this.$menu.css("transform","translateX(".concat(n,"px)"))}}},{key:"updateOverlayOpacity",value:function(t){var e;e="left"===this.options.edge?t/this.options.MENU_WIDTH:Math.abs((t-window.innerWidth)/this.options.MENU_WIDTH),this.$sidenavOverlay.velocity({opacity:e},{duration:10,queue:!1,easing:"easeOutQuad"})}},{key:"buildSidenavOverlay",value:function(){var e=this;this.$sidenavOverlay=t('<div id="sidenav-overlay"></div>'),this.$sidenavOverlay.css("opacity",0).on("click",function(){e.removeMenu()}),this.$body.append(this.$sidenavOverlay)}},{key:"disableScrolling",value:function(){var t=this.$body.innerWidth();this.$body.css("overflow","hidden"),this.$body.width(t)}},{key:"panendEventHandler",value:function(t){if("touch"===t.gesture.pointerType){var e=t.gesture.velocityX,i=t.gesture.center.x,n=i-this.options.MENU_WIDTH,o=i-this.options.MENU_WIDTH/2;n>0&&(n=0),o<0&&(o=0),"left"===this.options.edge?(this.menuOut&&e<=.3||e<-.5?(0!==n&&this.translateMenuX([0,n],"300"),this.showSidenavOverlay()):(!this.menuOut||e>.3)&&(this.enableScrolling(),this.translateMenuX([-1*this.options.MENU_WIDTH-10,n],"200"),this.hideSidenavOverlay()),this.$dragTarget.css({width:"10px",right:"",left:0})):this.menuOut&&e>=-.3||e>.5?(this.translateMenuX([0,o],"300"),this.showSidenavOverlay(),this.$dragTarget.css({width:"50%",right:"",left:0})):(!this.menuOut||e<-.3)&&(this.enableScrolling(),this.translateMenuX([this.options.MENU_WIDTH+10,o],"200"),this.hideSidenavOverlay(),this.$dragTarget.css({width:"10px",right:0,left:""}))}}},{key:"translateMenuX",value:function(t,e){this.$menu.velocity({translateX:t},{duration:"string"==typeof e?Number(e):e,queue:!1,easing:"easeOutQuad"})}},{key:"hideSidenavOverlay",value:function(){this.$sidenavOverlay.velocity({opacity:0},{duration:200,queue:!1,easing:"easeOutQuad",complete:function(){t(this).remove()}}),this.$sidenavOverlay=t()}},{key:"showSidenavOverlay",value:function(){this.$sidenavOverlay.velocity({opacity:1},{duration:50,queue:!1,easing:"easeOutQuad"})}},{key:"enableScrolling",value:function(){this.$body.css({overflow:"",width:""})}},{key:"openOnClick",value:function(){var e=this;this.$element.on("click",function(i){if(i.preventDefault(),!0===e.menuOut)e.menuOut=!1,e.removeMenu();else{e.$sidenavOverlay=t('<div id="sidenav-overlay"></div>'),e.$body.append(e.$sidenavOverlay);var n=[];n="left"===e.options.edge?[0,-1*e.options.MENU_WIDTH]:[0,e.options.MENU_WIDTH],e.$menu.velocity({translateX:n},{duration:300,queue:!1,easing:"easeOutQuad"}),e.$sidenavOverlay.on("click",function(){e.removeMenu()})}})}},{key:"closeOnClick",value:function(){var t=this;!0===this.options.closeOnClick&&this.$menu.on("click","a:not(.collapsible-header)",function(){t.removeMenu()})}},{key:"setMenuTranslation",value:function(){var e=this;"left"===this.options.edge?(this.$menu.css("transform","translateX(-100%)"),this.$dragTarget.css({left:0})):(this.$menu.addClass("right-aligned").css("transform","translateX(100%)"),this.$dragTarget.css({right:0})),this.$menu.hasClass("fixed")&&(window.innerWidth>1440&&this.$menu.css("transform","translateX(0)"),t(window).resize(function(){if(window.innerWidth>1440)e.$sidenavOverlay.length?e.removeMenu(!0):e.$menu.css("transform","translateX(0%)");else if(!1===e.menuOut){var t="left"===e.options.edge?"-100":"100";e.$menu.css("transform","translateX(".concat(t,"%)"))}}))}},{key:"setMenuWidth",value:function(){var i=t("#".concat(this.$menu.attr("id"))).find("> .sidenav-bg");this.options.MENU_WIDTH!==e&&(this.$menu.css("width",this.options.MENU_WIDTH),i.css("width",this.options.MENU_WIDTH))}},{key:"assignOptions",value:function(e){return t.extend({},this.defaults,e)}},{key:"removeMenu",value:function(t){var e=this;this.$body.css({overflow:"",width:""}),this.$menu.velocity({translateX:"left"===this.options.edge?"-100%":"100%"},{duration:200,queue:!1,easing:"easeOutCubic",complete:function(){!0===t&&(e.$menu.removeAttr("style"),e.$menu.css("width",e.options.MENU_WIDTH))}}),this.hideSidenavOverlay()}},{key:"show",value:function(){this.trigger("click")}},{key:"hide",value:function(){this.$sidenavOverlay.trigger("click")}}]),i}();t.fn.sideNav=function(e){return this.each(function(){new i(t(this),e)})}}(jQuery),function(t){t.fn.collapsible=function(e){var i={accordion:void 0};function n(e,i){$panelHeaders=e.find("> li > .collapsible-header"),i.hasClass("active")?i.parent().addClass("active"):i.parent().removeClass("active"),i.parent().hasClass("active")?i.siblings(".collapsible-body").stop(!0,!1).slideDown({duration:350,easing:"easeOutQuart",queue:!1,complete:function(){t(this).css("height","")}}):i.siblings(".collapsible-body").stop(!0,!1).slideUp({duration:350,easing:"easeOutQuart",queue:!1,complete:function(){t(this).css("height","")}}),$panelHeaders.not(i).removeClass("active").parent().removeClass("active"),$panelHeaders.not(i).parent().children(".collapsible-body").stop(!0,!1).slideUp({duration:350,easing:"easeOutQuart",queue:!1,complete:function(){t(this).css("height","")}})}function o(e){e.hasClass("active")?e.parent().addClass("active"):e.parent().removeClass("active"),e.parent().hasClass("active")?e.siblings(".collapsible-body").stop(!0,!1).slideDown({duration:350,easing:"easeOutQuart",queue:!1,complete:function(){t(this).css("height","")}}):e.siblings(".collapsible-body").stop(!0,!1).slideUp({duration:350,easing:"easeOutQuart",queue:!1,complete:function(){t(this).css("height","")}})}function a(t){return r(t).length>0}function r(t){return t.closest("li > .collapsible-header")}return e=t.extend(i,e),this.each(function(){var i=t(this),s=t(this).find("> li > .collapsible-header"),l=i.data("collapsible");i.off("click.collapse",".collapsible-header"),s.off("click.collapse"),e.accordion||"accordion"===l||void 0===l?((s=i.find("> li > .collapsible-header")).on("click.collapse",function(e){var o=t(e.target);a(o)&&(o=r(o)),o.toggleClass("active"),n(i,o)}),n(i,s.filter(".active").first())):s.each(function(){t(this).on("click.collapse",function(e){var i=t(e.target);a(i)&&(i=r(i)),i.toggleClass("active"),o(i)}),t(this).hasClass("active")&&o(t(this))})})},t(".collapsible").collapsible()}(jQuery),function(t){var e,i="input[type=range]:not(.custom-range)",n=!1;t(document).on("change",i,function(){var e=t(this);e.siblings(".thumb").find(".value").html(e.val())}),t(document).on("input mousedown touchstart",i,function(o){var a=t(this),r=a.siblings(".thumb"),s=a.outerWidth();if(!r.length&&function(){var e=t('<span class="thumb"><span class="value"></span></span>');t(i).after(e)}(),r.find(".value").html(a.val()),n=!0,a.addClass("active"),r.hasClass("active")||r.velocity({height:"30px",width:"30px",top:"-20px",marginLeft:"-15px"},{duration:300,easing:"easeOutExpo"}),"input"!==o.type){var l=void 0===o.pageX||null===o.pageX;(e=l?o.originalEvent.touches[0].pageX-t(this).offset().left:o.pageX-t(this).offset().left)<0?e=0:e>s&&(e=s),r.addClass("active").css("left",e)}r.find(".value").html(a.val())}),t(document).on("mouseup touchend",".range-field",function(){n=!1,t(this).removeClass("active")}),t(document).on("mousemove touchmove",".range-field",function(e){var o,a=t(this).children(".thumb");if(n){a.hasClass("active")||a.velocity({height:"30px",width:"30px",top:"-20px",marginLeft:"-15px"},{duration:300,easing:"easeOutExpo"}),o=void 0===e.pageX||null===e.pageX?e.originalEvent.touches[0].pageX-t(this).offset().left:e.pageX-t(this).offset().left;var r=t(this).outerWidth();o<0?o=0:o>r&&(o=r),a.addClass("active").css("left",o),a.find(".value").html(a.siblings(i).val())}}),t(document).on("mouseout touchleave",".range-field",function(){if(!n){var e=t(this).children(".thumb");e.hasClass("active")&&e.velocity({height:"0",width:"0",top:"10px",marginLeft:"-6px"},{duration:100}),e.removeClass("active")}})}(jQuery),function(t){t(document).on("change",'.file-field input[type="file"]',function(e){var i=t(e.target),n=i.closest(".file-field").find("input.file-path"),o=i[0].files,a=[];Array.isArray(o)?o.forEach(function(t){return a.push(t.name)}):Object.keys(o).forEach(function(t){a.push(o[t].name)}),n.val(a.join(", ")),n.trigger("change")})}(jQuery),function(t){var e,i=function(){function e(i,n){_classCallCheck(this,e),this.options=n,this.$nativeSelect=i,this.isMultiple=Boolean(this.$nativeSelect.attr("multiple")),this.isSearchable=Boolean(this.$nativeSelect.attr("searchable")),this.isRequired=Boolean(this.$nativeSelect.attr("required")),this.uuid=this._randomUUID(),this.$selectWrapper=t('<div class="select-wrapper"></div>'),this.$materialOptionsList=t('<ul id="select-options-'.concat(this.uuid,'" class="dropdown-content select-dropdown w-100 ').concat(this.isMultiple?"multiple-select-dropdown":"",'"></ul>')),this.$materialSelectInitialOption=i.find("option:selected").html()||i.find("option:first").html()||"",this.$nativeSelectChildren=this.$nativeSelect.children("option, optgroup"),this.$materialSelect=t('<input type="text" class="select-dropdown" readonly="true" '.concat(this.$nativeSelect.is(":disabled")?"disabled":"",' data-activates="select-options-').concat(this.uuid,'" value=""/>')),this.$dropdownIcon=t('<span class="caret">&#9660;</span>'),this.$searchInput=null,this.$toggleAll=t('<li class="select-toggle-all"><span><input type="checkbox" class="form-check-input"><label>Select all</label></span></li>'),this.valuesSelected=[],this.keyCodes={tab:9,esc:27,enter:13,arrowUp:38,arrowDown:40},e.mutationObservers=[]}return _createClass(e,[{key:"init",value:function(){if(Boolean(this.$nativeSelect.data("select-id"))&&this._removeMaterialWrapper(),"destroy"!==this.options){this.$nativeSelect.data("select-id",this.uuid),this.$selectWrapper.addClass(this.$nativeSelect.attr("class"));var t=this.$materialSelectInitialOption.replace(/"/g,"&quot;");this.$materialSelect.val(t),this.renderMaterialSelect(),this.bindEvents(),this.isRequired&&this.enableValidation()}else this.$nativeSelect.data("select-id",null).removeClass("initialized")}},{key:"_removeMaterialWrapper",value:function(){var e=this.$nativeSelect.data("select-id");this.$nativeSelect.parent().find("span.caret").remove(),this.$nativeSelect.parent().find("input").remove(),this.$nativeSelect.unwrap(),t("ul#select-options-".concat(e)).remove()}},{key:"renderMaterialSelect",value:function(){var e=this;if(this.$nativeSelect.before(this.$selectWrapper),this.appendDropdownIcon(),this.appendMaterialSelect(),this.appendMaterialOptionsList(),this.appendNativeSelect(),this.appendSaveSelectButton(),this.$nativeSelect.is(":disabled")||this.$materialSelect.dropdown({hover:!1,closeOnClick:!1}),!1!==this.$nativeSelect.data("inherit-tabindex")&&this.$materialSelect.attr("tabindex",this.$nativeSelect.attr("tabindex")),this.isMultiple)this.$nativeSelect.find("option:selected:not(:disabled)").each(function(i,n){var o=t(n).index();e._toggleSelectedValue(o),e.$materialOptionsList.find("li:not(.optgroup):not(.select-toggle-all)").eq(o).find(":checkbox").prop("checked",!0)});else{var i=this.$nativeSelect.find("option:selected").index();this.$materialOptionsList.find("li").eq(i).addClass("active")}this.$nativeSelect.addClass("initialized")}},{key:"appendDropdownIcon",value:function(){this.$nativeSelect.is(":disabled")&&this.$dropdownIcon.addClass("disabled"),this.$selectWrapper.append(this.$dropdownIcon)}},{key:"appendMaterialSelect",value:function(){this.$selectWrapper.append(this.$materialSelect)}},{key:"appendMaterialOptionsList",value:function(){this.isSearchable&&this.appendSearchInputOption(),this.buildMaterialOptions(),this.isMultiple&&this.appendToggleAllCheckbox(),this.$selectWrapper.append(this.$materialOptionsList)}},{key:"appendNativeSelect",value:function(){this.$nativeSelect.appendTo(this.$selectWrapper)}},{key:"appendSearchInputOption",value:function(){var e=this.$nativeSelect.attr("searchable");this.$searchInput=t('<span class="search-wrap ml-2"><div class="md-form mt-0"><input type="text" class="search form-control w-100 d-block" placeholder="'.concat(e,'"></div></span>')),this.$materialOptionsList.append(this.$searchInput)}},{key:"appendToggleAllCheckbox",value:function(){this.$materialOptionsList.find("li.disabled").first().after(this.$toggleAll)}},{key:"appendSaveSelectButton",value:function(){this.$selectWrapper.parent().find("button.btn-save").appendTo(this.$materialOptionsList)}},{key:"buildMaterialOptions",value:function(){var e=this;this.$nativeSelectChildren.each(function(i,n){var o=t(n);if(o.is("option"))e.buildSingleOption(o,e.isMultiple?"multiple":"");else if(o.is("optgroup")){var a=t('<li class="optgroup"><span>'.concat(o.attr("label"),"</span></li>"));e.$materialOptionsList.append(a),o.children("option").each(function(i,n){e.buildSingleOption(t(n),"optgroup-option")})}})}},{key:"buildSingleOption",value:function(e,i){var n=e.is(":disabled")?"disabled":"",o="optgroup-option"===i?"optgroup-option":"",a=e.data("icon"),r=e.data("fa")?'<i class="fa fa-'.concat(e.data("fa"),'"></i>'):"",s=e.attr("class"),l=a?'<img alt="" src="'.concat(a,'" class="').concat(s,'">'):"",c=this.isMultiple?'<input type="checkbox" class="form-check-input" '.concat(n,"/><label></label>"):"";this.$materialOptionsList.append(t('<li class="'.concat(n," ").concat(o,'">').concat(l,'<span class="filtrable">').concat(c," ").concat(r," ").concat(e.html(),"</span></li>")))}},{key:"enableValidation",value:function(){this.$nativeSelect.css({position:"absolute",top:"1rem",left:"0",height:"0",width:"0",opacity:"0",padding:"0","pointer-events":"none"}),-1===this.$nativeSelect.attr("style").indexOf("inline!important")&&this.$nativeSelect.attr("style","".concat(this.$nativeSelect.attr("style")," display: inline!important;")),this.$nativeSelect.attr("tabindex",-1),this.$nativeSelect.data("inherit-tabindex",!1)}},{key:"bindEvents",value:function(){var i=this,n=new MutationObserver(this._onMutationObserverChange.bind(this));n.observe(this.$nativeSelect.get(0),{attributes:!0,childList:!0,characterData:!0,subtree:!0}),n.customId=this.uuid,n.customStatus="observing",e.clearMutationObservers(),e.mutationObservers.push(n),this.$nativeSelect.parent().find("button.btn-save").on("click",this._onSaveSelectBtnClick),this.$materialSelect.on("focus",this._onMaterialSelectFocus.bind(this)),this.$materialSelect.on("click",this._onMaterialSelectClick.bind(this)),this.$materialSelect.on("blur",this._onMaterialSelectBlur.bind(this)),this.$materialSelect.on("keydown",this._onMaterialSelectKeydown.bind(this)),this.$toggleAll.on("click",this._onToggleAllClick.bind(this)),this.$materialOptionsList.on("mousedown",this._onEachMaterialOptionMousedown.bind(this)),this.$materialOptionsList.find("li:not(.optgroup)").not(this.$toggleAll).each(function(e,n){t(n).on("click",i._onEachMaterialOptionClick.bind(i,e,n))}),!this.isMultiple&&this.isSearchable&&this.$materialOptionsList.find("li").on("click",this._onSingleMaterialOptionClick.bind(this)),this.isSearchable&&this.$searchInput.find(".search").on("keyup",this._onSearchInputKeyup),t("html").on("click",this._onHTMLClick.bind(this))}},{key:"_onMutationObserverChange",value:function(i){i.forEach(function(i){var n=t(i.target).closest("select");!0!==n.data("stop-refresh")&&("childList"===i.type||"attributes"===i.type&&t(i.target).is("option"))&&(e.clearMutationObservers(),n.materialSelect("destroy"),n.materialSelect())})}},{key:"_onSaveSelectBtnClick",value:function(){t("input.select-dropdown").trigger("close")}},{key:"_onEachMaterialOptionClick",value:function(e,i,n){n.stopPropagation();var o=t(i);if(!o.hasClass("disabled")&&!o.hasClass("optgroup")){var a=!0;if(this.isMultiple){o.find('input[type="checkbox"]').prop("checked",function(t,e){return!e});var r=Boolean(this.$nativeSelect.find("optgroup").length),s=this._isToggleAllPresent()?o.index()-1:o.index();a=this.isSearchable&&r?this._toggleSelectedValue(s-o.prevAll(".optgroup").length-1):this.isSearchable?this._toggleSelectedValue(s-1):r?this._toggleSelectedValue(s-o.prevAll(".optgroup").length):this._toggleSelectedValue(s),this._isToggleAllPresent()&&this._updateToggleAllOption(),this.$materialSelect.trigger("focus")}else this.$materialOptionsList.find("li").removeClass("active"),o.toggleClass("active"),this.$materialSelect.val(o.text()),this.$materialSelect.trigger("close");this._selectSingleOption(o),this.$nativeSelect.data("stop-refresh",!0),this.$nativeSelect.find("option").eq(e).prop("selected",a),this.$nativeSelect.removeData("stop-refresh"),this._triggerChangeOnNativeSelect(),"function"==typeof this.options&&this.options()}}},{key:"_triggerChangeOnNativeSelect",value:function(){var t=new KeyboardEvent("change",{bubbles:!0,cancelable:!0});this.$nativeSelect.get(0).dispatchEvent(t)}},{key:"_onMaterialSelectFocus",value:function(e){var i=t(e.target);if(t("ul.select-dropdown").not(this.$materialOptionsList.get(0)).is(":visible")&&t("input.select-dropdown").trigger("close"),!this.$materialOptionsList.is(":visible")){i.trigger("open",["focus"]);var n=i.val(),o=this.$materialOptionsList.find("li").filter(function(){return t(this).text().toLowerCase()===n.toLowerCase()})[0];this._selectSingleOption(o)}}},{key:"_onMaterialSelectClick",value:function(t){t.stopPropagation()}},{key:"_onMaterialSelectBlur",value:function(e){var i=t(e);this.isMultiple||this.isSearchable||i.trigger("close"),this.$materialOptionsList.find("li.selected").removeClass("selected")}},{key:"_onSingleMaterialOptionClick",value:function(){this.$materialSelect.trigger("close")}},{key:"_onEachMaterialOptionMousedown",value:function(e){var i=e.target;t(".modal-content").find(this.$materialOptionsList).length&&i.scrollHeight>i.offsetHeight&&e.preventDefault()}},{key:"_onHTMLClick",value:function(e){t(e.target).closest("#select-options-".concat(this.uuid)).length||this.$materialSelect.trigger("close")}},{key:"_onToggleAllClick",value:function(){var e=this,i=t(this.$toggleAll).find('input[type="checkbox"]').first(),n=!t(i).prop("checked");t(i).prop("checked",n),this.$materialOptionsList.find("li:not(.optgroup):not(.disabled):not(.select-toggle-all)").each(function(i,o){var a=t(o).find('input[type="checkbox"]');n&&a.is(":checked")||!n&&!a.is(":checked")||(e._isToggleAllPresent()&&i++,a.prop("checked",n),e.$nativeSelect.find("option").eq(i).prop("selected",n),n?t(o).removeClass("active"):t(o).addClass("active"),e._toggleSelectedValue(i),e._selectOption(o),e._setValueToMaterialSelect())}),this.$nativeSelect.data("stop-refresh",!0),this._triggerChangeOnNativeSelect(),this.$nativeSelect.removeData("stop-refresh")}},{key:"_onMaterialSelectKeydown",value:function(e){var i=t(e.target),n=e.which===this.keyCodes.tab,o=e.which===this.keyCodes.esc,a=e.which===this.keyCodes.enter,r=e.which===this.keyCodes.arrowUp,s=e.which===this.keyCodes.arrowDown,l=this.$materialOptionsList.is(":visible");n?this._handleTabKey(i):!s||l?a&&!l||(e.preventDefault(),a?this._handleEnterKey(i):s?this._handleArrowDownKey():r?this._handleArrowUpKey():o?this._handleEscKey(i):this._handleLetterKey(e)):i.trigger("open")}},{key:"_handleTabKey",value:function(t){this._handleEscKey(t)}},{key:"_handleEnterKey",value:function(e){var i=t(e);this.$materialOptionsList.find("li.selected:not(.disabled)").trigger("click"),this.isMultiple||i.trigger("close")}},{key:"_handleArrowDownKey",value:function(){var t=this.$materialOptionsList.find("li").not(".disabled").not(".select-toggle-all").first(),e=this.$materialOptionsList.find("li").not(".disabled").not(".select-toggle-all").last(),i=this.$materialOptionsList.find("li.selected").length>0,n=i?this.$materialOptionsList.find("li.selected"):t,o=n.is(e)||!i?n:n.next("li:not(.disabled)");this._selectSingleOption(o),this.$materialOptionsList.find("li").removeClass("active"),o.toggleClass("active")}},{key:"_handleArrowUpKey",value:function(){var t=this.$materialOptionsList.find("li").not(".disabled").not(".select-toggle-all").first(),e=this.$materialOptionsList.find("li").not(".disabled").not(".select-toggle-all").last(),i=this.$materialOptionsList.find("li.selected").length>0,n=i?this.$materialOptionsList.find("li.selected"):e,o=n.is(t)||!i?n:n.prev("li:not(.disabled)");this._selectSingleOption(o),this.$materialOptionsList.find("li").removeClass("active"),o.toggleClass("active")}},{key:"_handleEscKey",value:function(e){t(e).trigger("close")}},{key:"_handleLetterKey",value:function(e){var i=this,n="",o=String.fromCharCode(e.which).toLowerCase(),a=Object.keys(this.keyCodes).map(function(t){return i.keyCodes[t]});if(o&&-1===a.indexOf(e.which)){n+=o;var r=this.$materialOptionsList.find("li").filter(function(){return-1!==t(this).text().toLowerCase().indexOf(n)}).first();this.isMultiple||this.$materialOptionsList.find("li").removeClass("active"),r.addClass("active"),this._selectSingleOption(r)}}},{key:"_onSearchInputKeyup",value:function(e){var i=t(e.target),n=i.closest("ul"),o=i.val();n.find("li span.filtrable").each(function(){var e=t(this);"string"==typeof this.outerHTML&&(this.textContent.toLowerCase().includes(o.toLowerCase())?e.show().parent().show():e.hide().parent().hide())})}},{key:"_isToggleAllPresent",value:function(){return this.$materialOptionsList.find(this.$toggleAll).length}},{key:"_updateToggleAllOption",value:function(){var t=this.$materialOptionsList.find("li").not(".select-toggle-all, .disabled").find("[type=checkbox]"),e=t.filter(":checked"),i=this.$toggleAll.find("[type=checkbox]").is(":checked");e.length!==t.length||i?e.length<t.length&&i&&this.$toggleAll.find("[type=checkbox]").prop("checked",!1):this.$toggleAll.find("[type=checkbox]").prop("checked",!0)}},{key:"_toggleSelectedValue",value:function(t){var e=this.valuesSelected.indexOf(t),i=-1!==e;return i?this.valuesSelected.splice(e,1):this.valuesSelected.push(t),this.$materialOptionsList.find("li:not(.optgroup):not(.select-toggle-all)").eq(t).toggleClass("active"),this.$nativeSelect.find("option").eq(t).prop("selected",!i),this._setValueToMaterialSelect(),!i}},{key:"_selectSingleOption",value:function(t){this.$materialOptionsList.find("li.selected").removeClass("selected"),this._selectOption(t)}},{key:"_selectOption",value:function(e){t(e).addClass("selected")}},{key:"_setValueToMaterialSelect",value:function(){for(var t="",e=this.valuesSelected.length,i=0;i<e;i++){var n=this.$nativeSelect.find("option").eq(this.valuesSelected[i]).text();t+=", ".concat(n)}0===(t=e>=5?"".concat(e," options selected"):t.substring(2)).length&&(t=this.$nativeSelect.find("option:disabled").eq(0).text()),this.$nativeSelect.siblings("input.select-dropdown").val(t)}},{key:"_randomUUID",value:function(){var t=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var i=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"===e?i:3&i|8).toString(16)})}}],[{key:"clearMutationObservers",value:function(){e.mutationObservers.forEach(function(t){t.disconnect(),t.customStatus="stopped"})}}]),e}();t.fn.materialSelect=function(e){t(this).not(".browser-default").not(".custom-select").each(function(){new i(t(this),e).init()})},t.fn.material_select=t.fn.materialSelect,e=t.fn.val,t.fn.val=function(t){if(!arguments.length)return e.call(this);if(!0!==this.data("stop-refresh")&&this.hasClass("mdb-select")&&this.hasClass("initialized")&&!this.hasClass("browser-default")&&!this.hasClass("custom-select")){i.clearMutationObservers(),this.materialSelect("destroy");var n=e.call(this,t);return this.materialSelect(),n}return e.call(this,t)}}(jQuery),jQuery("select").siblings("input.select-dropdown").on("mousedown",function(t){/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)&&(t.clientX>=t.target.clientWidth||t.clientY>=t.target.clientHeight)&&t.preventDefault()}),function(t){var e=0,i=function(){function i(n,o){_classCallCheck(this,i),this.defaults={topSpacing:e,zIndex:!1,stopper:"#footer",stickyClass:!1,startScrolling:"top",minWidth:!1},this.$element=n,this.options=this.assignOptions(o),this.$window=t(window),this.stopper=this.options.stopper,this.elementWidth=this.$element.outerWidth(),this.elementHeight=this.$element.outerHeight(!0),this.$placeholder=t('<div class="sticky-placeholder"></div>'),this.scrollTop=0,this.setPushPoint(),this.setStopperPosition(),this.bindEvents()}return _createClass(i,[{key:"assignOptions",value:function(e){return t.extend({},this.defaults,e)}},{key:"bindEvents",value:function(){this.$window.on("resize",this.handleResize.bind(this)),this.$window.on("scroll",this.init.bind(this))}},{key:"hasZIndex",value:function(){return"number"==typeof this.options.zIndex}},{key:"hasStopper",value:function(){return t(this.options.stopper).length||"number"==typeof this.options.stopper}},{key:"isScreenHeightEnough",value:function(){return this.$element.outerHeight()+this.options.topSpacing<this.$window.height()}},{key:"setStopperPosition",value:function(){"string"==typeof this.options.stopper?this.stopPoint=t(this.stopper).offset().top-this.options.topSpacing:"number"==typeof this.options.stopper&&(this.stopPoint=this.options.stopper)}},{key:"setPushPoint",value:function(){"bottom"!==this.options.startScrolling||this.isScreenHeightEnough()?"bottom"===this.options.startScrolling?this.$pushPoint=this.$element.offset().top+this.$element.outerHeight(!0)+this.options.topSpacing-this.$window.height():this.$pushPoint=this.$element.offset().top-this.options.topSpacing:this.$pushPoint=this.$element.offset().top+this.$element.outerHeight(!0)-this.$window.height()}},{key:"handleResize",value:function(){this.elementWidth=this.$element.outerWidth(),this.elementHeight=this.$element.outerHeight(!0),this.setPushPoint(),this.setStopperPosition(),this.init()}},{key:"init",value:function(){if(this.options.minWidth&&this.options.minWidth>this.$window.innerWidth())return!1;"bottom"!==this.options.startScrolling||this.isScreenHeightEnough()?this.scrollTop=this.$window.scrollTop():this.scrollTop=this.$window.scrollTop()+this.$window.height(),this.$pushPoint<this.scrollTop?(this.appendPlaceholder(),this.stickyStart()):this.stickyEnd(),this.$window.scrollTop()>this.$pushPoint?this.stop():this.stickyEnd()}},{key:"appendPlaceholder",value:function(){this.$element.after(this.$placeholder),this.$placeholder.css({width:this.elementWidth,height:this.elementHeight})}},{key:"stickyStart",value:function(){this.options.stickyClass&&this.$element.addClass(this.options.stickyClass),this.$element.get(0).style.overflow="scroll";var t=this.$element.get(0).scrollHeight;this.$element.get(0).style.overflow="",this.$element.css({position:"fixed",width:this.elementWidth,height:t}),"bottom"!==this.options.startScrolling||this.isScreenHeightEnough()?this.$element.css({top:this.options.topSpacing}):this.$element.css({bottom:0,top:""}),this.hasZIndex()&&this.$element.css({zIndex:this.options.zIndex})}},{key:"stickyEnd",value:function(){this.options.stickyClass&&this.$element.removeClass(this.options.stickyClass),this.$placeholder.remove(),this.$element.css({position:"static",top:e})}},{key:"stop",value:function(){this.stopPoint<t(this.$element).offset().top+this.$element.outerHeight(!0)&&this.$element.css({position:"absolute",bottom:0,top:""})}}]),i}();t.fn.sticky=function(e){return this.each(function(){var n=t(this);t(window).on("load",function(){new i(n,e).init()})})}}(jQuery),function t(e,i,n){function o(r,s){if(!i[r]){if(!e[r]){var l="function"==typeof require&&require;if(!s&&l)return l(r,!0);if(a)return a(r,!0);var c=new Error("Cannot find module '"+r+"'");throw c.code="MODULE_NOT_FOUND",c}var u=i[r]={exports:{}};e[r][0].call(u.exports,function(t){var i=e[r][1][t];return o(i||t)},u,u.exports,t,e,i,n)}return i[r].exports}for(var a="function"==typeof require&&require,r=0;r<n.length;r++)o(n[r]);return o}({1:[function(t,e,i){"use strict";var n=t("../main");"function"==typeof define&&define.amd?define(n):(window.PerfectScrollbar=n,void 0===window.Ps&&(window.Ps=n))},{"../main":7}],2:[function(t,e,i){"use strict";i.add=function(t,e){t.classList?t.classList.add(e):function(t,e){var i=t.className.split(" ");i.indexOf(e)<0&&i.push(e),t.className=i.join(" ")}(t,e)},i.remove=function(t,e){t.classList?t.classList.remove(e):function(t,e){var i=t.className.split(" "),n=i.indexOf(e);n>=0&&i.splice(n,1),t.className=i.join(" ")}(t,e)},i.list=function(t){return t.classList?Array.prototype.slice.apply(t.classList):t.className.split(" ")}},{}],3:[function(t,e,i){"use strict";var n={e:function(t,e){var i=document.createElement(t);return i.className=e,i},appendTo:function(t,e){return e.appendChild(t),t}};n.css=function(t,e,i){return"object"==typeof e?function(t,e){for(var i in e){var n=e[i];"number"==typeof n&&(n=n.toString()+"px"),t.style[i]=n}return t}(t,e):void 0===i?function(t,e){return window.getComputedStyle(t)[e]}(t,e):function(t,e,i){return"number"==typeof i&&(i=i.toString()+"px"),t.style[e]=i,t}(t,e,i)},n.matches=function(t,e){return void 0!==t.matches?t.matches(e):void 0!==t.matchesSelector?t.matchesSelector(e):void 0!==t.webkitMatchesSelector?t.webkitMatchesSelector(e):void 0!==t.mozMatchesSelector?t.mozMatchesSelector(e):void 0!==t.msMatchesSelector?t.msMatchesSelector(e):void 0},n.remove=function(t){void 0!==t.remove?t.remove():t.parentNode&&t.parentNode.removeChild(t)},n.queryChildren=function(t,e){return Array.prototype.filter.call(t.childNodes,function(t){return n.matches(t,e)})},e.exports=n},{}],4:[function(t,e,i){"use strict";var n=function(t){this.element=t,this.events={}};n.prototype.bind=function(t,e){void 0===this.events[t]&&(this.events[t]=[]),this.events[t].push(e),this.element.addEventListener(t,e,!1)},n.prototype.unbind=function(t,e){var i=void 0!==e;this.events[t]=this.events[t].filter(function(n){return!(!i||n===e)||(this.element.removeEventListener(t,n,!1),!1)},this)},n.prototype.unbindAll=function(){for(var t in this.events)this.unbind(t)};var o=function(){this.eventElements=[]};o.prototype.eventElement=function(t){var e=this.eventElements.filter(function(e){return e.element===t})[0];return void 0===e&&(e=new n(t),this.eventElements.push(e)),e},o.prototype.bind=function(t,e,i){this.eventElement(t).bind(e,i)},o.prototype.unbind=function(t,e,i){this.eventElement(t).unbind(e,i)},o.prototype.unbindAll=function(){for(var t=0;t<this.eventElements.length;t++)this.eventElements[t].unbindAll()},o.prototype.once=function(t,e,i){var n=this.eventElement(t),o=function(t){n.unbind(e,o),i(t)};n.bind(e,o)},e.exports=o},{}],5:[function(t,e,i){"use strict";e.exports=function(){function t(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}return function(){return t()+t()+"-"+t()+"-"+t()+"-"+t()+"-"+t()+t()+t()}}()},{}],6:[function(t,e,i){"use strict";function n(t){return function(e,i){t(e,"ps--in-scrolling"),void 0!==i?t(e,"ps--"+i):(t(e,"ps--x"),t(e,"ps--y"))}}var o=t("./class"),a=t("./dom"),r=i.toInt=function(t){return parseInt(t,10)||0},s=i.clone=function(t){if(t){if(Array.isArray(t))return t.map(s);if("object"==typeof t){var e={};for(var i in t)e[i]=s(t[i]);return e}return t}return null};i.extend=function(t,e){var i=s(t);for(var n in e)i[n]=s(e[n]);return i},i.isEditable=function(t){return a.matches(t,"input,[contenteditable]")||a.matches(t,"select,[contenteditable]")||a.matches(t,"textarea,[contenteditable]")||a.matches(t,"button,[contenteditable]")},i.removePsClasses=function(t){for(var e=o.list(t),i=0;i<e.length;i++){var n=e[i];0===n.indexOf("ps-")&&o.remove(t,n)}},i.outerWidth=function(t){return r(a.css(t,"width"))+r(a.css(t,"paddingLeft"))+r(a.css(t,"paddingRight"))+r(a.css(t,"borderLeftWidth"))+r(a.css(t,"borderRightWidth"))},i.startScrolling=n(o.add),i.stopScrolling=n(o.remove),i.env={isWebKit:"undefined"!=typeof document&&"WebkitAppearance"in document.documentElement.style,supportsTouch:"undefined"!=typeof window&&("ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch),supportsIePointer:"undefined"!=typeof window&&null!==window.navigator.msMaxTouchPoints}},{"./class":2,"./dom":3}],7:[function(t,e,i){"use strict";var n=t("./plugin/destroy"),o=t("./plugin/initialize"),a=t("./plugin/update");e.exports={initialize:o,update:a,destroy:n}},{"./plugin/destroy":9,"./plugin/initialize":17,"./plugin/update":21}],8:[function(t,e,i){"use strict";e.exports={handlers:["click-rail","drag-scrollbar","keyboard","wheel","touch"],maxScrollbarLength:null,minScrollbarLength:null,scrollXMarginOffset:0,scrollYMarginOffset:0,suppressScrollX:!1,suppressScrollY:!1,swipePropagation:!0,swipeEasing:!0,useBothWheelAxes:!1,wheelPropagation:!1,wheelSpeed:1,theme:"default"}},{}],9:[function(t,e,i){"use strict";var n=t("../lib/helper"),o=t("../lib/dom"),a=t("./instances");e.exports=function(t){var e=a.get(t);e&&(e.event.unbindAll(),o.remove(e.scrollbarX),o.remove(e.scrollbarY),o.remove(e.scrollbarXRail),o.remove(e.scrollbarYRail),n.removePsClasses(t),a.remove(t))}},{"../lib/dom":3,"../lib/helper":6,"./instances":18}],10:[function(t,e,i){"use strict";var n=t("../instances"),o=t("../update-geometry"),a=t("../update-scroll");e.exports=function(t){!function(t,e){function i(t){return t.getBoundingClientRect()}var n=function(t){t.stopPropagation()};e.event.bind(e.scrollbarY,"click",n),e.event.bind(e.scrollbarYRail,"click",function(n){var r=n.pageY-window.pageYOffset-i(e.scrollbarYRail).top>e.scrollbarYTop?1:-1;a(t,"top",t.scrollTop+r*e.containerHeight),o(t),n.stopPropagation()}),e.event.bind(e.scrollbarX,"click",n),e.event.bind(e.scrollbarXRail,"click",function(n){var r=n.pageX-window.pageXOffset-i(e.scrollbarXRail).left>e.scrollbarXLeft?1:-1;a(t,"left",t.scrollLeft+r*e.containerWidth),o(t),n.stopPropagation()})}(t,n.get(t))}},{"../instances":18,"../update-geometry":19,"../update-scroll":20}],11:[function(t,e,i){"use strict";var n=t("../../lib/helper"),o=t("../../lib/dom"),a=t("../instances"),r=t("../update-geometry"),s=t("../update-scroll");e.exports=function(t){var e=a.get(t);(function(t,e){function i(i){var o=a+i*e.railXRatio,r=Math.max(0,e.scrollbarXRail.getBoundingClientRect().left)+e.railXRatio*(e.railXWidth-e.scrollbarXWidth);e.scrollbarXLeft=o<0?0:o>r?r:o;var l=n.toInt(e.scrollbarXLeft*(e.contentWidth-e.containerWidth)/(e.containerWidth-e.railXRatio*e.scrollbarXWidth))-e.negativeScrollAdjustment;s(t,"left",l)}var a=null,l=null,c=function(e){i(e.pageX-l),r(t),e.stopPropagation(),e.preventDefault()},u=function(){n.stopScrolling(t,"x"),e.event.unbind(e.ownerDocument,"mousemove",c)};e.event.bind(e.scrollbarX,"mousedown",function(i){l=i.pageX,a=n.toInt(o.css(e.scrollbarX,"left"))*e.railXRatio,n.startScrolling(t,"x"),e.event.bind(e.ownerDocument,"mousemove",c),e.event.once(e.ownerDocument,"mouseup",u),i.stopPropagation(),i.preventDefault()})})(t,e),function(t,e){function i(i){var o=a+i*e.railYRatio,r=Math.max(0,e.scrollbarYRail.getBoundingClientRect().top)+e.railYRatio*(e.railYHeight-e.scrollbarYHeight);e.scrollbarYTop=o<0?0:o>r?r:o;var l=n.toInt(e.scrollbarYTop*(e.contentHeight-e.containerHeight)/(e.containerHeight-e.railYRatio*e.scrollbarYHeight));s(t,"top",l)}var a=null,l=null,c=function(e){i(e.pageY-l),r(t),e.stopPropagation(),e.preventDefault()},u=function(){n.stopScrolling(t,"y"),e.event.unbind(e.ownerDocument,"mousemove",c)};e.event.bind(e.scrollbarY,"mousedown",function(i){l=i.pageY,a=n.toInt(o.css(e.scrollbarY,"top"))*e.railYRatio,n.startScrolling(t,"y"),e.event.bind(e.ownerDocument,"mousemove",c),e.event.once(e.ownerDocument,"mouseup",u),i.stopPropagation(),i.preventDefault()})}(t,e)}},{"../../lib/dom":3,"../../lib/helper":6,"../instances":18,"../update-geometry":19,"../update-scroll":20}],12:[function(t,e,i){"use strict";function n(t,e){var i=!1;e.event.bind(t,"mouseenter",function(){i=!0}),e.event.bind(t,"mouseleave",function(){i=!1});e.event.bind(e.ownerDocument,"keydown",function(n){if(!(n.isDefaultPrevented&&n.isDefaultPrevented()||n.defaultPrevented)){var r=a.matches(e.scrollbarX,":focus")||a.matches(e.scrollbarY,":focus");if(i||r){var c=document.activeElement?document.activeElement:e.ownerDocument.activeElement;if(c){if("IFRAME"===c.tagName)c=c.contentDocument.activeElement;else for(;c.shadowRoot;)c=c.shadowRoot.activeElement;if(o.isEditable(c))return}var u=0,d=0;switch(n.which){case 37:u=n.metaKey?-e.contentWidth:n.altKey?-e.containerWidth:-30;break;case 38:d=n.metaKey?e.contentHeight:n.altKey?e.containerHeight:30;break;case 39:u=n.metaKey?e.contentWidth:n.altKey?e.containerWidth:30;break;case 40:d=n.metaKey?-e.contentHeight:n.altKey?-e.containerHeight:-30;break;case 33:d=90;break;case 32:d=n.shiftKey?90:-90;break;case 34:d=-90;break;case 35:d=n.ctrlKey?-e.contentHeight:-e.containerHeight;break;case 36:d=n.ctrlKey?t.scrollTop:e.containerHeight;break;default:return}l(t,"top",t.scrollTop-d),l(t,"left",t.scrollLeft+u),s(t),function(i,n){var o=t.scrollTop;if(0===i){if(!e.scrollbarYActive)return!1;if(0===o&&n>0||o>=e.contentHeight-e.containerHeight&&n<0)return!e.settings.wheelPropagation}var a=t.scrollLeft;if(0===n){if(!e.scrollbarXActive)return!1;if(0===a&&i<0||a>=e.contentWidth-e.containerWidth&&i>0)return!e.settings.wheelPropagation}return!0}(u,d)&&n.preventDefault()}}})}var o=t("../../lib/helper"),a=t("../../lib/dom"),r=t("../instances"),s=t("../update-geometry"),l=t("../update-scroll");e.exports=function(t){n(t,r.get(t))}},{"../../lib/dom":3,"../../lib/helper":6,"../instances":18,"../update-geometry":19,"../update-scroll":20}],13:[function(t,e,i){"use strict";function n(t,e){function i(i){var o=function(t){var e=t.deltaX,i=-1*t.deltaY;return void 0!==e&&void 0!==i||(e=-1*t.wheelDeltaX/6,i=t.wheelDeltaY/6),t.deltaMode&&1===t.deltaMode&&(e*=10,i*=10),e!=e&&i!=i&&(e=0,i=t.wheelDelta),t.shiftKey?[-i,-e]:[e,i]}(i),s=o[0],l=o[1];(function(e,i){var n=t.querySelector("textarea:hover, select[multiple]:hover, .ps-child:hover");if(n){var o=window.getComputedStyle(n);if(![o.overflow,o.overflowX,o.overflowY].join("").match(/(scroll|auto)/))return!1;var a=n.scrollHeight-n.clientHeight;if(a>0&&!(0===n.scrollTop&&i>0||n.scrollTop===a&&i<0))return!0;var r=n.scrollLeft-n.clientWidth;if(r>0&&!(0===n.scrollLeft&&e<0||n.scrollLeft===r&&e>0))return!0}return!1})(s,l)||(n=!1,e.settings.useBothWheelAxes?e.scrollbarYActive&&!e.scrollbarXActive?(r(t,"top",l?t.scrollTop-l*e.settings.wheelSpeed:t.scrollTop+s*e.settings.wheelSpeed),n=!0):e.scrollbarXActive&&!e.scrollbarYActive&&(r(t,"left",s?t.scrollLeft+s*e.settings.wheelSpeed:t.scrollLeft-l*e.settings.wheelSpeed),n=!0):(r(t,"top",t.scrollTop-l*e.settings.wheelSpeed),r(t,"left",t.scrollLeft+s*e.settings.wheelSpeed)),a(t),(n=n||function(i,n){var o=t.scrollTop;if(0===i){if(!e.scrollbarYActive)return!1;if(0===o&&n>0||o>=e.contentHeight-e.containerHeight&&n<0)return!e.settings.wheelPropagation}var a=t.scrollLeft;if(0===n){if(!e.scrollbarXActive)return!1;if(0===a&&i<0||a>=e.contentWidth-e.containerWidth&&i>0)return!e.settings.wheelPropagation}return!0}(s,l))&&(i.stopPropagation(),i.preventDefault()))}var n=!1;void 0!==window.onwheel?e.event.bind(t,"wheel",i):void 0!==window.onmousewheel&&e.event.bind(t,"mousewheel",i)}var o=t("../instances"),a=t("../update-geometry"),r=t("../update-scroll");e.exports=function(t){n(t,o.get(t))}},{"../instances":18,"../update-geometry":19,"../update-scroll":20}],14:[function(t,e,i){"use strict";var n=t("../instances"),o=t("../update-geometry");e.exports=function(t){!function(t,e){e.event.bind(t,"scroll",function(){o(t)})}(t,n.get(t))}},{"../instances":18,"../update-geometry":19}],15:[function(t,e,i){"use strict";function n(t,e){function i(){l||(l=setInterval(function(){return a.get(t)?(s(t,"top",t.scrollTop+c.top),s(t,"left",t.scrollLeft+c.left),void r(t)):void clearInterval(l)},50))}function n(){l&&(clearInterval(l),l=null),o.stopScrolling(t)}var l=null,c={top:0,left:0},u=!1;e.event.bind(e.ownerDocument,"selectionchange",function(){t.contains(function(){var t=window.getSelection?window.getSelection():document.getSelection?document.getSelection():"";return 0===t.toString().length?null:t.getRangeAt(0).commonAncestorContainer}())?u=!0:(u=!1,n())}),e.event.bind(window,"mouseup",function(){u&&(u=!1,n())}),e.event.bind(window,"keyup",function(){u&&(u=!1,n())}),e.event.bind(window,"mousemove",function(e){if(u){var a={x:e.pageX,y:e.pageY},r={left:t.offsetLeft,right:t.offsetLeft+t.offsetWidth,top:t.offsetTop,bottom:t.offsetTop+t.offsetHeight};a.x<r.left+3?(c.left=-5,o.startScrolling(t,"x")):a.x>r.right-3?(c.left=5,o.startScrolling(t,"x")):c.left=0,a.y<r.top+3?(c.top=r.top+3-a.y<5?-5:-20,o.startScrolling(t,"y")):a.y>r.bottom-3?(c.top=a.y-r.bottom+3<5?5:20,o.startScrolling(t,"y")):c.top=0,0===c.top&&0===c.left?n():i()}})}var o=t("../../lib/helper"),a=t("../instances"),r=t("../update-geometry"),s=t("../update-scroll");e.exports=function(t){n(t,a.get(t))}},{"../../lib/helper":6,"../instances":18,"../update-geometry":19,"../update-scroll":20}],16:[function(t,e,i){"use strict";var n=t("../../lib/helper"),o=t("../instances"),a=t("../update-geometry"),r=t("../update-scroll");e.exports=function(t){(n.env.supportsTouch||n.env.supportsIePointer)&&function(t,e,i,n){function s(i,n){var o=t.scrollTop,a=t.scrollLeft,r=Math.abs(i),s=Math.abs(n);if(s>r){if(n<0&&o===e.contentHeight-e.containerHeight||n>0&&0===o)return!e.settings.swipePropagation}else if(r>s&&(i<0&&a===e.contentWidth-e.containerWidth||i>0&&0===a))return!e.settings.swipePropagation;return!0}function l(e,i){r(t,"top",t.scrollTop-i),r(t,"left",t.scrollLeft-e),a(t)}function c(){w=!0}function u(){w=!1}function d(t){return t.targetTouches?t.targetTouches[0]:t}function h(t){return!(!t.targetTouches||1!==t.targetTouches.length)||!(!t.pointerType||"mouse"===t.pointerType||t.pointerType===t.MSPOINTER_TYPE_MOUSE)}function p(t){if(h(t)){S=!0;var e=d(t);g.pageX=e.pageX,g.pageY=e.pageY,m=(new Date).getTime(),null!==y&&clearInterval(y),t.stopPropagation()}}function f(t){if(!S&&e.settings.swipePropagation&&p(t),!w&&S&&h(t)){var i=d(t),n={pageX:i.pageX,pageY:i.pageY},o=n.pageX-g.pageX,a=n.pageY-g.pageY;l(o,a),g=n;var r=(new Date).getTime(),c=r-m;c>0&&(b.x=o/c,b.y=a/c,m=r),s(o,a)&&(t.stopPropagation(),t.preventDefault())}}function v(){!w&&S&&(S=!1,e.settings.swipeEasing&&(clearInterval(y),y=setInterval(function(){return o.get(t)&&(b.x||b.y)?Math.abs(b.x)<.01&&Math.abs(b.y)<.01?void clearInterval(y):(l(30*b.x,30*b.y),b.x*=.8,void(b.y*=.8)):void clearInterval(y)},10)))}var g={},m=0,b={},y=null,w=!1,S=!1;i?(e.event.bind(window,"touchstart",c),e.event.bind(window,"touchend",u),e.event.bind(t,"touchstart",p),e.event.bind(t,"touchmove",f),e.event.bind(t,"touchend",v)):n&&(window.PointerEvent?(e.event.bind(window,"pointerdown",c),e.event.bind(window,"pointerup",u),e.event.bind(t,"pointerdown",p),e.event.bind(t,"pointermove",f),e.event.bind(t,"pointerup",v)):window.MSPointerEvent&&(e.event.bind(window,"MSPointerDown",c),e.event.bind(window,"MSPointerUp",u),e.event.bind(t,"MSPointerDown",p),e.event.bind(t,"MSPointerMove",f),e.event.bind(t,"MSPointerUp",v)))}(t,o.get(t),n.env.supportsTouch,n.env.supportsIePointer)}},{"../../lib/helper":6,"../instances":18,"../update-geometry":19,"../update-scroll":20}],17:[function(t,e,i){"use strict";var n=t("../lib/helper"),o=t("../lib/class"),a=t("./instances"),r=t("./update-geometry"),s={"click-rail":t("./handler/click-rail"),"drag-scrollbar":t("./handler/drag-scrollbar"),keyboard:t("./handler/keyboard"),wheel:t("./handler/mouse-wheel"),touch:t("./handler/touch"),selection:t("./handler/selection")},l=t("./handler/native-scroll");e.exports=function(t,e){e="object"==typeof e?e:{},o.add(t,"ps");var i=a.add(t);i.settings=n.extend(i.settings,e),o.add(t,"ps--theme_"+i.settings.theme),i.settings.handlers.forEach(function(e){s[e](t)}),l(t),r(t)}},{"../lib/class":2,"../lib/helper":6,"./handler/click-rail":10,"./handler/drag-scrollbar":11,"./handler/keyboard":12,"./handler/mouse-wheel":13,"./handler/native-scroll":14,"./handler/selection":15,"./handler/touch":16,"./instances":18,"./update-geometry":19}],18:[function(t,e,i){"use strict";function n(t){return t.getAttribute("data-ps-id")}var o=t("../lib/helper"),a=t("../lib/class"),r=t("./default-setting"),s=t("../lib/dom"),l=t("../lib/event-manager"),c=t("../lib/guid"),u={};i.add=function(t){var e=c();return function(t,e){t.setAttribute("data-ps-id",e)}(t,e),u[e]=new function(t){function e(){a.add(t,"ps--focus")}function i(){a.remove(t,"ps--focus")}var n=this;n.settings=o.clone(r),n.containerWidth=null,n.containerHeight=null,n.contentWidth=null,n.contentHeight=null,n.isRtl="rtl"===s.css(t,"direction"),n.isNegativeScroll=function(){var e,i=t.scrollLeft;return t.scrollLeft=-1,e=t.scrollLeft<0,t.scrollLeft=i,e}(),n.negativeScrollAdjustment=n.isNegativeScroll?t.scrollWidth-t.clientWidth:0,n.event=new l,n.ownerDocument=t.ownerDocument||document,n.scrollbarXRail=s.appendTo(s.e("div","ps__scrollbar-x-rail"),t),n.scrollbarX=s.appendTo(s.e("div","ps__scrollbar-x"),n.scrollbarXRail),n.scrollbarX.setAttribute("tabindex",0),n.event.bind(n.scrollbarX,"focus",e),n.event.bind(n.scrollbarX,"blur",i),n.scrollbarXActive=null,n.scrollbarXWidth=null,n.scrollbarXLeft=null,n.scrollbarXBottom=o.toInt(s.css(n.scrollbarXRail,"bottom")),n.isScrollbarXUsingBottom=n.scrollbarXBottom==n.scrollbarXBottom,n.scrollbarXTop=n.isScrollbarXUsingBottom?null:o.toInt(s.css(n.scrollbarXRail,"top")),n.railBorderXWidth=o.toInt(s.css(n.scrollbarXRail,"borderLeftWidth"))+o.toInt(s.css(n.scrollbarXRail,"borderRightWidth")),s.css(n.scrollbarXRail,"display","block"),n.railXMarginWidth=o.toInt(s.css(n.scrollbarXRail,"marginLeft"))+o.toInt(s.css(n.scrollbarXRail,"marginRight")),s.css(n.scrollbarXRail,"display",""),n.railXWidth=null,n.railXRatio=null,n.scrollbarYRail=s.appendTo(s.e("div","ps__scrollbar-y-rail"),t),n.scrollbarY=s.appendTo(s.e("div","ps__scrollbar-y"),n.scrollbarYRail),n.scrollbarY.setAttribute("tabindex",0),n.event.bind(n.scrollbarY,"focus",e),n.event.bind(n.scrollbarY,"blur",i),n.scrollbarYActive=null,n.scrollbarYHeight=null,n.scrollbarYTop=null,n.scrollbarYRight=o.toInt(s.css(n.scrollbarYRail,"right")),n.isScrollbarYUsingRight=n.scrollbarYRight==n.scrollbarYRight,n.scrollbarYLeft=n.isScrollbarYUsingRight?null:o.toInt(s.css(n.scrollbarYRail,"left")),n.scrollbarYOuterWidth=n.isRtl?o.outerWidth(n.scrollbarY):null,n.railBorderYWidth=o.toInt(s.css(n.scrollbarYRail,"borderTopWidth"))+o.toInt(s.css(n.scrollbarYRail,"borderBottomWidth")),s.css(n.scrollbarYRail,"display","block"),n.railYMarginHeight=o.toInt(s.css(n.scrollbarYRail,"marginTop"))+o.toInt(s.css(n.scrollbarYRail,"marginBottom")),s.css(n.scrollbarYRail,"display",""),n.railYHeight=null,n.railYRatio=null}(t),u[e]},i.remove=function(t){delete u[n(t)],function(t){t.removeAttribute("data-ps-id")}(t)},i.get=function(t){return u[n(t)]}},{"../lib/class":2,"../lib/dom":3,"../lib/event-manager":4,"../lib/guid":5,"../lib/helper":6,"./default-setting":8}],19:[function(t,e,i){"use strict";function n(t,e){return t.settings.minScrollbarLength&&(e=Math.max(e,t.settings.minScrollbarLength)),t.settings.maxScrollbarLength&&(e=Math.min(e,t.settings.maxScrollbarLength)),e}var o=t("../lib/helper"),a=t("../lib/class"),r=t("../lib/dom"),s=t("./instances"),l=t("./update-scroll");e.exports=function(t){var e,i=s.get(t);i.containerWidth=t.clientWidth,i.containerHeight=t.clientHeight,i.contentWidth=t.scrollWidth,i.contentHeight=t.scrollHeight,t.contains(i.scrollbarXRail)||((e=r.queryChildren(t,".ps__scrollbar-x-rail")).length>0&&e.forEach(function(t){r.remove(t)}),r.appendTo(i.scrollbarXRail,t)),t.contains(i.scrollbarYRail)||((e=r.queryChildren(t,".ps__scrollbar-y-rail")).length>0&&e.forEach(function(t){r.remove(t)}),r.appendTo(i.scrollbarYRail,t)),!i.settings.suppressScrollX&&i.containerWidth+i.settings.scrollXMarginOffset<i.contentWidth?(i.scrollbarXActive=!0,i.railXWidth=i.containerWidth-i.railXMarginWidth,i.railXRatio=i.containerWidth/i.railXWidth,i.scrollbarXWidth=n(i,o.toInt(i.railXWidth*i.containerWidth/i.contentWidth)),i.scrollbarXLeft=o.toInt((i.negativeScrollAdjustment+t.scrollLeft)*(i.railXWidth-i.scrollbarXWidth)/(i.contentWidth-i.containerWidth))):i.scrollbarXActive=!1,!i.settings.suppressScrollY&&i.containerHeight+i.settings.scrollYMarginOffset<i.contentHeight?(i.scrollbarYActive=!0,i.railYHeight=i.containerHeight-i.railYMarginHeight,i.railYRatio=i.containerHeight/i.railYHeight,i.scrollbarYHeight=n(i,o.toInt(i.railYHeight*i.containerHeight/i.contentHeight)),i.scrollbarYTop=o.toInt(t.scrollTop*(i.railYHeight-i.scrollbarYHeight)/(i.contentHeight-i.containerHeight))):i.scrollbarYActive=!1,i.scrollbarXLeft>=i.railXWidth-i.scrollbarXWidth&&(i.scrollbarXLeft=i.railXWidth-i.scrollbarXWidth),i.scrollbarYTop>=i.railYHeight-i.scrollbarYHeight&&(i.scrollbarYTop=i.railYHeight-i.scrollbarYHeight),function(t,e){var i={width:e.railXWidth};e.isRtl?i.left=e.negativeScrollAdjustment+t.scrollLeft+e.containerWidth-e.contentWidth:i.left=t.scrollLeft,e.isScrollbarXUsingBottom?i.bottom=e.scrollbarXBottom-t.scrollTop:i.top=e.scrollbarXTop+t.scrollTop,r.css(e.scrollbarXRail,i);var n={top:t.scrollTop,height:e.railYHeight};e.isScrollbarYUsingRight?e.isRtl?n.right=e.contentWidth-(e.negativeScrollAdjustment+t.scrollLeft)-e.scrollbarYRight-e.scrollbarYOuterWidth:n.right=e.scrollbarYRight-t.scrollLeft:e.isRtl?n.left=e.negativeScrollAdjustment+t.scrollLeft+2*e.containerWidth-e.contentWidth-e.scrollbarYLeft-e.scrollbarYOuterWidth:n.left=e.scrollbarYLeft+t.scrollLeft,r.css(e.scrollbarYRail,n),r.css(e.scrollbarX,{left:e.scrollbarXLeft,width:e.scrollbarXWidth-e.railBorderXWidth}),r.css(e.scrollbarY,{top:e.scrollbarYTop,height:e.scrollbarYHeight-e.railBorderYWidth})}(t,i),i.scrollbarXActive?a.add(t,"ps--active-x"):(a.remove(t,"ps--active-x"),i.scrollbarXWidth=0,i.scrollbarXLeft=0,l(t,"left",0)),i.scrollbarYActive?a.add(t,"ps--active-y"):(a.remove(t,"ps--active-y"),i.scrollbarYHeight=0,i.scrollbarYTop=0,l(t,"top",0))}},{"../lib/class":2,"../lib/dom":3,"../lib/helper":6,"./instances":18,"./update-scroll":20}],20:[function(t,e,i){"use strict";var n=t("./instances"),o=function(t){var e=document.createEvent("Event");return e.initEvent(t,!0,!0),e};e.exports=function(t,e,i){if(void 0===t)throw"You must provide an element to the update-scroll function";if(void 0===e)throw"You must provide an axis to the update-scroll function";if(void 0===i)throw"You must provide a value to the update-scroll function";"top"===e&&i<=0&&(t.scrollTop=i=0,t.dispatchEvent(o("ps-y-reach-start"))),"left"===e&&i<=0&&(t.scrollLeft=i=0,t.dispatchEvent(o("ps-x-reach-start")));var a=n.get(t);"top"===e&&i>=a.contentHeight-a.containerHeight&&((i=a.contentHeight-a.containerHeight)-t.scrollTop<=1?i=t.scrollTop:t.scrollTop=i,t.dispatchEvent(o("ps-y-reach-end"))),"left"===e&&i>=a.contentWidth-a.containerWidth&&((i=a.contentWidth-a.containerWidth)-t.scrollLeft<=1?i=t.scrollLeft:t.scrollLeft=i,t.dispatchEvent(o("ps-x-reach-end"))),void 0===a.lastTop&&(a.lastTop=t.scrollTop),void 0===a.lastLeft&&(a.lastLeft=t.scrollLeft),"top"===e&&i<a.lastTop&&t.dispatchEvent(o("ps-scroll-up")),"top"===e&&i>a.lastTop&&t.dispatchEvent(o("ps-scroll-down")),"left"===e&&i<a.lastLeft&&t.dispatchEvent(o("ps-scroll-left")),"left"===e&&i>a.lastLeft&&t.dispatchEvent(o("ps-scroll-right")),"top"===e&&i!==a.lastTop&&(t.scrollTop=a.lastTop=i,t.dispatchEvent(o("ps-scroll-y"))),"left"===e&&i!==a.lastLeft&&(t.scrollLeft=a.lastLeft=i,t.dispatchEvent(o("ps-scroll-x")))}},{"./instances":18}],21:[function(t,e,i){"use strict";var n=t("../lib/helper"),o=t("../lib/dom"),a=t("./instances"),r=t("./update-geometry"),s=t("./update-scroll");e.exports=function(t){var e=a.get(t);e&&(e.negativeScrollAdjustment=e.isNegativeScroll?t.scrollWidth-t.clientWidth:0,o.css(e.scrollbarXRail,"display","block"),o.css(e.scrollbarYRail,"display","block"),e.railXMarginWidth=n.toInt(o.css(e.scrollbarXRail,"marginLeft"))+n.toInt(o.css(e.scrollbarXRail,"marginRight")),e.railYMarginHeight=n.toInt(o.css(e.scrollbarYRail,"marginTop"))+n.toInt(o.css(e.scrollbarYRail,"marginBottom")),o.css(e.scrollbarXRail,"display","none"),o.css(e.scrollbarYRail,"display","none"),r(t),s(t,"top",t.scrollTop),s(t,"left",t.scrollLeft),o.css(e.scrollbarXRail,"display",""),o.css(e.scrollbarYRail,"display",""))}},{"../lib/dom":3,"../lib/helper":6,"./instances":18,"./update-geometry":19,"./update-scroll":20}]},{},[1]),$.fn.mdb_autocomplete=function(t){return t=$.extend({data:{}},t),this.each(function(){var e,i=$(this),n=t.data;Object.keys(n).length&&(e=$('<ul class="mdb-autocomplete-wrap"></ul>')).insertAfter($(this)),i.on("keyup",function(t){var o=i.val();if(e.empty(),o.length)for(var a in n)if(-1!==n[a].toLowerCase().indexOf(o.toLowerCase())){var r=$("<li>".concat(n[a],"</li>"));e.append(r)}13===t.which&&(e.children(":first").trigger("click"),e.empty()),0===o.length?$(".mdb-autocomplete-clear").css("visibility","hidden"):$(".mdb-autocomplete-clear").css("visibility","visible")}),e.on("click","li",function(){i.val($(this).text()),e.empty()}),$(".mdb-autocomplete-clear").on("click",function(t){t.preventDefault(),i.val(""),$(this).css("visibility","hidden"),e.empty(),$(this).parent().find("label").removeClass("active")})})},$("body").on("shown.bs.modal",".modal",function(){$(".modal-backdrop").length||($modal_dialog=$(this).children(".modal-dialog"),$modal_dialog.hasClass("modal-side")&&($(this).addClass("modal-scrolling"),$("body").addClass("scrollable")),$modal_dialog.hasClass("modal-frame")&&($(this).addClass("modal-content-clickable"),$("body").addClass("scrollable")))}),$("body").on("hidden.bs.modal",".modal",function(){$("body").removeClass("scrollable")});