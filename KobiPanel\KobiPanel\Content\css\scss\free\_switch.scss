// Switch free
.bs-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
  input {
    display: none;
    &:checked {
      + .slider {
        background-color: #2196F3;
        &:before {
          transform: translateX(26px);
        }
      }
    }
    &:focus {
      + .slider {
        box-shadow: 0 0 1px #2196F3;
      }
    }
  }
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
    &:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      -webkit-transition: .4s;
      transition: .4s;
    }
    &.round {
      border-radius: 34px;
      &:before {
        border-radius: 50%;
      }
    }
  }
}
