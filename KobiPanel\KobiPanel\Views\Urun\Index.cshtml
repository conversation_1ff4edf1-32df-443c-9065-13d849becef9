﻿@model IEnumerable<KobiPanel.Models.Urunler>

@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>Index</h2>

<p>
    @Html.ActionLink("Create New", "Create")
</p>
<table class="table">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.UrunAdi)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Adet)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.AlisFiyat)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Kdv)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.SatisFiyat)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.EklenmeTarihi)
        </th>
        <th></th>
    </tr>

@foreach (var item in Model) {
    <tr>
        <td>
            @Html.DisplayFor(modelItem => item.UrunAdi)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.Adet)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.AlisFiyat)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.Kdv)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.SatisFiyat)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.EklenmeTarihi)
        </td>
        <td>
            @Html.ActionLink("Edit", "Edit", new { id=item.ID }) |
            @Html.ActionLink("Details", "Details", new { id=item.ID }) |
            @Html.ActionLink("Delete", "Delete", new { id=item.ID })
        </td>
    </tr>
}

</table>
