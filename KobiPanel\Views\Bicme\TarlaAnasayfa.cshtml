﻿@using GridMvc.Html
@model IEnumerable<KobiPanel.Models.Bicme.BicilenTarlalar>
<link href="~/Content/Gridmvc.css" rel="stylesheet" />

<link href="~/Content/gridmvc.datepicker.min.css" rel="stylesheet" />
<link href="~/Content/PagedList.css" rel="stylesheet" />
@{
    ViewBag.Pageheader = "Biçme Kayıtları";
    ViewBag.Title = "Biçme Kayıtları";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@Html.ActionLink("Tarla <PERSON>d<PERSON>", "TarlaEkle", null, new { @class = "btn btn-primary" })
<div>
    @Html.Grid(Model).Columns(col =>
{
    col.Add(x => x.Musteri.adsoyad).RenderValueAs(x => x.Musteri.adsoyad + "(" + x.Musteri<PERSON> + ")").Titled("Ad Soyad").Filterable(true).Sortable(true);
    col.Add(x => x.Donum).Titled("Dönüm").Filterable(true);
    col.Add(x => x.BicmeFiyati).Titled("Biçme Fiyatı");
    col.Add(x => x.BicimTarihi).Titled("Biçme Tarihi").Sortable(true).Filterable(true);
    col.Add(x => x.ToplamTutar).Titled("Toplam Tutar").Sortable(true).Filterable(true);
    col.Add(x => x.KalanTutar).Titled("Kalan Tutar").Sortable(true);
    col.Add(x => x.TahsilatTutari).Titled("Tahsil Edilen").Sortable(true);
    col.Add().Encoded(false).Sanitized(false).RenderValueAs(html =>
    {
        string htmlStr = "<a href='/Bicme/KantarFisiEkle/" + html.TarlaID.ToString()+ "' class='btn btn-success'><span class='glyphicon glyphicon-remove'></span> Kantar Fişi Ekle</a>";
        return MvcHtmlString.Create(htmlStr);

    });


}).WithPaging(12).SetLanguage("tr")
</div>