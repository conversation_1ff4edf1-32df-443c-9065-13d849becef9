﻿@model KobiPanel.Models.Urun

@{
    ViewBag.Title = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>Details</h2>

<div>
    <h4>Urun</h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.UrunAdi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.UrunAdi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.UrunFiyati)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.UrunFiyati)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.MevcutStok)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.MevcutStok)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.EklenmeTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.EklenmeTarihi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.DuzenlenmeTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.DuzenlenmeTarihi)
        </dd>

    </dl>
</div>
<p>
    @Html.ActionLink("Düzenle", "UrunDuzenle", new { id = Model.UrunID }) |
    @Html.ActionLink("Listeye Geri Dön", "UrunAnasayfa")
</p>
