﻿@model IEnumerable<KobiPanel.Models.Urun>

@{
    ViewBag.Title = "<PERSON><PERSON><PERSON><PERSON><PERSON>";
    ViewBag.PageHeader = "<PERSON>rün<PERSON>";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<p>
    @Html.ActionLink("Ye<PERSON>", "Urun<PERSON>kle",null, new {@class="btn btn-primary" })
</p>
<table id="example" class="table table-striped table-bordered dt-responsive" style="width:100%">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.UrunAdi)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.UrunFiyati)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.MevcutStok)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.NakliyeUcreti)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.EklenmeTarihi)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.DuzenlenmeTarihi)
        </th>
        <th></th>
    </tr>

    @foreach (var item in Model)
    {
    <tr>
        <td>
            @Html.DisplayFor(modelItem => item.UrunAdi)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.UrunFiyati)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.MevcutStok)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.NakliyeUcreti)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.EklenmeTarihi)
        </td>
        <td>
            @Html.DisplayFor(modelItem => item.DuzenlenmeTarihi)
        </td>
        <td>
            @Html.ActionLink("Düzenle", "UrunDuzenle", new { id = item.UrunID }) |
            @Html.ActionLink("Sil", "UrunSil", new { id = item.UrunID })
        </td>
    </tr>
    }

</table>
