// Globals
// Full palette of colors
@each $color_name, $color in $mdb-colors-1 {
  @each $color_type, $color_value in $color {
    @if $color_type == "base" {
      .#{$color_name} {
        background-color: $color_value !important;
      }
      .#{$color_name}-text {
        color: $color-value !important;
      }
      .rgba-#{$color_name}-slight,
      .rgba-#{$color_name}-slight:after {
        background-color: rgba($color_value, .1);
      }
      .rgba-#{$color_name}-light,
      .rgba-#{$color_name}-light:after {
        background-color: rgba($color_value, .3);
      }
      .rgba-#{$color_name}-strong,
      .rgba-#{$color_name}-strong:after {
        background-color: rgba($color_value, .7);
      }
    }
    @else {
      @if $enable_full_palette {
        .#{$color_name}.#{$color_type} {
          background-color: $color_value !important;
        }
      }
    }
  }
}

// Stylish color
@each $color_name, $color_value in $stylish-rgba {
  .#{$color_name} {
    background-color: $color_value;
  }
}

// Material colors palette
@each $color_name, $color in $material-colors {
  .#{$color_name} {
    background-color: $color !important;
  }
}

// Basic gradients
@each $name, $val in $gradients {
  @include make-gradient($name, $val);
}
@each $name, $val in $gradients-rgba {
  @include make-gradient-rgba($name, $val);
}

.dark-grey-text {
  color: #4f4f4f !important;
  &:hover,
  &:focus {
    color: #4f4f4f !important;
  }
}

// Shadow on hover
.hoverable {
  box-shadow: none;
  transition: $transition-hoverable;
  &:hover {
    box-shadow: $z-depth-2;
    transition: $transition-hoverable;
  }
}

// Shadows
.z-depth-0 {
  box-shadow: none !important;
}
.z-depth-1 {
  box-shadow: $z-depth-1 !important;
}
.z-depth-1-half {
  box-shadow: $z-depth-1-half !important;
}
.z-depth-2 {
  box-shadow: $z-depth-2 !important;
}
.z-depth-3 {
  box-shadow: $z-depth-3 !important;
}
.z-depth-4 {
  box-shadow: $z-depth-4 !important;
}
.z-depth-5 {
  box-shadow: $z-depth-5 !important;
}

// Disabled cursor
.disabled,
:disabled {
  pointer-events: none !important;
}

// Links
a {
  cursor: pointer;
  text-decoration: none;
  color: $link-color;
  transition: $transition-basic;
  &:hover {
    text-decoration: none;
    color: $link-hover-color;
    transition: $transition-basic;
  }
  &.disabled,
  &:disabled {
    &:hover {
      color: $link-color;
    }
  }
}

a:not([href]):not([tabindex]), a:not([href]):not([tabindex]):focus, a:not([href]):not([tabindex]):hover {
  color: inherit;
  text-decoration: none;
}
