﻿@model IEnumerable<KobiPanel.Models.Vehicles.Araclar>

@{
    ViewBag.Title = "Araçlar";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@if (TempData["AracEkleBasarili"] != null)
{
    <div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>@TempData["AracEkleBasarili"]</strong>
    </div>
}
@if (TempData["AracEkleBasarili"] != null)
{
    <div class="alert alert-danger alert-dismissible">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>@TempData["AracEkleBasarisiz"]</strong>
    </div>
}

<p>
    @Html.ActionLink("Yeni Arac Ekle", "AracEkle",null,new {@class="btn btn-primary" })
</p>
<table id="example" class="table table-striped table-bordered dt-responsive" style="width:100%">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.Plaka)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.TescilNo)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.VizeBitisTarihi)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.SigortaBitisTarihi)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.EgzozBitisTarihi)
        </th>
        <th></th>
    </tr>

    @foreach (var item in Model)
    {
<tr>
    <td>
        @Html.DisplayFor(modelItem => item.Plaka)
    </td>
    <td>
        @Html.DisplayFor(modelItem => item.TescilNo)
    </td>
    <td>
        @Html.DisplayFor(modelItem => item.VizeBitisTarihi)
    </td>
    <td>
        @Html.DisplayFor(modelItem => item.SigortaBitisTarihi)
    </td>
    <td>
        @Html.DisplayFor(modelItem => item.EgzozBitisTarihi)
    </td>
    <td>
        @Html.ActionLink("Sil", "AracSil", new { id = item.ID })
    </td>
</tr>
    }

</table>
