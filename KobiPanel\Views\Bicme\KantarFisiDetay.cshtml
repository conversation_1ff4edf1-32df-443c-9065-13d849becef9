﻿@model KobiPanel.Models.Bicme.KantarFisi

@{
    ViewBag.Title = "Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>Details</h2>

<div>
    <h4>KantarFisi</h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.BicilenTarlalar.aciklama)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.BicilenTarlalar.aciklama)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.birinciTartim)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.birinciTartim)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ikinciTartim)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ikinciTartim)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.NetKg)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.NetKg)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Plaka)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Plaka)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.aciklama)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.aciklama)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.TartimTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.TartimTarihi)
        </dd>

    </dl>
</div>
<p>
    @Html.ActionLink("Edit", "Edit", new { id = Model.FisID }) |
    @Html.ActionLink("Back to List", "Index")
</p>
