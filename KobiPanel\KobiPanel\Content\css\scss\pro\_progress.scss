// Progress
.md-progress {
  box-shadow: none;
  position: relative;
  display: block;
  width: 100%;
  height: $progress-height-mdb;
  overflow: hidden;
  margin-bottom: $progress-margin-bottom;
  background-color: $grey-lighten-3;
  .progress-bar {
    box-shadow: none;
    height: $progress-bar-height;
    border-radius: 0;
    background-color: $primary-color-dark;
  }
  .progress-bar-animated {
    transition: $progress-bar-animated-transition;
  }
  .indeterminate {
    background-color: $blue-lighten-3;
    &:before {
      content: "";
      position: absolute;
      background-color: inherit;
      top: 0;
      left: 0;
      bottom: 0;
      will-change: left, right;
      // Custom bezier
      animation: $progress-indeterminate-before-animation;
    }
    &:after {
      content: "";
      position: absolute;
      background-color: inherit;
      top: 0;
      left: 0;
      bottom: 0;
      will-change: left, right;
      // Custom bezier
      animation: $progress-indeterminate-after-animation;
      animation-delay: $progress-indeterminate-after-animation-delay;
    }
  }
  @include keyframes(indeterminate) {
    0% {
      left: -35%;
      right: 100%;
    }
    60% {
      left: 100%;
      right: -90%;
    }
    100% {
      left: 100%;
      right: -90%;
    }
  }
  @include keyframes(indeterminate-short) {
    0% {
      left: -200%;
      right: 100%;
    }
    60% {
      left: 107%;
      right: -8%;
    }
    100% {
      left: 107%;
      right: -8%;
    }
  }
}


/*********************
  CIRCLE
**********************/


/*
    @license
    Copyright (c) 2014 The Polymer Project Authors. All rights reserved.
    This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
    The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
    The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
    Code distributed by Google as part of the polymer project is also
    subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
 */


/**************************/


/* STYLES FOR THE SPINNER */


/**************************/


/*
 * Constants:
 *      STROKEWIDTH = 3px
 *      ARCSIZE     = 270 degrees (amount of circle the arc takes up)
 *      ARCTIME     = 1333ms (time it takes to expand and contract arc)
 *      ARCSTARTROT = 216 degrees (how much the start location of the arc
 *                                should rotate each time, 216 gives us a
 *                                5 pointed star shape (it's 360/5 * 3).
 *                                For a 7 pointed star, we might do
 *                                360/7 * 3 = 154.286)
 *      CONTAINERWIDTH = 28px
 *      SHRINK_TIME = 400ms
 */

.preloader-wrapper {
  display: inline-block;
  position: relative;
  width: $progress-preloader-width;
  height: $progress-preloader-height;
  font-size: 0;
  &.small {
    width: $progress-preloader-small-width;
    height: $progress-preloader-small-height;
  }
  &.big {
    width: $progress-preloader-big-width;
    height: $progress-preloader-big-height;
  }
  &.active {
    /* duration: 360 * ARCTIME / (ARCSTARTROT + (360-ARCSIZE)) */
    animation: $progress-preloader-active-animation;
  }
  &.crazy{
    animation-duration: $progress-preloader-crazy-animation;
  }
  .spinner-layer {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    /**
         * Patch the gap that appear between the two adjacent div.circle-clipper while the
         * spinner is rotating (appears on Chrome 38, Safari 7.1, and IE 11).
         */
    .gap-patch {
      position: absolute;
      top: 0;
      left: $progress-spinner-gap-left;
      width: $progress-spinner-gap-width;
      height: 100%;
      overflow: hidden;
      border-color: inherit;
      .circle {
        width: $progress-spinner-gap-circle-width;
        left: $progress-spinner-gap-circle-left;
      }
    }

    .circle-clipper {
      display: inline-block;
      position: relative;
      width: $progress-spinner-circle-clipper-width;
      height: 100%;
      overflow: hidden;
      border-color: inherit;
      .circle {
        width: $progress-spinner-circle-clipper-circle-width;
        height: 100%;
        border-width: $progress-spinner-circle-clipper-circle-border-width;
        /* STROKEWIDTH */
        border-style: solid;
        border-color: inherit;
        border-bottom-color: transparent !important;
        border-radius: $progress-spinner-circle-clipper-circle-border-radius;
        animation: none;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
      }
      &.left .circle {
        left: 0;
        border-right-color: transparent !important;
        transform: $progress-spinner-circle-clipper-left-circle-transform;
      }
      &.right .circle {
        left: $progress-spinner-circle-clipper-right-circle-left;
        border-left-color: transparent !important;
        transform: $progress-spinner-circle-clipper-right-circle-transform;
      }
    }

  }
  .spinner-blue,
  .spinner-blue-only {
    border-color: $primary-color;
  }

  .spinner-red,
  .spinner-red-only {
    border-color: #db4437;
  }

  .spinner-yellow,
  .spinner-yellow-only {
    border-color: #f4b400;
  }

  .spinner-green,
  .spinner-green-only {
    border-color: #0f9d58;
  }
}

@include keyframes(container-rotate){
  to{
    transform: rotate(360deg);
  }
}

/**
 * IMPORTANT NOTE ABOUT CSS ANIMATION PROPERTIES (keanulee):
 *
 * iOS Safari (tested on iOS 8.1) does not handle animation-delay very well - it doesn't
 * guarantee that the animation will start _exactly_ after that value. So we avoid using
 * animation-delay and instead set custom keyframes for each color (as redundant as it
 * seems).
 *
 * We write out each animation in full (instead of separating animation-name,
 * animation-duration, etc.) because under the polyfill, Safari does not recognize those
 * specific properties properly, treats them as -webkit-animation, and overrides the
 * other animation rules. See https://github.com/Polymer/platform/issues/53.
 */

.active {
  .spinner-layer {
    &.spinner-blue {
      /* durations: 4 * ARCTIME */
      @include progress-animation-default($progress-active-spinner-main, $progress-active-spinner-blue);
    }
    &.spinner-red {
      /* durations: 4 * ARCTIME */
      @include progress-animation-default($progress-active-spinner-main, $progress-active-spinner-red);
    }
    &.spinner-yellow {
      /* durations: 4 * ARCTIME */
      @include progress-animation-default($progress-active-spinner-main, $progress-active-spinner-yellow);
    }
    &.spinner-green {
      /* durations: 4 * ARCTIME */
      @include progress-animation-default($progress-active-spinner-main, $progress-active-spinner-green);
    }
    &.spinner-blue-only,
    &.spinner-red-only,
    &.spinner-yellow-only,
    &.spinner-green-only,
    &.spinner-primary-color-only {
      /* durations: 4 * ARCTIME */
      opacity: 1;
      @include progress-animation-default($progress-active-spinner-main);
    }
  }
  .circle-clipper.left .circle {
    /* duration: ARCTIME */
    animation: $progress-active-circle-left-spin;
  }
  .circle-clipper.right .circle {
    /* duration: ARCTIME */
    animation: $progress-active-circle-right-spin;
  }
}

@include keyframes(fill-unfill-rotate){
  12.5% {
    transform: rotate(135deg);
  }
  /* 0.5 * ARCSIZE */
  25% {
    transform: rotate(270deg);
  }
  /* 1   * ARCSIZE */
  37.5% {
    transform: rotate(405deg);
  }
  /* 1.5 * ARCSIZE */
  50% {
    transform: rotate(540deg);
  }
  /* 2   * ARCSIZE */
  62.5% {
    transform: rotate(675deg);
  }
  /* 2.5 * ARCSIZE */
  75% {
    transform: rotate(810deg);
  }
  /* 3   * ARCSIZE */
  87.5% {
    transform: rotate(945deg);
  }
  /* 3.5 * ARCSIZE */
  to {
    transform: rotate(1080deg);
  }
  /* 4   * ARCSIZE */
}

@include keyframes(blue-fade-in-out) {
  from {
    opacity: 1;
  }
  25% {
    opacity: 1;
  }
  26% {
    opacity: 0;
  }
  89% {
    opacity: 0;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 1;
  }
}

@include keyframes(red-fade-in-out) {
  from {
    opacity: 0;
  }
  15% {
    opacity: 0;
  }
  25% {
    opacity: 1;
  }
  50% {
    opacity: 1;
  }
  51% {
    opacity: 0;
  }
}

@include keyframes (yellow-fade-in-out) {
  from {
    opacity: 0;
  }
  40% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  75% {
    opacity: 1;
  }
  76% {
    opacity: 0;
  }
}

@include keyframes (green-fade-in-out) {
  from {
    opacity: 0;
  }
  65% {
    opacity: 0;
  }
  75% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@include keyframes(left-spin) {
  from {
    transform: rotate(130deg);
  }
  50% {
    transform: rotate(-5deg);
  }
  to {
    transform: rotate(130deg);
  }
}

@include keyframes(right-spin) {
  from {
    transform: rotate(-130deg);
  }
  50% {
    transform: rotate(5deg);
  }
  to {
    transform: rotate(-130deg);
  }
}

#spinnerContainer.cooldown {
  /* duration: SHRINK_TIME */
  @include progress-animation-default($progress-spinner-container-animation-rotate, $progress-spinner-container-animation-fade);
}

@include keyframes(fade-out){
  from{
    opacity: 1;
  }
  to{
    opacity: 0;
  }
}

#mdb-preloader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: $black;
  /* change if the mask should have another color then white */
  z-index: 9998;
  /* makes sure it stays on top */
  height: 100%;
  width: 100%;
}
