﻿@model KobiPanel.Models.Musteriler

@{
    ViewBag.Title = "Müşteri Silme";
}



<h3>Bu Müşteriyi silmek istediğinize emin misiniz?</h3>

<div>

    <hr />
    <dl class="dl-horizontal">
        <dt>
            Adı Soyadı
        </dt>
        <dd>
            @Html.DisplayFor(model => model.adsoyad)
        </dd>
        <dt>
            Telefon Numarası
        </dt>
        <dd>
            @Html.DisplayFor(model => model.tel)
        </dd>
        <dt>
            Yaşı
        </dt>
        <dd>
            @Html.DisplayFor(model => model.yas)
        </dd>
        <dt>
            Küçükbaş Hayvan Sayısı
        </dt>
        <dd>
            @Html.DisplayFor(model => model.KucukBasHayvanSayisi)
        </dd>
        <dt>
            Büyükbaş Hayvan Sayısı
        </dt>
        <dd>
            @Html.DisplayFor(model => model.BuyukBasHayvanSayisi)
        </dd>
        <dt>
            İl
        </dt>
        <dd>
            @Html.DisplayFor(model => model.City.CityName)
        </dd>
        <dt>
            İlçe
        </dt>
        <dd>
            @Html.DisplayFor(model => model.Town.TownName)
        </dd>
        <dt>
            Mahalle
        </dt>
        <dd>
            @Html.DisplayFor(model => model.Neighborhood.NeighborhoodName)
        </dd>

    </dl>
    <h1>Müşteri silinirse müşteriye ait satış kayıtlarıda silinecektir</h1>
    @using (Html.BeginForm())
    {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="SİL" class="btn btn-danger" /> |
            <a class="btn btn-primary" href="~/Musteri/">Listeye Geri Dön</a>
        </div>
    }
</div>


