namespace KobiPanel.Models
{
    using System;
    using System.Data.Entity;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Linq;

    public class KobiPanelDbContext : DbContext
    {
        public KobiPanelDbContext()
            : base("name=KobiPanelDbContext") => Configuration.LazyLoadingEnabled = true;
        public DbSet<Araclar> Araclar { get; set; }
        public DbSet<City> City { get; set; }
        public DbSet<District> District { get; set; }
        public DbSet<Giderler> G<PERSON>ler { get; set; }
        public DbSet<Musteriler> Musteriler { get; set; }
        public DbSet<Neighborhood> Neighborhood { get; set; }
        public DbSet<Not> Not { get; set; }
        public DbSet<Satislar> Satislar { get; set; }
        public DbSet<Town> Town { get; set; }
        public DbSet<Urunler> Urunler { get; set; }

        

        
    }
}
