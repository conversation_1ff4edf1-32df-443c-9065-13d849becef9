﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using KobiPanel.Models;
using KobiPanel.ViewModels;

namespace KobiPanel.Controllers
{
    public class SatisController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();

        // GET: Satis
        public ActionResult Index()
        {
            SatisViewModel model = new SatisViewModel();
            model.Satislar = db.Satislar.ToList();
            model.Musteriler = db.Musteriler.ToList();


            return View(model);
        }

        // GET: Satis/Details/5
        public ActionResult Details(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Satislar satislar = db.Satislar.Find(id);
            if (satislar == null)
            {
                return HttpNotFound();
            }
            return View(satislar);
        }

        // GET: Satis/Create
        public ActionResult Create()
        {
    

            //var musteri =  db.Musteriler.ToList();
            ViewBag.SListMusteriler = new SelectList(db.Musteriler, "ID", "adsoyad");





            return View();
        }

        // POST: Satis/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(Satislar model)
        {

            ViewBag.SListMusteriler = new SelectList(db.Musteriler, "ID", "adsoyad",model.MusteriID);
            if (ModelState.IsValid)
            {
                model.SatisTarihi = model.SatisTarihi.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));
               db.Satislar.Add(model);
               int sonuc =  db.SaveChanges();
                if (sonuc>0)
                {
                    TempData["Success"] = "Satış bilgileri kayıt edilmiştir.";
                    return RedirectToAction("Index");
                }
                else
                {
                    ViewBag.Error = "Bir hata oluştu.";
                    return View(model);
                }
            }
            return View(model);
        }



        // GET: Satis/Edit/5
        public ActionResult Edit(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Satislar satislar = db.Satislar.Find(id);
            if (satislar == null)
            {
                return HttpNotFound();
            }
            ViewBag.MusteriID = new SelectList(db.Musteriler, "ID", "adsoyad", satislar.MusteriID);
            return View(satislar);
        }

        // POST: Satis/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Edit([Bind(Include = "SatisID,MusteriID,Tutar,ServisMi,SatisTarihi,TeslimEdildiMi,Aciklama")] Satislar satislar)
        {
            satislar.SatisTarihi = DateTime.Now;
            if (ModelState.IsValid)
            {
                db.Entry(satislar).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("Index");
            }
            ViewBag.MusteriID = new SelectList(db.Musteriler, "ID", "adsoyad", satislar.MusteriID);
            return View(satislar);
        }

        // GET: Satis/Delete/5
        public ActionResult Delete(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Satislar satislar = db.Satislar.Find(id);
            if (satislar == null)
            {
                return HttpNotFound();
            }
            return View(satislar);
        }

        // POST: Satis/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public ActionResult DeleteConfirmed(int id)
        {
            Satislar satislar = db.Satislar.Find(id);
            db.Satislar.Remove(satislar);
            db.SaveChanges();
            return RedirectToAction("Index");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
