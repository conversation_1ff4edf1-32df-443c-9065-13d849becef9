﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace KobiPanel.Models.ViewModels
{
    public class LoginViewModel
    {
        public LoginViewModel()
        {
            // Default constructor - null reference hatalarını önlemek için
            Kullanici<PERSON>di = string.Empty;
            Sifre = string.Empty;
        }

        [Required(ErrorMessage = "Kullanıcı Adı Girmediniz."),DisplayName("Kullanıcı Adı")]
        public string KullaniciAdi { get; set; }
        [Required(ErrorMessage ="Şifre Giriniz."),DisplayName("Şifre")]
        public string Sifre { get; set; }
    }
}