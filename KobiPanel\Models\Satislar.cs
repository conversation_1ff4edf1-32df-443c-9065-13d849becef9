namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Data.Entity.Spatial;

    [Table("Satislar")]
    public class Satislar
    {
        [Key,DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int SatisID { get; set; }


        [Required(AllowEmptyStrings = false, ErrorMessage = "Ürün Seçmeniz Gerekmektedir.")]
        public int UrunID { get; set; }


        [Required(AllowEmptyStrings = false, ErrorMessage = "Müşteri Seçmeniz Gerekmektedir.")]
        public int MusteriID { get; set; }

        [DisplayName("Kaç Adet?")]
        [Required(AllowEmptyStrings = false, ErrorMessage = "Ürün Adeti gereklidir.")]
        public int UrunAdeti { get; set; }

        
        public decimal Tutar { get; set; }
        
        public decimal indirim { get; set; }

        [DisplayName("Teslim Şekli")]
        public bool ServisMi { get; set; }

        public bool KrediKartiMi { get; set; }

        [DisplayName("Satış Tarihi")]
        public DateTime SatisTarihi { get; set; }

        [DisplayName("Nakliye Bedeli")]
        public int NakliyeBedeli { get; set; }

        [DisplayName("Teslim Durumu")]
        public bool TeslimEdildiMi { get; set; }

        public DateTime? TeslimTarihi { get; set; }

        [DisplayName("Açıklama")]
        public string Aciklama { get; set; }

        // ÖNEMLi NOKTA
        [ForeignKey("MusteriID")]
        public virtual Musteriler Musteriler { get; set; }

        [ForeignKey("UrunID")]
        public virtual Urun Urun { get; set; }
    }
}
