﻿@model PagedList.IPagedList<KobiPanel.Models.ViewModels.SatislarListesiDTO>
@using PagedList.Mvc;


@{
    ViewBag.PageHeader = "Satış Listesi";
    ViewBag.Title = "Satış Listesi";
}

<link href="~/Content/PagedList.css" rel="stylesheet" type="text/css" />
<div>

    @Html.ActionLink("Yeni Satış Yap", "SatisEkle", null, new { @class = "btn btn-success" })

    @Html.ActionLink("TESLİM EDİLMEYEN SATIŞLAR", "SatisAnasayfa", new { tip = 0 }, new { @class = "btn btn-danger" })

    @Html.ActionLink("TÜM SATIŞLAR", "SatisAnasayfa", null, new { @class = "btn btn-primary" })

</div>


@if (TempData["TeslimSuccess"] != null)
{
    <div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>@TempData["TeslimSuccess"]</strong>
    </div>
}

@if (TempData["DuzenleSuccess"] != null)
{
    <div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>@TempData["DuzenleSuccess"]</strong>
    </div>
}






@if (@TempData["SilindiSuccess"] != null)
{
    <div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>@TempData["SilindiSuccess"]</strong>
    </div>
}



@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>@TempData["Success"]</strong>
    </div>
}
<link href="~/Content/footable/css/footable.bootstrap.min.css" rel="stylesheet" />
@using (Html.BeginForm("SatisAnasayfa", "Satis", FormMethod.Get))
{
    <p>
        İsme Göre Arama: @Html.TextBox("SearchString", ViewBag.CurrentFilter as string)
        <input type="submit" value="Ara" />
    </p>
}
<table class="table" data-toggle-column="last">
    <thead>

        <tr>
            <th>
                @Html.ActionLink("Adı Soyadı", "SatisAnasayfa", new { sortOrder = ViewBag.NameSortParm, currentFilter = ViewBag.CurrentFilter })
            </th>
            <th>
                Teslim Şekli
            </th>
            <th>
                @Html.ActionLink("Kayıt Tarihi", "SatisAnasayfa", new { sortOrder = ViewBag.DateSortParm, currentFilter = ViewBag.CurrentFilter })
            </th>
            <th>
                Teslim Durumu
            </th>
            <th>
                Tutar
            </th>
            <th>
                İşlem
            </th>
        </tr>

    </thead>


    @foreach (var item in Model)
    {
        <tr>
            <td>

                <b>@Html.DisplayFor(modelItem => item.Musteri.adsoyad)</b> (@Html.DisplayFor(modelItem => item.Musteri.Town.TownName) - @Html.DisplayFor(modelItem => item.Musteri.Neighborhood.NeighborhoodName))
</td>
            <td>
                @if (item.ServisMi == true)
                {
                    <span class='badge badge-pill badge-default'>Adrese Teslim</span>
                }
                else
                {
                    <span class='badge badge-pill badge-light'>Yerinde Teslim</span>
                }
            </td>
            <td>
                @item.SatisTarihi
            </td>
            <td>
                @if (item.TeslimEdildiMi == true)
                {
                    <span class='badge badge-pill badge-success'>Edildi</span>
                }
                else
                {
                    <span class='badge badge-pill badge-danger'>Edilmedi</span>
                }
            </td>
            <td>
                @string.Format("{0:0,0.00}", item.Tutar) ₺

            </td>
            <td>
                <i class="fas fa-edit" style="color:blue">
                    @Html.ActionLink("Düzenle", "SatisDuzenle", new { id = item.SatisID }, new { @class = "text-primary" })
                </i>
                <i class="fas fa-trash" style="color:red">
                    @Html.ActionLink("Sil", "SatisSil", new { id = item.SatisID }, new { @class = "text-danger" })
                </i>
                <i class="fas fa-print" style="color:#4ee7f1">
                    @Html.ActionLink("Özet(Yazdır)", "SatisOzeti", new { id = item.SatisID }, new { @class = "text-primary" })
                </i>
                @if (item.TeslimEdildiMi == false)
                {
                    <i class="fas fa-truck" style="color:green">
                        @Html.ActionLink("Teslim ET", "TeslimEt", new { id = item.SatisID }, new { @class = "text-primary" })
                    </i>
                }

            </td>
        </tr>
    }

</table>


@Model.PageCount Sayfadan @(Model.PageCount < Model.PageNumber ? 0 : Model.PageNumber) sayılı sayfa

@Html.PagedListPager(Model, page => Url.Action("SatisAnasayfa",
    new { page, sortOrder = ViewBag.CurrentSort, currentFilter = ViewBag.CurrentFilter }))