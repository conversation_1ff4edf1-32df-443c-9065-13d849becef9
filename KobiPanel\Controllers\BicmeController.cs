﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using KobiPanel.Helpers;
using KobiPanel.Models;
using KobiPanel.Models.Bicme;


namespace KobiPanel.Controllers
{

    [IPAutorize]
    //TODO: Biçme Viewlarını ve actionlarını optimize edilecek.
    public class BicmeController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();

        // GET: Bicme
        public ActionResult TarlaAnasayfa()
        {
            var model = db.BicilenTarlalar.Include(b => b.<PERSON>).ToList();
            return View(model);
        }

        // GET: Bicme/Details/5
        public ActionResult TarlaDetay(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            BicilenTarlalar bicilenTarlalar = db.BicilenTarlalar.Find(id);
            if (bicilenTarlalar == null)
            {
                return HttpNotFound();
            }
            return View(bicilenTarlalar);
        }

        // GET: Bicme/Create
        public ActionResult TarlaEkle()
        {
            
            ViewBag.SListMusteriler = new SelectList(db.Musteriler, "ID", "adsoyad");
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult TarlaEkle(BicilenTarlalar bicilenTarlalar)
        {
            bicilenTarlalar.BicimTarihi = bicilenTarlalar.BicimTarihi.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));

            bicilenTarlalar.ToplamTutar = (bicilenTarlalar.Donum * bicilenTarlalar.BicmeFiyati);

            bicilenTarlalar.KalanTutar = bicilenTarlalar.ToplamTutar - bicilenTarlalar.TahsilatTutari; 

            if (ModelState.IsValid)
            {
                db.BicilenTarlalar.Add(bicilenTarlalar);
                db.SaveChanges();
                return RedirectToAction("TarlaAnasayfa");
            }

            ViewBag.MusteriID = new SelectList(db.Musteriler, "ID", "adsoyad", bicilenTarlalar.MusteriID);
            return View(bicilenTarlalar);
        }

        // GET: Bicme/Edit/5
        public ActionResult TarlaDuzenle(int? id)
        {
            
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            BicilenTarlalar bicilenTarlalar = db.BicilenTarlalar.Find(id);
            if (bicilenTarlalar == null)
            {
                return HttpNotFound();
            }
            ViewBag.MusteriID = new SelectList(db.Musteriler, "ID", "adsoyad", bicilenTarlalar.MusteriID);
            return View(bicilenTarlalar);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult TarlaDuzenle(BicilenTarlalar bicilenTarlalar)
        {
            bicilenTarlalar.BicimTarihi = bicilenTarlalar.BicimTarihi.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));
            if (ModelState.IsValid)
            {
                db.Entry(bicilenTarlalar).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("TarlaAnasayfa");
            }
            ViewBag.MusteriID = new SelectList(db.Musteriler, "ID", "adsoyad", bicilenTarlalar.MusteriID);
            return View(bicilenTarlalar);
        }

        // GET: Bicme/Delete/5
        public ActionResult TarlaSil(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            BicilenTarlalar bicilenTarlalar = db.BicilenTarlalar.Find(id);
            if (bicilenTarlalar == null)
            {
                return HttpNotFound();
            }
            return View(bicilenTarlalar);
        }

        // POST: Bicme/Delete/5
        [HttpPost, ActionName("TarlaSil")]
        [ValidateAntiForgeryToken]
        public ActionResult TarlaSilOnayli(int id)
        {
            BicilenTarlalar bicilenTarlalar = db.BicilenTarlalar.Find(id);
            db.BicilenTarlalar.Remove(bicilenTarlalar);
            db.SaveChanges();
            return RedirectToAction("TarlaAnasayfa");
        }

        public ActionResult KantarFisiAnasayfa()
        {
            var KantarFisi = db.KantarFisi.Include(k => k.BicilenTarlalar).ToList();
            

            return View();
        }

        public ActionResult KantarFisiDetay(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            KantarFisi kantarFisi = db.KantarFisi.Find(id);
            if (kantarFisi == null)
            {
                return HttpNotFound();
            }
            return View(kantarFisi);
        }

        public ActionResult KantarFisiEkle(int? id)
        {
           
           List<SelectListItem> tarlalar = new List<SelectListItem>();
           
           foreach (var item in db.BicilenTarlalar.ToList())
           {
               tarlalar.Add(new SelectListItem { Text = item.Musteri.adsoyad +" ("+item.Musteri.Town.TownName + ") " +item.Musteri.Neighborhood.NeighborhoodName +"Tarla Adı : " + item.TarlaAdi  ,
                   Value = item.TarlaID.ToString(),
                   Selected = (item.TarlaID == id ? true : false),
               });
           }
           ViewBag.TarlaID = tarlalar;
           return View();
           
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult KantarFisiEkle(KantarFisi kantarFisi)
        {
            if (ModelState.IsValid)
            {
                db.KantarFisi.Add(kantarFisi);
                db.SaveChanges();
                return RedirectToAction("KantarFisiAnasayfa");
            }
            
            return View(kantarFisi);
        }

        public ActionResult KantarFisiDuzenle(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            KantarFisi kantarFisi = db.KantarFisi.Find(id);
            if (kantarFisi == null)
            {
                return HttpNotFound();
            }
            ViewBag.TarlaID = new SelectList(db.BicilenTarlalar, "TarlaID", "aciklama", kantarFisi.TarlaID);
            return View(kantarFisi);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult KantarFisiDuzenle(KantarFisi kantarFisi)
        {
            if (ModelState.IsValid)
            {
                db.Entry(kantarFisi).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("KantarFisiAnasayfa");
            }
            ViewBag.TarlaID = new SelectList(db.BicilenTarlalar, "TarlaID", "aciklama", kantarFisi.TarlaID);
            return View(kantarFisi);
        }

        public ActionResult KantarFisiil(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            KantarFisi kantarFisi = db.KantarFisi.Find(id);
            if (kantarFisi == null)
            {
                return HttpNotFound();
            }
            return View(kantarFisi);
        }

        [HttpPost, ActionName("KantarFisiil")]
        [ValidateAntiForgeryToken]
        public ActionResult KantarFisiilOnayli(int id)
        {
            KantarFisi kantarFisi = db.KantarFisi.Find(id);
            db.KantarFisi.Remove(kantarFisi);
            db.SaveChanges();
            return RedirectToAction("KantarFisiAnasayfa");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
