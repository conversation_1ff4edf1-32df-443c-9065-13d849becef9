﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using KobiPanel.Models;

namespace KobiPanel.Controllers
{
    public class GiderController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();
        

        // GET: Gider
        public ActionResult GiderAnasayfa()
        {
            ViewBag.PageHeader = "Giderler";
            return View(db.Giderler.ToList());
        }


        // GET: Gider/Create
        public ActionResult GiderEkle()
        {
            ViewBag.PageHeader = "Giderler";
            return View();
        }

        // POST: Gider/Create
        
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult GiderEkle(Giderler giderler)
        {
            ViewBag.PageHeader = "Giderler";

            giderler.Tarih = DateTime.Now;
            if (ModelState.IsValid)
            {
                giderler.Tarih = DateTime.Now;
                db.Giderler.Add(giderler);
                db.SaveChanges();
                return RedirectToAction("GiderAnasayfa");
            }

            return View(giderler);
        }

        // GET: Gider/Edit/5
        public ActionResult GiderDuzenle(int? id)
        {
            ViewBag.PageHeader = "Giderler";

            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Giderler giderler = db.Giderler.Find(id);
            if (giderler == null)
            {
                return HttpNotFound();
            }
            return View(giderler);
        }

        
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult GiderDuzenle(Giderler giderler)
        {
            ViewBag.PageHeader = "Giderler";

            giderler.Tarih = DateTime.Now;
            if (ModelState.IsValid)
            {
                db.Entry(giderler).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("GiderAnasayfa");
            }
            return View(giderler);
        }

        // GET: Gider/Delete/5
        public ActionResult GiderSil(int? id)
        {
            ViewBag.PageHeader = "Giderler";

            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Giderler giderler = db.Giderler.Find(id);
            if (giderler == null)
            {
                return HttpNotFound();
            }
            return View(giderler);
        }

        // POST: Gider/Delete/5
        [HttpPost, ActionName("GiderSil")]
        [ValidateAntiForgeryToken]
        public ActionResult GiderSilOnayli(int id)
        {

            ViewBag.PageHeader = "Giderler";

            Giderler giderler = db.Giderler.Find(id);
            db.Giderler.Remove(giderler);
            db.SaveChanges();
            return RedirectToAction("GiderAnasayfa");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
