namespace KobiPanel.Models
{
    using System;
    using System.Data.Entity;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Linq;
    using KobiPanel.Models.Vehicles;
    using KobiPanel.Models.Bicme;
    using AspNetMvcFilters.Models;

    public class KobiPanelDbContext : DbContext
    {
        public KobiPanelDbContext()
            : base("name=KobiPanelDbContext") => Configuration.LazyLoadingEnabled = true;
        public DbSet<Araclar> Araclar { get; set; }
        public DbSet<City> City { get; set; }
        public DbSet<District> District { get; set; }
        public DbSet<Giderler> Giderler { get; set; }
        public DbSet<Musteriler> Musteriler { get; set; }
        public DbSet<Neighborhood> Neighborhood { get; set; }
        public DbSet<Not> Not { get; set; }
        public DbSet<Satislar> Satislar { get; set; }
        public DbSet<Town> Town { get; set; }
        public DbSet<Urun> Urun { get; set; }
        public DbSet<AlinanYakitlar> Yakitlar { get; set; }
        public DbSet<Kullanicilar> Kullanicilar { get; set; }
        public DbSet<BicilenTarlalar> BicilenTarlalar { get; set; }

        public DbSet<KantarFisi> KantarFisi { get; set; }

        public DbSet<Log> Log { get; set; }
    }
}
