﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Source" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="Target" xml:space="preserve">
    <value>H4sIAAAAAAAEAO1d224cu5V9H2D+odFPmcGJWrKRQY4hJZAlKxF0JAtu+2CSF4PqolqEqqs6VdWKdQbzZfOQT8ovDOvK2+atilXdDg78YvGyeNmLmyzu3dz//L9/nP7x2yaeveAsJ2lyNj85Op7PcLJKI5Ksz+a74vG3v5//8Q///m+nH6LNt9nPbbm3ZTlaM8nP5k9FsX23WOSrJ7xB+dGGrLI0Tx+Lo1W6WaAoXbw5Pv5xcXKywBRiTrFms9NPu6QgG1z9Qf+8SJMV3hY7FN+mEY7zJp3mLCvU2R3a4HyLVvhsfpM+kHuU4PioLjufnccE0X4scfw4n6EkSQtU0F6++5LjZZGlyXq5pQko/vy6xbTcI4pz3PT+HSvuOpDjN+VAFqxiC7Xa5UW68QQ8edvMzEKu3mt+593M0bn7QOe4eC1HXc3f2fw8Q6sYZfOZ3Na7izgry6mze/QzfiKrGOdHTeUfZl2RHzo+UNqU/********************************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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>