﻿using AspNetMvcFilters.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Data.Entity;
using KobiPanel.Models;
using System.Web.Security;
using System.Security.Claims;


namespace KobiPanel.Filters
{
    public class ActFilter : FilterAttribute, IActionFilter
    {
        KobiPanelDbContext db;
        public ActFilter()
        {
            db = new KobiPanelDbContext();
        }



        //Action Çalıştıktan sonra...
        public void OnActionExecuted(ActionExecutedContext filterContext)
        {
            try
            {
                if (filterContext?.HttpContext?.User?.Identity != null)
                {
                    string kullaniciAdi = string.IsNullOrEmpty(filterContext.HttpContext.User.Identity.Name)
                        ? "Misafir"
                        : filterContext.HttpContext.User.Identity.Name;

                    string actionName = filterContext.ActionDescriptor?.ActionName ?? "Bilinmeyen Action";
                    string controllerName = filterContext.ActionDescriptor?.ControllerDescriptor?.ControllerName ?? "Bilinmeyen Controller";
                    string ipAddress = filterContext.HttpContext.Request?.UserHostAddress ?? "Bilinmeyen IP";
                    string httpMethod = filterContext.HttpContext.Request?.HttpMethod ?? "Bilinmeyen Method";

                    db.Log.Add(new Log()
                    {
                        KullaniciAdi = kullaniciAdi,
                        IP = ipAddress,
                        Method = httpMethod,
                        ActionName = actionName,
                        ControllerName = controllerName,
                        Tarih = DateTime.Now,
                        Bilgi = actionName + " işlemi başarıyla tamamlandı."
                    });

                    db.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                // Log hatası durumunda sessizce devam et
                System.Diagnostics.Debug.WriteLine("ActFilter OnActionExecuted hatası: " + ex.Message);
            }
        }

        //Action Çalışmaya Başladığında...
        public void OnActionExecuting(ActionExecutingContext filterContext)
        {
            try
            {
                if (filterContext?.HttpContext?.User?.Identity != null)
                {
                    string kullaniciAdi = string.IsNullOrEmpty(filterContext.HttpContext.User.Identity.Name)
                        ? "Misafir"
                        : filterContext.HttpContext.User.Identity.Name;

                    string actionName = filterContext.ActionDescriptor?.ActionName ?? "Bilinmeyen Action";
                    string controllerName = filterContext.ActionDescriptor?.ControllerDescriptor?.ControllerName ?? "Bilinmeyen Controller";
                    string ipAddress = filterContext.HttpContext.Request?.UserHostAddress ?? "Bilinmeyen IP";
                    string httpMethod = filterContext.HttpContext.Request?.HttpMethod ?? "Bilinmeyen Method";

                    db.Log.Add(new Log()
                    {
                        KullaniciAdi = kullaniciAdi,
                        IP = ipAddress,
                        Method = httpMethod,
                        ActionName = actionName,
                        ControllerName = controllerName,
                        Tarih = DateTime.Now,
                        Bilgi = actionName + " işlemine başlandı."
                    });

                    db.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                // Log hatası durumunda sessizce devam et
                System.Diagnostics.Debug.WriteLine("ActFilter OnActionExecuting hatası: " + ex.Message);
            }
        }

    }
}