﻿using AspNetMvcFilters.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Data.Entity;
using KobiPanel.Models;
using System.Web.Security;
using System.Security.Claims;


namespace KobiPanel.Filters
{
    public class ActFilter : FilterAttribute, IActionFilter
    {
        KobiPanelDbContext db;
        public ActFilter()
        {
            db = new KobiPanelDbContext();
        }



        //Action Çalıştıktan sonra...
        public void OnActionExecuted(ActionExecutedContext filterContext)
        {
            if (filterContext.HttpContext.User.Identity.Name=="")
            {
                db.Log.Add(new Log()
                {
                    KullaniciAdi = "Misafir",
                    IP = filterContext.HttpContext.Request.UserHostAddress,
                    Method = filterContext.HttpContext.Request.HttpMethod,
                    ActionName = filterContext.ActionDescriptor.ActionName,
                    ControllerName = filterContext.ActionDescriptor.ControllerDescriptor.ControllerName,
                    Tarih = DateTime.Now,
                    Bilgi = filterContext.ActionDescriptor.ActionName + " işlemi başarıyla tamamlandı."
                });
            } else
            {
                db.Log.Add(new Log()
                {
                    KullaniciAdi = filterContext.HttpContext.User.Identity.Name,
                    IP = filterContext.HttpContext.Request.UserHostAddress,
                    Method = filterContext.HttpContext.Request.HttpMethod,
                    ActionName = filterContext.ActionDescriptor.ActionName,
                    ControllerName = filterContext.ActionDescriptor.ControllerDescriptor.ControllerName,
                    Tarih = DateTime.Now,
                    Bilgi = filterContext.ActionDescriptor.ActionName + " işlemi başarıyla tamamlandı."
                });
            }

            db.SaveChanges();
        }

        //Action Çalışmaya Başladığında...
        public void OnActionExecuting(ActionExecutingContext filterContext)
        {
            if (filterContext.HttpContext.User.Identity.Name=="")
            {
                db.Log.Add(new Log()
                {
                    KullaniciAdi = "Misafir",
                    IP = filterContext.HttpContext.Request.UserHostAddress,
                    Method = filterContext.HttpContext.Request.HttpMethod,
                    ActionName = filterContext.ActionDescriptor.ActionName,
                    ControllerName = filterContext.ActionDescriptor.ControllerDescriptor.ControllerName,
                    Tarih = DateTime.Now,
                    Bilgi = filterContext.ActionDescriptor.ActionName + " işlemine başlandı."
                });
            } else
            {
                db.Log.Add(new Log()
                {
                    KullaniciAdi = filterContext.HttpContext.User.Identity.Name,
                    IP = filterContext.HttpContext.Request.UserHostAddress,
                    Method = filterContext.HttpContext.Request.HttpMethod,
                    ActionName = filterContext.ActionDescriptor.ActionName,
                    ControllerName = filterContext.ActionDescriptor.ControllerDescriptor.ControllerName,
                    Tarih = DateTime.Now,
                    Bilgi = filterContext.ActionDescriptor.ActionName + " işlemine başlandı."
                });
            }

            db.SaveChanges();
            
        }

    }
}