﻿@model KobiPanel.Models.Urunler

@{
    ViewBag.Title = "Delete";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2><PERSON><PERSON><PERSON><PERSON><PERSON></h2>

<h3>Bu ürü<PERSON><PERSON> silmek istediğinize emin misiniz?</h3>
<div>
    <h4>Urunler</h4>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.UrunAdi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.UrunAdi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Adet)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Adet)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.AlisFiyat)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.AlisFiyat)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Kdv)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Kdv)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SatisFiyat)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SatisFiyat)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.EklenmeTarihi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.EklenmeTarihi)
        </dd>

    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Delete" class="btn btn-default" /> |
            @Html.ActionLink("Listeye Geri Dön", "Index",null, new {@class="btn btn-danger" })
        </div>
    }
</div>
