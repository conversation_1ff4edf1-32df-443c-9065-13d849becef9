namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Data.Entity.Spatial;

    [Table("Giderler")]
    public class Giderler
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int id { get; set; }

        [StringLength(30)]
        public string Konu { get; set; }

        public decimal? Tutar { get; set; }

        public DateTime Tarih { get; set; }
    }
}
