﻿@model KobiPanel.Models.ViewModels.LoginViewModel
@{
                /**/

                Layout = null;
}
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title><PERSON><PERSON><PERSON>n Silaj Uygulama Paneli Girişi</title>
    <!-- Font Awesome -->

    <link href="~/Content/fontawesome/css/all.min.css" rel="stylesheet" />
    <!-- Bootstrap core CSS -->

    <link href="~/Content/Mdb/css/bootstrap.min.css" rel="stylesheet" />
    <!-- Material Design Bootstrap -->

    <link href="~/Content/Mdb/css/mdb.min.css" rel="stylesheet" />
    <!-- Your custom styles (optional) -->
    <style>

        .intro-2 {
            background: url("/Content/Mdb/img/bgLogin.jpg")no-repeat center center;
            background-size: cover;
        }
        .top-nav-collapse {
            background-color: #3f51b5 !important;
        }
        .navbar:not(.top-nav-collapse) {
            background: transparent !important;
        }
        @@media (max-width: 768px) {
            .navbar:not(.top-nav-collapse) {
                background: #3f51b5 !important;
            }
        }

        .card {
            background-color: rgba(229, 228, 255, 0.2);
        }
        .md-form label {
            color: #ffffff;
        }
        h6 {
            line-height: 1.7;
        }

        html,
        body,
        header,
        .view {
          height: 100%;
        }

        @@media (min-width: 560px) and (max-width: 740px) {
          html,
          body,
          header,
          .view {
            height: 650px;
          }
        }

        @@media (min-width: 800px) and (max-width: 850px) {
          html,
          body,
          header,
          .view  {
            height: 650px;
          }
        }

        .card {
            margin-top: 30px;
            /*margin-bottom: -45px;*/

        }

        .md-form input[type=text]:focus:not([readonly]),
        .md-form input[type=password]:focus:not([readonly]) {
            border-bottom: 1px solid #8EDEF8;
            box-shadow: 0 1px 0 0 #8EDEF8;
        }
        .md-form input[type=text]:focus:not([readonly])+label,
        .md-form input[type=password]:focus:not([readonly])+label {
            color: #8EDEF8;
        }

        .md-form .form-control {
            color: #fff;
        }

        .navbar.navbar-dark form .md-form input:focus:not([readonly]) {
            border-color: #8EDEF8;
        }

        @@media (min-width: 800px) and (max-width: 850px) {
            .navbar:not(.top-nav-collapse) {
                background: #3f51b5!important;
            }
        }

    </style>
</head>
<body class="login-page">
    <!-- Main Navigation -->
    <header>
        <!-- Intro Section -->
        <section class="view intro-2">
            <div class="mask rgba-stylish-strong h-100 d-flex justify-content-center align-items-center">
                <div class="container">
                    <div class="row">
                        <div class="col-xl-5 col-lg-6 col-md-10 col-sm-12 mx-auto mt-5">
                            <!-- Form with header -->
                            <div class="card wow fadeIn" data-wow-delay="0.3s">
                                <div class="card-body">
                                    <!-- Header -->
                                    <div class="form-header peach-gradient">
                                        <h3 class="font-weight-500 my-2 py-1"><i class="fas fa-user"></i> Giriş Yap</h3>
                                    </div>
                                    @using (Html.BeginForm("Login", "Home", FormMethod.Post))
                                    {
                                        @Html.ValidationSummary(true, "")
                                        @Html.AntiForgeryToken()
                                        <!-- Body -->
                                        <div class="md-form">
                                            <i class="fas fa-user prefix white-text"></i>
                                            @if (Model != null)
                                            {
                                                @Html.TextBoxFor(m => m.KullaniciAdi, new { @class = "form-control" })
                                                @Html.LabelFor(m => m.KullaniciAdi)
                                            }
                                            else
                                            {
                                                <input type="text" name="KullaniciAdi" class="form-control" />
                                                <label for="KullaniciAdi">Kullanıcı Adı</label>
                                            }
                                        </div>
                                        <div class="md-form">
                                            <i class="fas fa-key prefix white-text"></i>
                                            @if (Model != null)
                                            {
                                                @Html.PasswordFor(m => m.Sifre, new { @class = "form-control" })
                                                @Html.LabelFor(m => m.Sifre)
                                            }
                                            else
                                            {
                                                <input type="password" name="Sifre" class="form-control" />
                                                <label for="Sifre">Şifre</label>
                                            }
                                        </div>
                                        <div class="text-center">
                                            <button class="btn peach-gradient btn-lg waves-effect" type="submit">Giriş Yap</button>
                                            <hr class="mt-4">

                                        </div>
                                    }

                                </div>
                            </div>
                            <!-- Form with header -->
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- Intro Section -->
    </header>
    <!-- Main Navigation -->
    <!-- SCRIPTS -->
    <!-- JQuery -->


    <script src="~/Scripts/jquery-3.4.1.min.js"></script>
    <!-- Bootstrap tooltips -->

    <script src="~/Content/Mdb/js/popper.min.js"></script>
    <!-- Bootstrap core JavaScript -->

    <script src="~/Content/Mdb/js/bootstrap.min.js"></script>
    <!-- MDB core JavaScript -->

    <script src="~/Content/Mdb/js/mdb.min.js"></script>
    <!-- Custom scripts -->
    <script>new WOW().init();</script>
</body>
</html>
