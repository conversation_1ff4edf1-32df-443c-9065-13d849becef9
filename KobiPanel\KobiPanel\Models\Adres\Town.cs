//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace KobiPanel.Models
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("Town")]
    public class Town
    {
        
        public Town()
        {
            this.District = new HashSet<District>();
            this.Musteriler = new HashSet<Musteriler>();
        }
        [Key]
        public int TownID { get; set; }
        public int CityID { get; set; }
        public string TownName { get; set; }
    
        public virtual City City { get; set; }
        
        public virtual ICollection<District> District { get; set; }
        
        public virtual ICollection<Musteriler> Musteriler { get; set; }
    }
}
