﻿@model KobiPanel.Models.Musteriler

@{
    ViewBag.Title = "Kaydet";
}

<h2><PERSON><PERSON></h2>


@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()



    <div class="form-horizontal">
        <div class="row">
            <div class="col-6">
                @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                <div class="form-group">
                    <label class="control-label col-md-6">Ad Soyad</label>
                    <div class="col-md-6">
                        @Html.EditorFor(model => model.adsoyad, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.adsoyad, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label col-md-6">Telefon Numarası</label>
                    <div class="col-md-6">
                        @Html.EditorFor(model => model.tel, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.tel, "", new { @class = "text-danger" })
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-6">Küçükbaş Hayvan Sayısı</label>
                        <div class="col-md-6">
                            @Html.EditorFor(model => model.KucukBasHayvanSayisi, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.KucukBasHayvanSayisi, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-md-6">Büyükbaş Hayvan Sayısı</label>
                        <div class="col-md-6">
                            @Html.EditorFor(model => model.BuyukBasHayvanSayisi, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.BuyukBasHayvanSayisi, "", new { @class = "text-danger" })
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-6">
                <div class="form-group">
                    <label class="control-label col-md-6">İl Seçiniz</label>
                    <div class="col-md-6">
                        @Html.DropDownList("CityID", null, htmlAttributes: new { @class = "form-control mdb-select md-form", onchange = "CityOnChange(this)", searchable = "İl Seçin..." })
                        @Html.ValidationMessageFor(model => model.CityID, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label col-md-6">İlçe Seçiniz</label>
                    <div class="col-md-6">
                        @Html.DropDownList("TownID", null, htmlAttributes: new { @class = "form-control", onchange = "TownOnChange(this)" })
                        @Html.ValidationMessageFor(model => model.TownID, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label col-md-6">Belde seçiniz</label>
                    <div class="col-md-6">
                        @Html.DropDownList("DistricID", null, htmlAttributes: new { @class = "form-control", onfocus = "DistricOnFocus(this)" })
                        @Html.ValidationMessageFor(model => model.DistricID, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label col-md-6">Mahalle / Köy Seçiniz</label>
                    <div class="col-md-6">
                        @Html.DropDownList("NeighborhoodID", null, htmlAttributes: new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.NeighborhoodID, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>

        <div>
            
        </div>
        <div class="form-group text-center">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Kaydet" class="btn btn-success btn-lg" />
                
            </div>
        </div>
    </div>
}

    <div>
        @Html.ActionLink("Listeye Geri Dön", "Index", null, new { @class = "btn btn-info" })
    </div>
<script>

    function CityOnChange(data) {
        $.ajax({
            type: 'GET',
            url: '/Musteri/GetTowns/?Id=' + data.value,
            success: function (data) {
                $("#TownID").find("option").remove();
                $.each(data, function (index, element) {
                    $("#TownID").append(new Option(element.TownName, element.TownID))
                });
                console.log(data);
            }
        });
    }
    function TownOnChange(data) {
        $.ajax({
            type: 'GET',
            url: '/Musteri/GetDistrict/?Id=' + data.value,
            success: function (data) {
                $("#DistricID").find("option").remove();
                $.each(data, function (index, element) {
                    $("#DistricID").append(new Option(element.DistrictName, element.DistrictID))
                });
                console.log(data);
            }
        });
    }
    function DistricOnFocus(data) {
        $.ajax({
            type: 'GET',
            url: '/Musteri/GetNeighborhood/?Id=' + data.value,
            success: function (data) {
                $("#NeighborhoodID").find("option").remove();
                $.each(data, function (index, element) {
                    $("#NeighborhoodID").append(new Option(element.NeighborhoodName, element.NeighborhoodID))
                });
                console.log(data);
            }
        });
    }

</script>
<script src="~/Scripts/jquery.mask.min.js"></script>

<script type="text/javascript">

    $(document).ready(function () {
        $('#tell').mask('0-(*************');
    })
</script>