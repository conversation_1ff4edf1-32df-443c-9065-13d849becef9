﻿@model KobiPanel.Models.Giderler

@{
    ViewBag.Title = "Gider Silme İşlemi";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>Gider Silme İşlemi</h2>

<h3><PERSON><PERSON><PERSON> istedi<PERSON>ine emin misin?</h3>
<div>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.Konu)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Konu)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Tutar)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Tutar)
        </dd>

    </dl>

    @using (Html.BeginForm()) {
        @Html.AntiForgeryToken()

        <div class="form-actions no-color">
            <input type="submit" value="Sil" class="btn btn-default" /> |
            @Html.ActionLink("<PERSON><PERSON>", "GiderAnasayfa")
        </div>
    }
</div>
