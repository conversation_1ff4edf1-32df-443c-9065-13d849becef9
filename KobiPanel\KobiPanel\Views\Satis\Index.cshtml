﻿@using KobiPanel.ViewModels
@model SatisViewModel


@{
    ViewBag.Title = "Index";
}

<h3 class="text-center">Satış Listesi</h3>
<p>
    @Html.ActionLink("Yeni Satış Yap", "Create", null, new { @class = "btn btn-primary" })
</p>
@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>@TempData["Success"]</strong>
    </div>
}

<table class="table">
    <tr>
        <th class="th-lg">
            Müşteri Adı
        </th>
        <th class="th-lg">
            Teslim Şekli
        </th>
        <th class="th-lg">
            Satış Tarihi
        </th>
        <th class="th-lg">
            Teslim Durumu
        </th>
        <th class="th-lg">
            A<PERSON>ıklama
        </th>
        <th class="th-lg">
            Tutar
        </th>
        <th class="th-lg"></th>
    </tr>

    @foreach (var item in Model.Satislar)
    {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Musteriler.adsoyad)
            </td>
            <td>
                @(item.ServisMi == true ? "Adrese Teslim" : "Yerinde Teslim")
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.SatisTarihi)
            </td>
            <td>
                @(item.TeslimEdildiMi == true ? "Edildi" : "Edilmedi")
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Aciklama)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Tutar)₺
            </td>
            <td>
                <i class="far fa-edit" style="color:#000000">
                    @Html.ActionLink("Düzenle", "Edit", new { id = item.SatisID }, new { @class = "text-dark" })
                </i>
                <i class="fal fa-trash-alt" style="color:red">
                    @Html.ActionLink("Sil", "Delete", new { id = item.SatisID }, new { @class = "text-danger" })
                </i>
            </td>
        </tr>
    }

</table>
