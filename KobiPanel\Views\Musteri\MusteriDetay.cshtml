﻿@model KobiPanel.Models.Musteriler

@{
    ViewBag.Title = "Müşteri Detayı";
}

<h2>Müşteri Detayları</h2>

<div>
    <hr />
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.adsoyad)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.adsoyad)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.tel)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.tel)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.yas)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.yas)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.KucukBasHayvanSayisi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.KucukBasHayvanSayisi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.BuyukBasHayvanSayisi)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.BuyukBasHayvanSayisi)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.City.CityName)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.City.CityName)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Neighborhood.NeighborhoodName)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Neighborhood.NeighborhoodName)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Town.TownName)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Town.TownName)
        </dd>

    </dl>
</div>
<p>
    @Html.ActionLink("Düzenle", "MusteriDuzenle", new { id = Model.ID }) |
    @Html.ActionLink("Listeye Geri Dön", "MusteriAnasayfa",null, new {@class = "btn btn-primary" })
</p>