﻿@using KobiPanel.Models.ViewModels
@model SatisViewModel


@{
    ViewBag.PageHeader = "Teslim Edilmeyen Satışlar";
    ViewBag.Title = "Teslim Edilmeyen Satışlar";
}
<link href="~/Content/DataTables/datatables.css" rel="stylesheet" />
<p>
    @Html.ActionLink("Yeni Satış Yap", "SatisEkle", null, new { @class = "btn btn-primary" })
</p>
@if (TempData["TeslimSuccess"] != null)
{
    <div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert">&times;</button>
        <strong>@TempData["TeslimSuccess"]</strong>
    </div>
}
<link href="~/Content/footable/css/footable.bootstrap.min.css" rel="stylesheet" />
<table class="table" data-toggle-column="last">
    <thead>
        <tr>
            <th>
                Müşteri Adı
            </th>
            <th data-title="Teslim Şekli" data-breakpoints="xs sm">
                Teslim Şekli
            </th>
            <th data-title="Satış Tarihi" data-breakpoints="xs sm">
                Satış Tarihi
            </th>
            <th data-title="Teslim Durumu" data-breakpoints="xs sm">
                Teslim Durumu
            </th>
            <th data-title="Açıklama" data-breakpoints="xs sm md">
                Açıklama
            </th>
            <th>
                Tutar
            </th>
            <th data-title=" İşlem" data-breakpoints="xs sm">
                İşlem
            </th>
        </tr>
    </thead>


    @foreach (var item in Model.Satislar)
    {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.Musteriler.adsoyad)
            </td>
            <td>
                @if (item.ServisMi == true)
                {
                    <span class='badge badge-pill badge-default'>Adrese Teslim</span>
                }
                else
                {
                    <span class='badge badge-pill badge-light'>Yerinde Teslim</span>
                }
            </td>
            <td>
                @item.SatisTarihi
            </td>
            <td>
                @if (item.TeslimEdildiMi == true)
                {
                    <span class='badge badge-pill badge-success'>Edildi</span>
                }
                else
                {
                    <span class='badge badge-pill badge-danger'>Edilmedi</span>
                }
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Aciklama)
            </td>
            <td>
                @string.Format("{0:0,0.00}", item.Tutar) ₺

            </td>
            <td>
                <i class="far fa-edit" style="color:#000000">
                    @Html.ActionLink("Teslim Et", "TeslimEt", "Satis", new { id = item.SatisID }, new { @class = "text-dark" })
                </i>
                <i class="far fa-edit" style="color:#000000">
                    @Html.ActionLink("Düzenle", "SatisDuzenle", "Satis", new { id = item.SatisID }, new { @class = "text-dark" })
                </i>
                <i class="fas fa-trash" style="color:red">
                    @Html.ActionLink("Sil", "SatisSil","Satis", new { id = item.SatisID }, new { @class = "text-danger" })
                </i>
            </td>
        </tr>
    }

</table>
<script src="~/Content/footable/js/footable.min.js"></script>

<script>
    jQuery(function($){
	$('.table').footable();
});
</script>